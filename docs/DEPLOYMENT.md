# EduGuideBot Deployment Guide

## Overview

This guide covers deploying EduGuideBot in various environments using Docker, docker-compose, and GitHub Actions CI/CD.

## Prerequisites

- Docker 20.10+
- docker-compose 2.0+
- Git
- Telegram <PERSON><PERSON> (from @BotFather)

## Environment Variables

### Required Variables

```bash
BOT_TOKEN=your_telegram_bot_token_here
```

### Optional Variables

```bash
# ML Model Version (1, 2, or 3)
ML_VERSION=3

# Metrics Collection (0 or 1)
METRICS_ENABLED=1

# Database URL (if using external database)
DATABASE_URL=postgresql://user:pass@host:port/db
```

## Local Development

### 1. Clone Repository

```bash
git clone https://github.com/your-org/eduguidebot.git
cd eduguidebot
```

### 2. Create Environment File

```bash
# Create .env file
echo "BOT_TOKEN=your_bot_token_here" > .env
echo "ML_VERSION=3" >> .env
echo "METRICS_ENABLED=1" >> .env
```

### 3. Build and Run

```bash
# Build Docker image
make docker-build

# Test with dry-run
make docker-test

# Run with real token
docker run --rm --env-file .env eduguidebot:local
```

## Staging Environment

### Using docker-compose

```bash
# Start all services (bot + metrics)
docker compose --profile staging up --build

# Run in background
docker compose --profile staging up --build -d

# View logs
docker compose logs -f bot
docker compose logs -f metrics

# Stop services
docker compose down
```

### Services Included

- **bot**: Main EduGuideBot application
- **metrics**: Prometheus metrics exporter (port 8000)

### Health Checks

```bash
# Check bot health (dry-run)
curl -f http://localhost:8000/health

# View metrics
curl http://localhost:8000/metrics
```

## Production Deployment

### 1. GitHub Actions CI/CD

The repository includes automated CI/CD pipeline:

```yaml
# .github/workflows/ci.yml
- Runs tests on every push/PR
- Builds Docker image
- Pushes to GitHub Container Registry (if GHCR_PAT secret exists)
```

### 2. Container Registry

Images are automatically published to:
```
ghcr.io/your-org/eduguidebot:latest
ghcr.io/your-org/eduguidebot:sha-abc123
```

### 3. Production Deployment

```bash
# Pull latest image
docker pull ghcr.io/your-org/eduguidebot:latest

# Run in production
docker run -d \
  --name eduguidebot \
  --restart unless-stopped \
  -e BOT_TOKEN=your_production_token \
  -e ML_VERSION=3 \
  -e METRICS_ENABLED=1 \
  -v /var/log/eduguidebot:/app/logs \
  ghcr.io/your-org/eduguidebot:latest
```

## Monitoring & Metrics

### Prometheus Integration

When `METRICS_ENABLED=1`, the bot exposes metrics at:
- `http://localhost:8000/metrics` (Prometheus format)
- `http://localhost:8000/health` (Health check)

### Available Metrics

```
# Function call counts
eduguidebot_calls_total{event="start"} 42

# Latency distribution
eduguidebot_latency_bucket{event="recommendation_latency",le="100"} 15
eduguidebot_latency_bucket{event="recommendation_latency",le="300"} 28

# Average ML scores
eduguidebot_histogram_avg{name="hybrid_score"} 0.7234
```

### Log Files

```bash
# Application logs
tail -f logs/app.log

# Metrics logs (NDJSON format)
tail -f logs/metrics.ndjson

# Error logs
tail -f logs/error.log
```

## Scaling & Performance

### Horizontal Scaling

```bash
# Run multiple bot instances
docker run -d --name eduguidebot-1 -e BOT_TOKEN=token1 eduguidebot:latest
docker run -d --name eduguidebot-2 -e BOT_TOKEN=token2 eduguidebot:latest
```

### Resource Requirements

**Minimum:**
- CPU: 0.5 cores
- Memory: 512MB
- Storage: 1GB

**Recommended:**
- CPU: 1 core
- Memory: 1GB
- Storage: 5GB

### Performance Tuning

```bash
# Optimize for production
docker run \
  --memory=1g \
  --cpus=1.0 \
  --restart=unless-stopped \
  eduguidebot:latest
```

## Security

### Container Security

```dockerfile
# Non-root user (already implemented)
USER bot

# Read-only filesystem
docker run --read-only --tmpfs /tmp eduguidebot:latest

# Security scanning
docker scan eduguidebot:latest
```

### Network Security

```bash
# Restrict network access
docker network create eduguidebot-net
docker run --network eduguidebot-net eduguidebot:latest
```

### Secrets Management

```bash
# Use Docker secrets
echo "your_bot_token" | docker secret create bot_token -
docker service create \
  --secret bot_token \
  --env BOT_TOKEN_FILE=/run/secrets/bot_token \
  eduguidebot:latest
```

## Backup & Recovery

### Data Backup

```bash
# Backup logs
tar -czf backup-$(date +%Y%m%d).tar.gz logs/

# Backup models
tar -czf models-backup.tar.gz models/
```

### Disaster Recovery

```bash
# Quick recovery
docker pull ghcr.io/your-org/eduguidebot:latest
docker run -d --env-file .env.backup eduguidebot:latest
```

## Troubleshooting

### Common Issues

**Bot not starting:**
```bash
# Check logs
docker logs eduguidebot

# Verify token
echo $BOT_TOKEN | wc -c  # Should be ~46 characters
```

**High memory usage:**
```bash
# Monitor resources
docker stats eduguidebot

# Restart if needed
docker restart eduguidebot
```

**Metrics not working:**
```bash
# Check metrics service
curl http://localhost:8000/health

# Verify environment
docker exec eduguidebot env | grep METRICS_ENABLED
```

### Debug Mode

```bash
# Run with debug logging
docker run -e LOG_LEVEL=DEBUG eduguidebot:latest

# Interactive debugging
docker run -it --entrypoint /bin/bash eduguidebot:latest
```

## Maintenance

### Updates

```bash
# Pull latest image
docker pull ghcr.io/your-org/eduguidebot:latest

# Rolling update
docker stop eduguidebot
docker rm eduguidebot
docker run -d --name eduguidebot --env-file .env ghcr.io/your-org/eduguidebot:latest
```

### Health Monitoring

```bash
# Automated health check
#!/bin/bash
if ! docker exec eduguidebot python src/bot/app.py --dry-run; then
  docker restart eduguidebot
  echo "Bot restarted at $(date)" >> /var/log/eduguidebot-health.log
fi
```

### Log Rotation

```bash
# Configure log rotation
cat > /etc/logrotate.d/eduguidebot << EOF
/var/log/eduguidebot/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

## Support

### Getting Help

- **Documentation**: Check this guide and README.md
- **Issues**: Create GitHub issue with logs and environment details
- **Community**: Join our Discord/Telegram support channel

### Reporting Issues

Include the following information:
- Docker version
- Environment variables (redacted)
- Error logs
- Steps to reproduce

---

*For additional support, contact the development team or create an issue on GitHub.*
