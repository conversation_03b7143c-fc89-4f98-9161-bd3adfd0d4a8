# EduGuideBot Telegram Error Handling System

## Overview

This document describes the comprehensive Telegram bot error handling system implemented for EduGuideBot to achieve 99.99% uptime reliability. The system eliminates common Python-Telegram-Bot (PTB) errors and provides robust error recovery mechanisms.

## Key Components

### 1. Central Error Handling Module (`src/bot/telegram_safe.py`)

The core module provides safe wrappers around common Telegram API operations:

#### Core Functions

- **`safe_answer_callback()`**: <PERSON><PERSON> callback queries within Telegram's 15-second timeout limit
- **`safe_edit_message()`**: Prevents "message is not modified" errors by comparing content before editing
- **`log_telegram_errors()`**: Decorator for consistent error logging and recovery
- **`offload_heavy_task()`**: Background task execution to prevent handler timeouts

#### Error Categories Handled

1. **Ignorable Errors** (logged as debug, not errors):
   - "message is not modified"
   - "query is too old"
   - "response timeout expired"
   - "message can't be edited"
   - "message to edit not found"

2. **Timeout Errors** (logged as warnings):
   - `TimedOut`
   - `RetryAfter`
   - `NetworkError`

3. **Serious Errors** (logged as errors with full traceback):
   - All other exceptions

### 2. Enhanced Error Handler (`src/bot/error_handler.py`)

The global error handler has been enhanced with:

- Telegram-specific error filtering
- Appropriate logging levels for different error types
- Prevention of error message flooding
- Graceful degradation for network issues

### 3. Callback Pattern Validation

#### Supported Patterns

```python
CALLBACK_PATTERNS = {
    'assessment_answers': r'^answer_[a-z_]+_[a-z0-9]+$',
    'filter_operations': r'^FILTER(_.*|S(_HOME)?)?$', 
    'navigation': r'^(BACK|NEXT|HOME|RESTART)$',
    'details': r'^(DET|SEC)_[0-9]+(_[0-9]+)?$',
    'quick_start': r'^QS_[A-Z_]+$',
    'trending': r'^TREND_[0-9]+$',
    'feedback': r'^FB_(UP|DOWN)$',
    'more_info': r'^MORE_[0-9]+$',
    'save_remove': r'^(SAVE|REMOVE)_[0-9]+$',
    'compare': r'^CMP_[0-9]+$',
    'language': r'^lang_(kh|en)$',
    'wizard': r'^WIZARD_START$'
}
```

#### Startup Validation

The system validates callback patterns at startup to prevent unhandled callback errors:

```python
def validate_callback_patterns() -> None:
    """Validate that all callback patterns are properly configured"""
    # Tests common patterns and warns about unhandled ones
```

## Usage Examples

### Basic Handler Refactoring

**Before:**
```python
async def my_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.callback_query
    await query.answer()  # Can timeout or fail
    
    # Heavy operation that might take > 15 seconds
    recommendations = generate_recommendations(user_data)
    
    await query.edit_message_text(  # Can fail with "message is not modified"
        text=new_text,
        reply_markup=keyboard
    )
```

**After:**
```python
@log_telegram_errors(logger)
async def my_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.callback_query
    await safe_answer_callback(query)  # Always succeeds
    
    # Offload heavy work to background
    offload_heavy_task(_process_recommendations(query, context, user_data))

async def _process_recommendations(query, context, user_data):
    """Background task for heavy operations"""
    recommendations = generate_recommendations(user_data)
    
    # Safe message editing
    await safe_edit_message(
        query,
        text=new_text,
        reply_markup=keyboard
    )
```

### Decorator Usage

```python
@log_telegram_errors(logger)
async def handler_function(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Function automatically handles all Telegram errors
    # Ignorable errors are swallowed
    # Serious errors are logged and user is notified
    pass
```

## Implementation Guidelines

### 1. Handler Refactoring Checklist

- [ ] Replace `await query.answer()` with `await safe_answer_callback(query)`
- [ ] Replace `await query.edit_message_text()` with `await safe_edit_message(query, ...)`
- [ ] Add `@log_telegram_errors(logger)` decorator to all callback handlers
- [ ] Move heavy operations (>5 seconds) to background tasks using `offload_heavy_task()`
- [ ] Ensure callback queries are answered within 1-2 seconds

### 2. Background Task Pattern

For operations that might take longer than 15 seconds:

```python
@log_telegram_errors(logger)
async def quick_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    await safe_answer_callback(query, "Processing...")
    
    # Kick heavy work to background
    offload_heavy_task(_heavy_operation(query, context))

async def _heavy_operation(query, context):
    # Do expensive work here
    result = expensive_computation()
    
    # Update message when done
    await safe_edit_message(query, f"Result: {result}")
```

### 3. Error Recovery Strategies

The system implements multiple layers of error recovery:

1. **Immediate Recovery**: Safe wrappers handle common errors automatically
2. **Graceful Degradation**: Serious errors are logged but don't crash the bot
3. **User Notification**: Users are informed of errors without technical details
4. **Background Processing**: Heavy operations don't block the main handler thread

## Testing

### Unit Tests

Comprehensive test suite in `tests/bot/test_telegram_safe.py`:

- Tests all safe wrapper functions
- Validates error handling for different scenarios
- Checks callback pattern validation
- Includes integration tests

### Running Tests

```bash
python -m pytest tests/bot/test_telegram_safe.py -v
```

### Dry-Run Testing

Test callback pattern validation without network calls:

```bash
python -m src.bot.app --dry-run
```

## Monitoring and Metrics

### Log Levels

- **DEBUG**: Ignorable Telegram errors (filtered out in production)
- **WARNING**: Timeout errors and non-critical BadRequest errors
- **ERROR**: Serious errors that need investigation

### Key Metrics to Monitor

1. **Callback Answer Success Rate**: Should be >99%
2. **Message Edit Success Rate**: Should be >95%
3. **Handler Response Time**: Should be <15 seconds
4. **Unhandled Callback Rate**: Should be <0.1%

## Deployment Considerations

### Production Configuration

1. Set logging level to INFO or WARNING to reduce noise
2. Monitor error rates and response times
3. Set up alerts for unusual error patterns
4. Ensure adequate server resources for background tasks

### Rollback Strategy

If issues occur:

1. The system is backward compatible with existing handlers
2. Safe wrappers can be disabled by removing decorators
3. Background task processing can be made synchronous if needed

## Future Enhancements

1. **Retry Logic**: Implement exponential backoff for timeout errors
2. **Circuit Breaker**: Temporarily disable features during high error rates
3. **Health Checks**: Periodic validation of bot functionality
4. **Advanced Metrics**: Detailed performance monitoring and alerting

## Troubleshooting

### Common Issues

1. **"Query is too old" errors**: Ensure `safe_answer_callback()` is called immediately
2. **"Message is not modified" errors**: Use `safe_edit_message()` instead of direct API calls
3. **Handler timeouts**: Move heavy operations to background tasks
4. **Unhandled callbacks**: Update callback patterns in `CALLBACK_PATTERNS`

### Debug Mode

Enable debug logging to see all ignored errors:

```python
logging.getLogger('src.bot.telegram_safe').setLevel(logging.DEBUG)
```

This comprehensive error handling system ensures EduGuideBot maintains high availability and provides a smooth user experience even under adverse network conditions or high load scenarios.
