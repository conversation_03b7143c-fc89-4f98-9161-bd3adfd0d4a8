# EduGuideBot 🎓

AI-powered university recommendation system for Cambodian students, combining Multi-Criteria Decision Analysis (MCDA) with Machine Learning re-ranking.

## Features

- **16-Question Assessment**: Comprehensive evaluation of student preferences and goals
- **Hybrid Scoring**: MCDA baseline + ML re-ranking for optimal recommendations
- **47 Universities**: Complete coverage of Cambodian higher education institutions
- **500+ Programs**: Detailed program information with employment rates and costs
- **Khmer Language**: Full localization for Cambodian users
- **Cold-Start Resilient**: Handles incomplete data gracefully

## Architecture

- **MCDA Engine**: Multi-criteria scoring with configurable weights
- **ML Re-ranker**: ExtraTrees model with 16 enriched features (87% R²)
- **Data Pipeline**: Automated audit and patching for data quality
- **Telegram Bot**: Interactive assessment and recommendation delivery

## 🚀 Deployment

### Local Testing

```bash
# Quick test
make docker-test

# Build image
make docker-build

# Run with custom token
docker run --rm -e BOT_TOKEN=your_token eduguidebot:local
```

### Docker Build & Run

```bash
# Build the image
docker build . -t eduguidebot:local

# Run in dry-run mode (no token needed)
docker run --rm eduguidebot:local

# Run with real bot token
docker run --rm -e BOT_TOKEN=your_bot_token eduguidebot:local
```

### Staging Environment

```bash
# Create .env file
echo "BOT_TOKEN=your_token" > .env
echo "ML_VERSION=3" >> .env

# Run staging
./scripts/run_staging.sh

# Or manually
docker compose --profile staging up --build
```

### Production Deployment

The bot is automatically built and pushed to GitHub Container Registry via GitHub Actions:

1. **Push to main branch** triggers CI/CD pipeline
2. **Tests run** (pytest + Docker build verification)
3. **Image pushed** to `ghcr.io/your-repo/eduguidebot:latest`
4. **Deploy** using the published image

```bash
# Pull and run production image
docker pull ghcr.io/your-repo/eduguidebot:latest
docker run --rm -e BOT_TOKEN=your_token ghcr.io/your-repo/eduguidebot:latest
```

## Development

### Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Run tests
pytest -q

# Data quality pipeline
make data-clean

# Build assets
make assets               # Khmer (default)
make assets LANG=en       # English

# ML model training
python scripts/hp_search.py
python scripts/train_ml.py --data build/synth_train.csv --model models/rf_ranker_v3.joblib
```

### Environment Variables

- `BOT_TOKEN`: Telegram bot token (required for production)
- `ML_VERSION`: Model version (1=v1, 2=v2, 3=v3, default=1)

### Testing

```bash
# All tests
pytest -q

# Specific test suites
pytest tests/test_mcda.py -v
pytest tests/test_ml_training.py -v
pytest tests/test_cold_start.py -v

# Bot dry-run
BOT_TOKEN=dry python src/bot/app.py --dry-run
```

## 📈 Metrics

EduGuideBot includes lightweight performance monitoring and metrics collection.

### Local Metrics

```bash
# Enable metrics collection
export METRICS_ENABLED=1

# Run bot with metrics
BOT_TOKEN=dry python src/bot/app.py --dry-run

# Check metrics file
cat logs/metrics.ndjson

# Clean metrics
make metrics-clean
```

### Prometheus Exporter

```bash
# Start metrics exporter
METRICS_ENABLED=1 python -m src.infra.exporter

# View metrics in Prometheus format
curl http://localhost:8000/metrics

# Health check
curl http://localhost:8000/health
```

### Docker with Metrics

```bash
# Start bot and metrics exporter
docker compose --profile staging up --build

# Access metrics endpoint
curl http://localhost:8000/metrics
```

### Tracked Metrics

- **Call Counts**: `/start` command usage
- **Latency**: Recommendation generation time
- **Histograms**: Average ML scores for recommendations
- **Events**: Recommendation delivery counts

## License

Educational project for university presentation.

## Contributing

This is a university project. For issues or suggestions, please contact the development team.
