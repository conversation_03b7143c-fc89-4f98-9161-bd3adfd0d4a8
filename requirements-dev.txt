# Development dependencies for EduGuideBot

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# Code quality
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0

# Documentation
markdownlint-cli>=0.35.0

# Development tools
ipython>=8.14.0
jupyter>=1.0.0

# Type checking (optional)
mypy>=1.4.0
types-requests>=2.31.0

# Performance testing
pytest-benchmark>=4.0.0

# Security scanning
bandit>=1.7.5
safety>=2.3.0
