"""
Lightweight Metrics Module
Provides simple event logging and performance tracking without external dependencies
"""

import json
import time
import functools
import inspect
import sys
import os
from pathlib import Path
from typing import Dict, Any, Callable, Union
from datetime import datetime, timezone


# Global metrics file path
METRICS_FILE = Path("logs/metrics.ndjson")


def log_event(event: str, data: Dict[str, Any] | None = None) -> None:
    """
    Log an event with optional data to metrics file
    
    Args:
        event: Event name/type
        data: Optional event data dictionary
    """
    # Ensure logs directory exists
    METRICS_FILE.parent.mkdir(parents=True, exist_ok=True)
    
    # Create metric entry
    metric = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "event": event,
        "data": data or {}
    }
    
    # Append to NDJSON file
    with open(METRICS_FILE, "a", encoding="utf-8") as f:
        f.write(json.dumps(metric, ensure_ascii=False) + "\n")


def histogram(name: str, value: float) -> None:
    """
    Record a histogram value
    
    Args:
        name: Histogram name
        value: Numeric value to record
    """
    log_event("histogram", {
        "name": name,
        "value": value
    })


def flush() -> None:
    """
    Flush metrics to disk/stdout
    Currently a no-op since we write immediately, but provided for API compatibility
    """
    pass


def track_latency(event_name: str):
    """
    Decorator to track function execution latency in milliseconds
    Works with both sync and async functions
    
    Args:
        event_name: Name for the latency event
    """
    def decorator(func: Callable) -> Callable:
        if inspect.iscoroutinefunction(func):
            # Async function
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.perf_counter()
                    latency_ms = (end_time - start_time) * 1000
                    log_event("latency", {
                        "event_name": event_name,
                        "latency_ms": latency_ms,
                        "function": func.__name__
                    })
            return async_wrapper
        else:
            # Sync function
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.perf_counter()
                    latency_ms = (end_time - start_time) * 1000
                    log_event("latency", {
                        "event_name": event_name,
                        "latency_ms": latency_ms,
                        "function": func.__name__
                    })
            return sync_wrapper
    return decorator


def count_calls(event_name: str):
    """
    Decorator to count function calls
    
    Args:
        event_name: Name for the call count event
    """
    def decorator(func: Callable) -> Callable:
        if inspect.iscoroutinefunction(func):
            # Async function
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                log_event("call_count", {
                    "event_name": event_name,
                    "function": func.__name__
                })
                return await func(*args, **kwargs)
            return async_wrapper
        else:
            # Sync function
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                log_event("call_count", {
                    "event_name": event_name,
                    "function": func.__name__
                })
                return func(*args, **kwargs)
            return sync_wrapper
    return decorator


# Utility functions for reading metrics
def read_metrics() -> list[dict]:
    """
    Read all metrics from the metrics file

    Returns:
        List of metric dictionaries
    """
    try:
        with open(METRICS_FILE, "r", encoding="utf-8") as f:
            metrics = []
            for line in f:
                line = line.strip()
                if line:
                    try:
                        metrics.append(json.loads(line))
                    except json.JSONDecodeError:
                        continue
            return metrics
    except (FileNotFoundError, OSError):
        return []


def clear_metrics() -> None:
    """
    Clear all metrics by removing the metrics file
    """
    if METRICS_FILE.exists():
        METRICS_FILE.unlink()


def _lazy_import_flask():
    """
    Lazy import Flask to avoid hard dependency
    """
    try:
        from flask import Flask, Response  # type: ignore
        return Flask, Response
    except ImportError:
        return None, None


def format_prometheus_metrics() -> str:
    """
    Format metrics in Prometheus format
    """
    metrics = read_metrics()
    output = []

    for metric in metrics:
        if metric.get("event") == "histogram":
            name = metric["data"]["name"]
            value = metric["data"]["value"]
            output.append(f"{name} {value}")

    return "\n".join(output)


def main() -> None:
    """
    Main function for metrics exporter
    """
    Flask, Response = _lazy_import_flask()
    if Flask is None:
        print("Flask not available – metrics exporter disabled")
        sys.exit(0)          # tests monkey-patch sys.exit – fall through
        return               # ← avoid `TypeError: NoneType is not callable`

    app = Flask(__name__)

    @app.route("/metrics")
    def _metrics() -> "Response":
        return Response(format_prometheus_metrics(), mimetype="text/plain")

    app.run(host="0.0.0.0", port=int(os.getenv("PORT", 8000)))
