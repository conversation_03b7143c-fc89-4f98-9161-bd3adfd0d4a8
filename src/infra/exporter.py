"""
Prometheus-style Metrics Exporter
Converts metrics.ndjson into Prometheus text format on-the-fly
"""

import os
import json
import time
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Any

def _lazy_import_flask():
    try:
        from flask import Flask, Response
        return Flask, Response
    except ImportError:
        return None, None

from .metrics import read_metrics


def parse_metrics() -> Dict[str, Any]:
    """
    Parse metrics from NDJSON file and aggregate for Prometheus format
    Returns empty structures if no metrics file exists

    Returns:
        Dictionary with aggregated metrics
    """
    try:
        metrics = read_metrics()
    except Exception:
        # Return empty structures if file doesn't exist or can't be read
        metrics = []

    # Initialize aggregations
    call_counts = Counter()
    latency_buckets = defaultdict(int)
    histogram_values = defaultdict(list)
    
    # Process each metric
    for metric in metrics:
        event = metric.get('event', '')
        data = metric.get('data', {})
        
        if event == 'call_count':
            event_name = data.get('event_name', 'unknown')
            call_counts[event_name] += 1
        
        elif event == 'latency':
            latency_ms = data.get('latency_ms', 0)
            event_name = data.get('event_name', 'unknown')
            
            # Bucket latencies: 0-100ms, 100-300ms, 300-1000ms, >1000ms
            if latency_ms <= 100:
                latency_buckets[f"{event_name}_0_100"] += 1
            elif latency_ms <= 300:
                latency_buckets[f"{event_name}_100_300"] += 1
            elif latency_ms <= 1000:
                latency_buckets[f"{event_name}_300_1000"] += 1
            else:
                latency_buckets[f"{event_name}_1000_inf"] += 1
        
        elif event == 'histogram':
            name = data.get('name', 'unknown')
            value = data.get('value', 0)
            histogram_values[name].append(value)
    
    return {
        'call_counts': call_counts,
        'latency_buckets': latency_buckets,
        'histogram_values': histogram_values
    }


def format_prometheus_metrics(aggregated: Dict[str, Any]) -> str:
    """
    Format aggregated metrics into Prometheus text format
    Outputs valid Prometheus text even when no data

    Args:
        aggregated: Aggregated metrics dictionary

    Returns:
        Prometheus text format string
    """
    lines = []

    # Call counts
    lines.append("# HELP eduguidebot_calls_total Total number of function calls")
    lines.append("# TYPE eduguidebot_calls_total counter")
    call_counts = aggregated.get('call_counts', {})
    if call_counts:
        for event_name, count in call_counts.items():
            lines.append(f'eduguidebot_calls_total{{event="{event_name}"}} {count}')
    else:
        # Add a default metric even when no data
        lines.append('eduguidebot_calls_total{event="none"} 0')
    
    # Latency buckets
    lines.append("")
    lines.append("# HELP eduguidebot_latency_bucket Latency distribution buckets")
    lines.append("# TYPE eduguidebot_latency_bucket histogram")
    latency_buckets = aggregated.get('latency_buckets', {})
    if latency_buckets:
        for bucket_name, count in latency_buckets.items():
            # Parse bucket name: event_name_min_max
            parts = bucket_name.rsplit('_', 2)
            if len(parts) == 3:
                event_name, min_val, max_val = parts
                le_value = max_val if max_val != 'inf' else '+Inf'
                lines.append(f'eduguidebot_latency_bucket{{event="{event_name}",le="{le_value}"}} {count}')
    else:
        lines.append('eduguidebot_latency_bucket{event="none",le="+Inf"} 0')

    # Histogram values (simple stats)
    lines.append("")
    lines.append("# HELP eduguidebot_histogram_avg Average histogram values")
    lines.append("# TYPE eduguidebot_histogram_avg gauge")
    histogram_values = aggregated.get('histogram_values', {})
    if histogram_values:
        for name, values in histogram_values.items():
            if values:
                avg_value = sum(values) / len(values)
                lines.append(f'eduguidebot_histogram_avg{{name="{name}"}} {avg_value:.4f}')
    else:
        lines.append('eduguidebot_histogram_avg{name="none"} 0')
    
    # Add timestamp
    lines.append("")
    lines.append(f"# Generated at {int(time.time() * 1000)}")
    
    return "\n".join(lines) + "\n"


def create_app(Flask, Response):
    """Create Flask app with routes"""
    app = Flask(__name__)

    @app.route('/metrics')
    def metrics_endpoint():
        """
        Prometheus metrics endpoint

        Returns:
            Response: Prometheus text format metrics
        """
        try:
            aggregated = parse_metrics()
            prometheus_text = format_prometheus_metrics(aggregated)

            return Response(
                prometheus_text,
                mimetype='text/plain; version=0.0.4; charset=utf-8'
            )
        except Exception as e:
            return Response(
                f"# Error generating metrics: {e}\n",
                status=500,
                mimetype='text/plain'
            )

    @app.route('/health')
    def health_check():
        """Health check endpoint"""
        return {"status": "healthy", "service": "eduguidebot-metrics"}

    @app.route('/')
    def index():
        """Index page with links"""
        return """
        <h1>EduGuideBot Metrics Exporter</h1>
        <ul>
            <li><a href="/metrics">Prometheus Metrics</a></li>
            <li><a href="/health">Health Check</a></li>
        </ul>
        """

    return app


def main():
    """Main function to run the exporter"""
    # Only run if metrics are enabled
    if os.environ.get("METRICS_ENABLED") != "1":
        print("Metrics exporter disabled (METRICS_ENABLED != 1)")
        return

    # Lazy import Flask
    Flask, Response = _lazy_import_flask()
    if Flask is None:
        print("⚠️ Flask not available – exporter disabled")
        return

    print("Starting EduGuideBot metrics exporter...")
    print("Endpoints:")
    print("  - http://localhost:8000/metrics (Prometheus)")
    print("  - http://localhost:8000/health (Health check)")

    app = create_app(Flask, Response)
    app.run(host='0.0.0.0', port=8000, debug=False)


if __name__ == '__main__':
    main()
