"""
UI Module for EduGuideBot
Streamlined single-screen recommendation display with inline navigation
"""

from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from typing import List, Dict, Any
from .i18n import t, get_lang

# Translation fallback system for robustness
def tr(key: str, lang: str) -> str:
    """
    Translation with fallback system

    Args:
        key: Translation key
        lang: Language code

    Returns:
        str: Translated text with fallback to English or key itself
    """
    try:
        from .i18n import I18N
        return I18N[lang].get(key, I18N['en'].get(key, key))
    except (ImportError, KeyError):
        # Fallback if I18N not available or key missing
        return t(key, lang) if hasattr(t, '__call__') else key


def create_recommendations_view(recommendations: List[Dict[str, Any]], lang: str = "kh") -> tuple[str, InlineKeyboardMarkup]:
    """
    Create enhanced single-screen view with expandable recommendation rows

    Args:
        recommendations: List of recommendation dictionaries
        lang: Language code for translations

    Returns:
        tuple: (message_text, inline_keyboard)
    """
    if not recommendations:
        return tr('no_recommendations', lang), InlineKeyboardMarkup([])

    # Build message text with all recommendations
    text_parts = [f"15/15 ✅ {tr('recommendations_title', lang)}", ""]

    for idx, rec in enumerate(recommendations, 1):
        major_name = rec.get('major_name_kh' if lang == 'kh' else 'major_name_en', 'មិនមានឈ្មោះ')
        university_name = rec.get('university_name_kh' if lang == 'kh' else 'university_name_en', 'មិនមានឈ្មោះ')
        city = rec.get('city', 'មិនមានទីតាំង')
        hybrid_score = rec.get('hybrid_score', rec.get('mcda_score', 0.0))

        text_parts.append(f"🎓 {idx}. *{major_name}*")
        text_parts.append(f"📍 {university_name}")
        text_parts.append(f"🏛️ {city} • 📊 {hybrid_score:.2f}")
        text_parts.append("")  # Empty line between recommendations

    message_text = "\n".join(text_parts)

    # Create enhanced inline keyboard with expandable rows
    keyboard_rows = []

    # Add expandable recommendation rows
    for idx, rec in enumerate(recommendations, 1):
        major_id = rec.get('major_id', f'unknown_{idx}')

        # Main action row for each recommendation
        action_row = [
            InlineKeyboardButton(
                f"ℹ️ {tr('more', lang)} #{idx}",
                callback_data=f"DET_{major_id}"
            ),
            InlineKeyboardButton(
                f"🔍 {tr('compare', lang)}",
                callback_data=f"CMP_{major_id}"
            )
        ]
        keyboard_rows.append(action_row)

    # Advanced controls row
    controls_row = [
        InlineKeyboardButton(f"🏷️ {tr('filters', lang)}", callback_data="FILTERS"),
        InlineKeyboardButton(f"🔄 {tr('refresh', lang)}", callback_data="REFRESH"),
        InlineKeyboardButton(f"↩️ {tr('restart', lang)}", callback_data="RESTART")
    ]
    keyboard_rows.append(controls_row)

    return message_text, InlineKeyboardMarkup(keyboard_rows)


def create_enhanced_recommendations_view(recommendations: List[Dict[str, Any]], lang: str = "kh") -> tuple[str, InlineKeyboardMarkup]:
    """
    Create enhanced recommendations view with rich interaction buttons for each major

    Args:
        recommendations: List of recommendation dictionaries
        lang: Language code for translations

    Returns:
        tuple: (message_text, inline_keyboard)
    """
    if not recommendations:
        return tr('no_recommendations', lang), InlineKeyboardMarkup([])

    # Build message text with all recommendations
    text_parts = [f"15/15 ✅ {tr('recommendations_title', lang)}", ""]

    for idx, rec in enumerate(recommendations, 1):
        major_name = rec.get('major_name_kh' if lang == 'kh' else 'major_name_en', 'មិនមានឈ្មោះ')
        university_name = rec.get('university_name_kh' if lang == 'kh' else 'university_name_en', 'មិនមានឈ្មោះ')
        city = rec.get('city', 'មិនមានទីតាំង')
        hybrid_score = rec.get('hybrid_score', rec.get('mcda_score', 0.0))

        text_parts.append(f"🎓 {idx}. *{major_name}*")
        text_parts.append(f"📍 {university_name}")
        text_parts.append(f"🏛️ {city} • 📊 {hybrid_score:.2f}")
        text_parts.append("")  # Empty line between recommendations

    message_text = "\n".join(text_parts)

    # Create enhanced inline keyboard with rich interaction buttons
    keyboard_rows = []

    # Add rich interaction buttons for each recommendation
    for idx, rec in enumerate(recommendations, 1):
        major_id = rec.get('major_id', f'unknown_{idx}')

        # Enhanced action buttons for each recommendation
        detail_row = [
            InlineKeyboardButton(
                f"🔍 ព័ត៌មានបន្ថែមពីអនុសាសន៍ទី {idx}" if lang == 'kh' else f"🔍 More Details #{idx}",
                callback_data=f"DET_{major_id}"
            )
        ]
        keyboard_rows.append(detail_row)

        # University and location info buttons
        info_row = [
            InlineKeyboardButton(
                "🏫 សាកលវិទ្យាល័យនេះមានមុខជំនាញផ្សេងទៀតទេ?" if lang == 'kh' else "🏫 Other Majors at University?",
                callback_data=f"UNI_{major_id}"
            )
        ]
        keyboard_rows.append(info_row)

        # Location and contact buttons
        contact_row = [
            InlineKeyboardButton(
                "📍 ទីតាំងសាកលវិទ្យាល័យ" if lang == 'kh' else "📍 University Location",
                callback_data=f"LOC_{major_id}"
            ),
            InlineKeyboardButton(
                "📞 ទំនាក់ទំនងសាកលវិទ្យាល័យ" if lang == 'kh' else "📞 University Contact",
                callback_data=f"CONTACT_{major_id}"
            )
        ]
        keyboard_rows.append(contact_row)

        # Save and compare buttons
        action_row = [
            InlineKeyboardButton(
                f"⭐ {tr('save', lang)}",
                callback_data=f"SAVE_{major_id}"
            ),
            InlineKeyboardButton(
                f"🔍 {tr('compare', lang)}",
                callback_data=f"CMP_{major_id}"
            )
        ]
        keyboard_rows.append(action_row)

    # Back to list button
    back_row = [
        InlineKeyboardButton(
            "🔙 ត្រឡប់ទៅបញ្ជីអនុសាសន៍" if lang == 'kh' else "🔙 Back to Recommendations",
            callback_data="BACK"
        )
    ]
    keyboard_rows.append(back_row)

    # Add global action buttons
    global_actions = [
        InlineKeyboardButton(f"🔄 {tr('refresh', lang)}", callback_data="REFRESH"),
        InlineKeyboardButton(f"🏠 {tr('home', lang)}", callback_data="HOME")
    ]
    keyboard_rows.append(global_actions)

    return message_text, InlineKeyboardMarkup(keyboard_rows)


def create_detail_view(programme: Dict[str, Any], lang: str = "kh",
                      recommendations: List[Dict[str, Any]] = None) -> tuple[str, InlineKeyboardMarkup]:
    """
    Create enhanced detailed view with sequential navigation

    Args:
        programme: Programme data dictionary
        lang: Language code for translations
        recommendations: Full list of recommendations for navigation

    Returns:
        tuple: (message_text, inline_keyboard)
    """
    major_name = programme.get('major_name_kh' if lang == 'kh' else 'major_name_en', 'មិនមានឈ្មោះ')
    university_name = programme.get('university_name_kh' if lang == 'kh' else 'university_name_en', 'មិនមានឈ្មោះ')
    city = programme.get('city', 'មិនមានទីតាំង')
    tuition = programme.get('tuition_fees_usd', 'N/A')
    employment_rate = programme.get('employment_rate', 'N/A')

    # Format tuition display
    tuition_display = f"{tuition} USD" if tuition != 'N/A' else 'N/A'

    # Format employment rate
    employment_display = f"{employment_rate}%" if employment_rate != 'N/A' else 'N/A'

    message_text = f"""*{major_name}*

🏛️ {tr('university', lang)}: {university_name}
📍 {tr('city', lang)}: {city}
💰 {tr('tuition', lang)}: {tuition_display}
📈 {tr('employment_rate', lang)}: {employment_display}

{programme.get('mcda_reason', tr('default_reason', lang))}"""

    # Create enhanced inline keyboard with sequential navigation
    keyboard_rows = []

    # Contact and Map buttons (if URLs available)
    contact_map_row = []
    if programme.get('contact_url'):
        contact_map_row.append(InlineKeyboardButton(
            f"📞 {tr('contact', lang)}",
            url=programme['contact_url']
        ))
    if programme.get('map_url'):
        contact_map_row.append(InlineKeyboardButton(
            f"📍 {tr('map', lang)}",
            url=programme['map_url']
        ))

    if contact_map_row:
        keyboard_rows.append(contact_map_row)

    # Sequential navigation row (if recommendations provided)
    if recommendations:
        current_major_id = programme.get('major_id')
        current_index = None

        # Find current position in recommendations
        for i, rec in enumerate(recommendations):
            if rec.get('major_id') == current_major_id:
                current_index = i
                break

        if current_index is not None:
            nav_row = []

            # Previous button (disabled if first item)
            if current_index > 0:
                prev_major_id = recommendations[current_index - 1].get('major_id')
                nav_row.append(InlineKeyboardButton(
                    f"◀ {tr('prev', lang)}",
                    callback_data=f"DET_{prev_major_id}"
                ))

            # Next button (disabled if last item)
            if current_index < len(recommendations) - 1:
                next_major_id = recommendations[current_index + 1].get('major_id')
                nav_row.append(InlineKeyboardButton(
                    f"{tr('next', lang)} ▶",
                    callback_data=f"DET_{next_major_id}"
                ))

            if nav_row:
                keyboard_rows.append(nav_row)

    # Action buttons row
    action_row = [
        InlineKeyboardButton(f"💾 {tr('save', lang)}", callback_data=f"SAVE_{programme.get('major_id')}"),
        InlineKeyboardButton(f"🗑️ {tr('remove', lang)}", callback_data=f"REMOVE_{programme.get('major_id')}")
    ]
    keyboard_rows.append(action_row)

    # Navigation buttons
    keyboard_rows.append([
        InlineKeyboardButton(f"⬅️ {tr('back_to_list', lang)}", callback_data="BACK"),
        InlineKeyboardButton(f"🔄 {tr('refresh', lang)}", callback_data="REFRESH")
    ])

    return message_text, InlineKeyboardMarkup(keyboard_rows)


def get_programme_by_id(major_id: str, cached_recommendations: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Get programme data by major_id from cached recommendations
    
    Args:
        major_id: Major ID to search for
        cached_recommendations: List of cached recommendation dictionaries
    
    Returns:
        Dict: Programme data or empty dict if not found
    """
    for rec in cached_recommendations:
        if rec.get('major_id') == major_id:
            return rec
    return {}


def create_comparison_view(programme: Dict[str, Any], top_programme: Dict[str, Any], lang: str = "kh") -> str:
    """
    Create instant comparison table between two programmes

    Args:
        programme: Selected programme to compare
        top_programme: Top-ranked programme for comparison
        lang: Language code for translations

    Returns:
        str: Formatted comparison text in Markdown
    """
    prog_name = programme.get('major_name_kh' if lang == 'kh' else 'major_name_en', 'N/A')
    top_name = top_programme.get('major_name_kh' if lang == 'kh' else 'major_name_en', 'N/A')

    comparison_text = f"""*{tr('comparison', lang)} - {prog_name} vs {top_name}*

| {tr('metric', lang)} | {prog_name[:15]}... | {top_name[:15]}... |
|---|---|---|
| {tr('tuition', lang)} | {programme.get('tuition_fees_usd', 'N/A')} USD | {top_programme.get('tuition_fees_usd', 'N/A')} USD |
| {tr('employment_rate', lang)} | {programme.get('employment_rate', 'N/A')}% | {top_programme.get('employment_rate', 'N/A')}% |
| {tr('city', lang)} | {programme.get('city', 'N/A')} | {top_programme.get('city', 'N/A')} |
| {tr('score', lang)} | {programme.get('hybrid_score', 0.0):.2f} | {top_programme.get('hybrid_score', 0.0):.2f} |"""

    return comparison_text


def create_filters_menu(lang: str = "kh") -> tuple[str, InlineKeyboardMarkup]:
    """
    Create dynamic filters menu

    Args:
        lang: Language code for translations

    Returns:
        tuple: (message_text, inline_keyboard)
    """
    message_text = f"🏷️ {tr('select_filter', lang)}"

    keyboard_rows = [
        [
            InlineKeyboardButton(f"💰 {tr('budget_filter', lang)}", callback_data="FILTER_budget"),
            InlineKeyboardButton(f"🔬 {tr('field_filter', lang)}", callback_data="FILTER_field")
        ],
        [
            InlineKeyboardButton(f"🏛️ {tr('city_filter', lang)}", callback_data="FILTER_city"),
            InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="BACK")
        ]
    ]

    return message_text, InlineKeyboardMarkup(keyboard_rows)


def apply_filter(recommendations: List[Dict[str, Any]], filter_type: str, filter_value: str) -> List[Dict[str, Any]]:
    """
    Apply dynamic filter to recommendations without re-scoring

    Args:
        recommendations: Current recommendations list
        filter_type: Type of filter (budget, field, city)
        filter_value: Filter value to apply

    Returns:
        List[Dict]: Filtered recommendations
    """
    if not recommendations:
        return recommendations

    filtered = []

    for rec in recommendations:
        include = True

        if filter_type == "budget":
            tuition = rec.get('tuition_fees_usd', '0')
            try:
                tuition_val = float(str(tuition).replace('$', '').replace(',', ''))
                if filter_value == "low" and tuition_val > 500:
                    include = False
                elif filter_value == "mid" and (tuition_val <= 500 or tuition_val > 1500):
                    include = False
                elif filter_value == "high" and tuition_val <= 1500:
                    include = False
            except (ValueError, TypeError):
                pass

        elif filter_type == "field":
            field_tag = rec.get('field_tag', '').lower()
            if filter_value == "stem" and 'stem' not in field_tag:
                include = False
            elif filter_value == "business" and 'business' not in field_tag:
                include = False
            elif filter_value == "health" and 'health' not in field_tag:
                include = False

        elif filter_type == "city":
            city = rec.get('city', '').lower()
            if filter_value == "pp" and 'ភ្នំពេញ' not in city:
                include = False
            elif filter_value == "sr" and 'សៀមរាប' not in city:
                include = False
            elif filter_value == "btb" and 'បាត់ដំបង' not in city:
                include = False

        if include:
            filtered.append(rec)

    return filtered


def render_section(programme: Dict[str, Any], section_index: int, lang: str) -> tuple[str, InlineKeyboardMarkup]:
    """
    Render specific section of programme details with navigation

    Args:
        programme: Programme data dictionary
        section_index: Section number (0-4)
        lang: Language code

    Returns:
        tuple: (message_text, inline_keyboard)
    """
    sections = [
        ("overview", _render_overview_section),
        ("fees_funding", _render_fees_section),
        ("admission", _render_admission_section),
        ("outcomes", _render_outcomes_section),
        ("contacts", _render_contacts_section)
    ]

    if not (0 <= section_index < len(sections)):
        section_index = 0

    section_name, render_func = sections[section_index]
    message_text = render_func(programme, lang)

    # Navigation buttons
    nav_buttons = []
    if section_index > 0:
        prev_callback = f"SEC_{programme.get('major_id', 'unknown')}_{section_index - 1}"
        nav_buttons.append(InlineKeyboardButton(f"◀ {tr('prev', lang)}", callback_data=prev_callback))

    if section_index < len(sections) - 1:
        next_callback = f"SEC_{programme.get('major_id', 'unknown')}_{section_index + 1}"
        nav_buttons.append(InlineKeyboardButton(f"{tr('next', lang)} ▶", callback_data=next_callback))

    keyboard_rows = []
    if nav_buttons:
        keyboard_rows.append(nav_buttons)

    # Share and feedback row (secondary actions)
    major_id = programme.get('major_id', 'unknown')
    secondary_row = [
        InlineKeyboardButton(f"📤 {tr('share', lang)}", url=f"https://t.me/teslajds1bot?start=major_{major_id}"),
        InlineKeyboardButton(f"👍 {tr('like', lang)}", callback_data="FB_UP"),
        InlineKeyboardButton(f"👎 {tr('dislike', lang)}", callback_data="FB_DOWN")
    ]
    keyboard_rows.append(secondary_row)

    # Back to recommendations button
    keyboard_rows.append([
        InlineKeyboardButton(f"⬅️ {tr('back_to_list', lang)}", callback_data="BACK")
    ])

    return message_text, InlineKeyboardMarkup(keyboard_rows)


def _render_overview_section(programme: Dict[str, Any], lang: str) -> str:
    """Render overview section of programme details"""
    major_name = programme.get('major_name_kh' if lang == 'kh' else 'major_name_en', 'N/A')
    university_name = programme.get('university_name_kh' if lang == 'kh' else 'university_name_en', 'N/A')

    text = f"""*📋 {tr('overview', lang)} - {major_name}*

🏛️ {tr('university', lang)}: {university_name}
🎓 {tr('degree_type', lang)}: {programme.get('degree_type', 'N/A')}
📚 {tr('field_tag', lang)}: {programme.get('field_tag', 'N/A')}
⏱️ {tr('duration', lang)}: {programme.get('programme_duration', 'N/A')}
📊 {tr('credits', lang)}: {programme.get('credit_count', 'N/A')}
🌐 {tr('language_instruction', lang)}: {programme.get('language_of_instruction', 'N/A')}
📅 {tr('intake_months', lang)}: {programme.get('intake_months', 'N/A')}"""

    return text


def _render_fees_section(programme: Dict[str, Any], lang: str) -> str:
    """Render fees and funding section"""
    tuition = programme.get('tuition_fees_usd', 'N/A')
    tuition_display = f"{tuition} USD" if tuition != 'N/A' else 'N/A'

    text = f"""*💰 {tr('fees_funding', lang)}*

💵 {tr('tuition', lang)}: {tuition_display}
📊 {tr('tuition_bracket', lang)}: {programme.get('tuition_bracket', 'N/A')}
🎓 {tr('scholarship_available', lang)}: {tr('yes' if programme.get('scholarship_availability') else 'no', lang)}
📝 {tr('scholarship_details', lang)}: {programme.get('scholarship_details', 'N/A')}
💸 {tr('other_costs', lang)}: {programme.get('other_costs_note', 'N/A')}"""

    return text


def _render_admission_section(programme: Dict[str, Any], lang: str) -> str:
    """Render admission requirements section"""
    text = f"""*📝 {tr('admission', lang)}*

📋 {tr('requirements', lang)}: {programme.get('admission_req', 'N/A')}
📊 {tr('entrance_exam', lang)}: {programme.get('entrance_exam', 'N/A')}
🎯 {tr('min_gpa', lang)}: {programme.get('min_gpa', 'N/A')}
📅 {tr('application_deadline', lang)}: {programme.get('application_deadline', 'N/A')}
✅ {tr('accreditation', lang)}: {programme.get('accreditation_body', 'N/A')}"""

    return text


def _render_outcomes_section(programme: Dict[str, Any], lang: str) -> str:
    """Render career outcomes section"""
    employment_rate = programme.get('employment_rate', 'N/A')
    employment_display = f"{employment_rate}%" if employment_rate != 'N/A' else 'N/A'

    salary = programme.get('median_salary_usd', 'N/A')
    salary_display = f"{salary} USD" if salary != 'N/A' else 'N/A'

    text = f"""*📈 {tr('outcomes', lang)}*

💼 {tr('employment_rate', lang)}: {employment_display}
💰 {tr('median_salary', lang)}: {salary_display}
🏢 {tr('internship_required', lang)}: {tr('yes' if programme.get('internship_required') else 'no', lang)}
🌍 {tr('global_ranking', lang)}: {programme.get('global_ranking', 'N/A')}
⭐ {tr('alumni_highlight', lang)}: {programme.get('alumni_highlight', 'N/A')}"""

    return text


def _render_contacts_section(programme: Dict[str, Any], lang: str) -> str:
    """Render contact information section"""
    text = f"""*📞 {tr('contacts', lang)}*

📱 {tr('phone', lang)}: {programme.get('contact_phone', 'N/A')}
📧 {tr('email', lang)}: {programme.get('contact_email', 'N/A')}
📘 Facebook: {programme.get('facebook_url', 'N/A')}
💬 Telegram: {programme.get('telegram_url', 'N/A')}
🌐 {tr('website', lang)}: {programme.get('website_url', 'N/A')}
📍 {tr('location', lang)}: {programme.get('map_url', 'N/A')}"""

    return text


def load_trending_majors() -> List[Dict[str, str]]:
    """Load trending majors from JSON file with graceful fallback"""
    try:
        import json
        from pathlib import Path

        trending_file = Path("data/cache/trending.json")
        if trending_file.exists():
            with open(trending_file, "r", encoding="utf-8") as f:
                trending = json.load(f)
            return trending[:10]  # Limit to 10 items maximum
    except (FileNotFoundError, json.JSONDecodeError, Exception) as e:
        # Graceful fallback for missing/corrupt file
        pass

    return []


def create_trending_carousel(lang: str = "kh") -> List[List[InlineKeyboardButton]]:
    """Generate trending majors display with graceful fallback"""
    trending_majors = load_trending_majors()

    if not trending_majors:
        return []

    # Create horizontal carousel (2 buttons per row)
    carousel_rows = []
    for i in range(0, len(trending_majors), 2):
        row = []
        for j in range(2):
            if i + j < len(trending_majors):
                major = trending_majors[i + j]
                major_id = major.get('major_id', '')
                name = major.get('name_kh' if lang == 'kh' else 'name_en', major_id)

                # Ensure callback data is under 64 bytes
                callback_data = f"TREND_{major_id}"
                if len(callback_data.encode('utf-8')) <= 64:
                    row.append(InlineKeyboardButton(
                        f"🔥 {name[:15]}..." if len(name) > 15 else f"🔥 {name}",
                        callback_data=callback_data
                    ))

        if row:
            carousel_rows.append(row)

    return carousel_rows


def inject_lang_toggle(keyboard_markup: InlineKeyboardMarkup, lang: str) -> InlineKeyboardMarkup:
    """Add language toggle to existing keyboard markup"""
    rows = list(keyboard_markup.inline_keyboard)

    # Add language toggle as last row
    lang_row = [InlineKeyboardButton(f"🌐 {tr('language_toggle', lang)}", callback_data="LANG_TOGGLE")]
    rows.append(lang_row)

    return InlineKeyboardMarkup(rows)


def create_home_screen(lang: str = "kh") -> tuple[str, InlineKeyboardMarkup]:
    """
    Create enhanced home screen with quick actions and trending carousel

    Args:
        lang: Language code for translations

    Returns:
        tuple: (message_text, inline_keyboard)
    """
    message_text = f"""🎓 *{tr('welcome_eduguide', lang)}*

{tr('home_description', lang)}

{tr('choose_action', lang)}:"""

    keyboard_rows = []

    # Quick Start row
    keyboard_rows.append([
        InlineKeyboardButton(f"⚡ {tr('quick_start', lang)}", callback_data="QS_SURPRISE"),
        InlineKeyboardButton(f"🏛️ {tr('phnom_penh_only', lang)}", callback_data="QS_PP")
    ])

    # Budget and Guide row
    keyboard_rows.append([
        InlineKeyboardButton(f"💰 {tr('low_tuition', lang)}", callback_data="QS_BUD_LOW"),
        InlineKeyboardButton(f"🧭 {tr('guide_me', lang)}", callback_data="WIZARD_START")
    ])

    # Traditional options row
    keyboard_rows.append([
        InlineKeyboardButton(f"🧮 {tr('take_quiz', lang)}", callback_data="START_QUIZ"),
        InlineKeyboardButton(f"🏷️ {tr('filter', lang)}", callback_data="FILTERS_HOME")
    ])

    # Trending majors carousel (if available)
    trending_rows = create_trending_carousel(lang)
    if trending_rows:
        # Add trending header
        message_text += f"\n\n🔥 *{tr('trending', lang)}*:"
        keyboard_rows.extend(trending_rows)

    # Management row
    keyboard_rows.append([
        InlineKeyboardButton(f"⭐ {tr('shortlist', lang)}", callback_data="SHORTLIST_VIEW"),
        InlineKeyboardButton(f"❓ {tr('help_button', lang)}", callback_data="HELP_INFO")
    ])

    # Language toggle row
    keyboard_rows.append([
        InlineKeyboardButton(f"🌐 {tr('language_toggle', lang)}", callback_data="LANG_TOGGLE")
    ])

    return message_text, InlineKeyboardMarkup(keyboard_rows)


def add_missing_translation_keys():
    """
    Helper function to identify missing translation keys for UI
    This is for development reference only
    """
    required_keys = [
        'more_info', 'refresh', 'restart', 'university', 'city', 'tuition',
        'employment_rate', 'default_reason', 'contact', 'map', 'back_to_list',
        'no_recommendations', 'more', 'compare', 'filters', 'prev', 'next',
        'save', 'remove', 'comparison', 'metric', 'score', 'select_filter',
        'budget_filter', 'field_filter', 'city_filter', 'back',
        # New keys for enhanced features
        'overview', 'fees_funding', 'admission', 'outcomes', 'contacts',
        'degree_type', 'field_tag', 'duration', 'credits', 'language_instruction',
        'intake_months', 'tuition_bracket', 'scholarship_available', 'scholarship_details',
        'other_costs', 'requirements', 'entrance_exam', 'min_gpa', 'application_deadline',
        'accreditation', 'median_salary', 'internship_required', 'global_ranking',
        'alumni_highlight', 'phone', 'email', 'website', 'location',
        'welcome_eduguide', 'home_description', 'choose_action', 'take_quiz',
        'surprise_me', 'browse_majors', 'help_button', 'language_toggle'
    ]
    return required_keys
