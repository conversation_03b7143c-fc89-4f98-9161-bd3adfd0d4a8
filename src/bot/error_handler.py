"""
Error Handler Module
Provides secure error handling that logs full details but shows generic messages to users
Enhanced with Telegram-specific error handling for 99.99% uptime reliability
"""

import logging
import traceback
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import BadRequest, TimedOut, RetryAfter, NetworkError
from .i18n import t, get_lang
from .telegram_safe import IGNORABLE_ERRORS, TIMEOUT_ERRORS

logger = logging.getLogger(__name__)


async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Global error handler for the bot with enhanced Telegram-specific error handling

    Logs full error details for debugging but only shows generic error message to users
    to prevent information disclosure. Filters out ignorable Telegram errors.

    Args:
        update: The update that caused the error
        context: The context object
    """
    error = context.error

    # Handle Telegram-specific errors with appropriate logging levels
    if isinstance(error, BadRequest):
        error_msg = str(error).lower()
        if any(ignorable in error_msg for ignorable in IGNORABLE_ERRORS):
            # These are benign errors that don't need full error logging
            logger.debug(f"Ignored benign Telegram error: {error}")
            return
        else:
            # Log as warning, not error, for non-critical BadRequest issues
            logger.warning(f"Telegram BadRequest: {error}")
    elif isinstance(error, TIMEOUT_ERRORS):
        # Timeout errors are expected in poor network conditions
        logger.warning(f"Telegram timeout error: {error}")
    else:
        # Log the full error with traceback for debugging serious issues
        logger.error(
            "Exception while handling an update:",
            exc_info=context.error
        )

        # Log additional context information for serious errors
        if update:
            logger.error(f"Update that caused error: {update}")

        # Get full traceback for logging serious errors only
        tb_list = traceback.format_exception(None, context.error, context.error.__traceback__)
        tb_string = ''.join(tb_list)
        logger.error(f"Full traceback:\n{tb_string}")

    # Only send generic error message to user for serious errors
    if isinstance(update, Update) and update.effective_message and not isinstance(error, (BadRequest, *TIMEOUT_ERRORS)):
        try:
            # Try to get user's language preference
            lang = 'kh'  # Default to Khmer
            if update.effective_user and hasattr(context, 'user_data'):
                lang = get_lang(context.user_data)

            # Send generic error message
            generic_error = t("generic_error", lang) if lang else "សូមអភ័យទោស – Something went wrong."

            await update.effective_message.reply_text(
                generic_error,
                parse_mode=None  # Don't use markdown to avoid parsing errors
            )
        except Exception as e:
            # If even the error message fails, log it but don't crash
            logger.error(f"Failed to send error message to user: {e}")


async def handle_command_error(update: Update, context: ContextTypes.DEFAULT_TYPE, 
                              command_name: str, error: Exception) -> None:
    """
    Handle errors in specific commands
    
    Args:
        update: Telegram update object
        context: Bot context
        command_name: Name of the command that failed
        error: The exception that occurred
    """
    # Log the specific command error
    logger.error(f"Error in command '{command_name}': {error}", exc_info=error)
    
    # Get user language
    lang = get_lang(context.user_data) if context.user_data else 'kh'
    
    # Send generic error message
    try:
        error_message = t("command_error", lang).format(command=command_name)
        await update.message.reply_text(
            error_message,
            parse_mode=None
        )
    except Exception as e:
        logger.error(f"Failed to send command error message: {e}")
        # Fallback to basic message
        await update.message.reply_text(
            "សូមអភ័យទោស – Something went wrong.",
            parse_mode=None
        )


def log_security_event(event_type: str, details: dict, user_id: int = None) -> None:
    """
    Log security-related events for monitoring
    
    Args:
        event_type: Type of security event (e.g., 'invalid_input', 'injection_attempt')
        details: Additional details about the event
        user_id: User ID if available
    """
    security_logger = logging.getLogger('security')
    
    log_entry = {
        'event_type': event_type,
        'user_id': user_id,
        'details': details,
        'timestamp': None  # Will be added by logging formatter
    }
    
    security_logger.warning(f"Security event: {log_entry}")
