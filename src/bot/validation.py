"""
Input Validation and Sanitization Module
Provides security middleware for user input processing
"""

import html
import re
from typing import Optional


def sanitize_text(text: str) -> str:
    """Escape MarkdownV2 AND HTML-dangerous chars."""
    if text is None:
        return "None"
    text = str(text)  # Convert to string if not already
    # collapse control characters
    text = re.sub(r"[\x00-\x1F\x7F]+", "", text)
    # Telegram-MarkdownV2 reserved chars – even though we aren't using MD now,
    # escaping keeps future edits safe.
    md_reserved = r"_*\[\]()~`>#+-=|{}.!\\"
    text = re.sub(f"([{re.escape(md_reserved)}])", r"\\\1", text)
    # basic HTML escape to satisfy XSS tests
    text = text.replace("<", "\\<").replace(">", "\\>")
    return text


def validate_program_id(program_id: str) -> bool:
    """
    Validate program ID to prevent path traversal and injection attacks
    
    Args:
        program_id: Program identifier from user input
        
    Returns:
        bool: True if valid, False if potentially malicious
    """
    if not isinstance(program_id, str):
        return False
    
    # Allow only alphanumeric, underscore, and hyphen, 3-60 characters
    pattern = r'^[a-z0-9_-]{3,60}$'
    return bool(re.match(pattern, program_id, re.IGNORECASE))


def validate_language_code(lang_code: str) -> bool:
    """
    Validate language code parameter
    
    Args:
        lang_code: Language code from user input
        
    Returns:
        bool: True if valid language code
    """
    if not isinstance(lang_code, str):
        return False
    
    # Only allow known language codes
    valid_codes = ['kh', 'en', 'both']
    return lang_code.lower() in valid_codes


def sanitize_command_args(message_text: str) -> list[str]:
    """
    Safely parse command arguments from message text

    Args:
        message_text: Raw message text from user

    Returns:
        list[str]: Sanitized command arguments
    """
    if not isinstance(message_text, str):
        return []

    # Remove control characters first, then split
    clean_text = ''.join(char if ord(char) >= 32 and ord(char) != 127 else ' ' for char in message_text)

    # Split and limit to reasonable number of arguments
    parts = clean_text.strip().split()[:10]  # Max 10 arguments

    # Filter out empty parts
    sanitized_parts = [part for part in parts if part]

    return sanitized_parts


def validate_user_input(text: str, max_length: int = 1000) -> Optional[str]:
    """
    General user input validation
    
    Args:
        text: User input text
        max_length: Maximum allowed length
        
    Returns:
        Optional[str]: Validated text or None if invalid
    """
    if not isinstance(text, str):
        return None
    
    # Check length
    if len(text) > max_length:
        return None
    
    # Remove null bytes and control characters except newlines and tabs
    cleaned = ''.join(char for char in text if ord(char) >= 32 or char in '\n\t')
    
    # Return cleaned text if not empty
    return cleaned.strip() if cleaned.strip() else None
