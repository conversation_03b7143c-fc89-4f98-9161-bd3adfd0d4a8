"""
Telegram-Safe Operations Module
Provides robust error handling for Telegram bot operations to ensure 99.99% uptime reliability.

This module implements safe wrappers around common Telegram API operations that are prone to:
- Callback query timeouts (15-second limit)
- "Message is not modified" errors
- "Query is too old" errors
- Network timeouts and retries

Key Features:
- safe_answer_callback(): Handle callback queries within timeout limits
- safe_edit_message(): Prevent duplicate message modification errors
- log_telegram_errors(): Decorator for consistent error logging
- offload_heavy_task(): Background task execution to prevent handler timeouts
"""

import asyncio
import logging
from functools import wraps
from typing import Optional, Any, Callable, Awaitable
from telegram import InlineKeyboardMarkup
from telegram.error import BadRequest, TimedOut, RetryAfter, NetworkError
from telegram.ext import ContextTypes

logger = logging.getLogger(__name__)

# Telegram errors that should be ignored (not logged as errors)
IGNORABLE_ERRORS = (
    "message is not modified",
    "query is too old", 
    "response timeout expired",
    "message can't be edited",
    "message to edit not found",
    "bad request: message is not modified",
    "bad request: query is too old"
)

# Timeout errors that should trigger retry logic
TIMEOUT_ERRORS = (TimedOut, RetryAfter, NetworkError)


async def safe_answer_callback(query, text: str = "", show_alert: bool = False, 
                             cache_time: int = 1, **kwargs) -> bool:
    """
    Safely answer a callback query within Telegram's 15-second timeout limit.
    
    This function ensures callback queries are answered immediately to prevent
    "query is too old" errors. It swallows ignorable errors and logs serious ones.
    
    Args:
        query: Telegram CallbackQuery object
        text: Optional text to show to user
        show_alert: Whether to show as alert popup
        cache_time: Cache time for the answer (default: 1 second)
        **kwargs: Additional arguments for query.answer()
        
    Returns:
        bool: True if answer was sent successfully, False if ignored/failed
    """
    try:
        await query.answer(
            text=text,
            show_alert=show_alert, 
            cache_time=cache_time,
            **kwargs
        )
        return True
        
    except BadRequest as e:
        error_msg = str(e).lower()
        if any(ignorable in error_msg for ignorable in IGNORABLE_ERRORS):
            logger.debug(f"Ignored benign callback answer error: {e}")
            return False
        else:
            logger.warning(f"BadRequest in callback answer: {e}")
            return False
            
    except TIMEOUT_ERRORS as e:
        logger.warning(f"Timeout error in callback answer: {e}")
        return False
        
    except Exception as e:
        logger.error(f"Unexpected error in callback answer: {e}", exc_info=True)
        return False


async def safe_edit_message(query, text: str, reply_markup: Optional[InlineKeyboardMarkup] = None,
                          parse_mode: Optional[str] = None, **kwargs) -> bool:
    """
    Safely edit a message, preventing "message is not modified" errors.
    
    This function compares the new content with existing content before making
    the API call, avoiding unnecessary edits that would trigger errors.
    
    Args:
        query: Telegram CallbackQuery object
        text: New message text
        reply_markup: New inline keyboard markup
        parse_mode: Parse mode for the message (Markdown, HTML, etc.)
        **kwargs: Additional arguments for edit_message_text()
        
    Returns:
        bool: True if message was edited successfully, False if skipped/failed
    """
    try:
        # Check if message content would actually change
        current_message = query.message
        if (current_message.text == text and 
            current_message.reply_markup == reply_markup):
            logger.debug("Skipping message edit - content unchanged")
            return True
            
        await query.edit_message_text(
            text=text,
            reply_markup=reply_markup,
            parse_mode=parse_mode,
            **kwargs
        )
        return True
        
    except BadRequest as e:
        error_msg = str(e).lower()
        if any(ignorable in error_msg for ignorable in IGNORABLE_ERRORS):
            logger.debug(f"Ignored benign message edit error: {e}")
            return False
        else:
            logger.warning(f"BadRequest in message edit: {e}")
            return False
            
    except TIMEOUT_ERRORS as e:
        logger.warning(f"Timeout error in message edit: {e}")
        return False
        
    except Exception as e:
        logger.error(f"Unexpected error in message edit: {e}", exc_info=True)
        return False


def log_telegram_errors(logger_instance: logging.Logger):
    """
    Decorator for consistent Telegram error logging and recovery.
    
    This decorator wraps handler functions to:
    - Log exceptions with proper context
    - Swallow ignorable Telegram errors
    - Provide graceful degradation for serious errors
    - Attempt user notification when possible
    
    Args:
        logger_instance: Logger instance to use for error logging
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
                
            except BadRequest as e:
                error_msg = str(e).lower()
                if any(ignorable in error_msg for ignorable in IGNORABLE_ERRORS):
                    logger_instance.debug(f"Ignored benign Telegram error in {func.__name__}: {e}")
                    return None
                else:
                    logger_instance.warning(f"BadRequest in {func.__name__}: {e}")
                    await _attempt_user_notification(args, "⚠️ Request failed")
                    return None
                    
            except TIMEOUT_ERRORS as e:
                logger_instance.warning(f"Timeout error in {func.__name__}: {e}")
                await _attempt_user_notification(args, "⏱️ Request timed out")
                return None
                
            except Exception as e:
                logger_instance.error(f"Unexpected error in {func.__name__}: {e}", exc_info=True)
                await _attempt_user_notification(args, "⚠️ An error occurred")
                return None
                
        return wrapper
    return decorator


async def _attempt_user_notification(args: tuple, message: str) -> None:
    """
    Attempt to notify user of error without raising exceptions.
    Send fallback Khmer error messages on failure.

    Args:
        args: Handler function arguments (should contain update)
        message: Error message to show user
    """
    try:
        update = args[0] if args else None
        if update and hasattr(update, 'callback_query') and update.callback_query:
            # Try to get user language preference
            context = args[1] if len(args) > 1 else None
            lang = 'kh'  # Default to Khmer
            if context and hasattr(context, 'user_data'):
                lang = context.user_data.get('language', context.user_data.get('lang', 'kh'))

            # Create bilingual error message
            if lang == 'kh':
                khmer_message = "❌ សូមអភ័យទោស! មានបញ្ហាបច្ចេកទេស។ សូមព្យាយាមម្តងទៀត។"
            else:
                khmer_message = "❌ Sorry! Technical issue occurred. Please try again."

            await safe_answer_callback(update.callback_query, khmer_message, show_alert=True)
    except Exception:
        # Swallow secondary failures to prevent error loops
        pass


def offload_heavy_task(coro: Awaitable[Any]) -> None:
    """
    Execute heavy operations in background to prevent handler timeouts.
    
    This function creates a fire-and-forget asyncio task for operations that
    might take longer than Telegram's 15-second handler timeout limit.
    
    Args:
        coro: Coroutine to execute in background
    """
    try:
        asyncio.create_task(coro)
    except Exception as e:
        logger.error(f"Failed to create background task: {e}", exc_info=True)


# Callback pattern validation utilities
CALLBACK_PATTERNS = {
    'assessment_answers': r'^answer_[a-z_]+_[a-z0-9]+$',
    'filter_operations': r'^FILTER(_.*|S(_HOME)?)?$',
    'filter_selections': r'^FILTER_[a-z]+$',
    'filter_applications': r'^APPLY_FILTER_[a-z]+_[a-z0-9]+$',
    'navigation': r'^(BACK|NEXT|HOME|RESTART|REFRESH)$',
    'details': r'^(DET|SEC)_[a-zA-Z0-9_]+(_[0-9]+)?$',
    'quick_start': r'^QS_[A-Z_]+$',
    'trending': r'^TREND_[a-zA-Z0-9_]+$',
    'feedback': r'^FB_(UP|DOWN)$',
    'more_info': r'^MORE_[a-zA-Z0-9_]+$',
    'save_remove': r'^(SAVE|REMOVE)_[a-zA-Z0-9_]+$',
    'compare': r'^CMP_[a-zA-Z0-9_]+$',
    'language': r'^lang_(kh|en)$',
    'wizard': r'^WIZ_[A-Z_]+$',
    'home_screen_actions': r'^(START_QUIZ|BROWSE_MAJORS|SHORTLIST_VIEW|HELP_INFO|LANG_TOGGLE)$'
}


def validate_callback_pattern(callback_data: str) -> bool:
    """
    Validate if callback data matches any known pattern.
    
    Args:
        callback_data: The callback data string to validate
        
    Returns:
        bool: True if matches a known pattern, False otherwise
    """
    import re
    
    for pattern_name, pattern in CALLBACK_PATTERNS.items():
        if re.match(pattern, callback_data):
            logger.debug(f"Callback '{callback_data}' matched pattern '{pattern_name}'")
            return True
            
    logger.warning(f"Unrecognized callback pattern: '{callback_data}'")
    return False


def validate_callback_patterns_on_startup():
    """
    Validate all callback patterns on startup to ensure they're properly configured.

    Raises:
        ValueError: If any pattern is invalid or missing
    """
    logger.info("🔍 Validating callback patterns on startup...")

    # Test patterns with sample data
    test_cases = [
        ("START_QUIZ", True),
        ("HOME", True),
        ("QS_SURPRISE", True),
        ("DET_test_major_1", True),
        ("answer_location_preference_pp", True),
        ("SEC_test_major_1_2", True),
        ("FB_UP", True),
        ("SAVE_test_major_1", True),
        ("CMP_test_major_1", True),
        ("WIZ_START", True),
        ("FILTERS_HOME", True),
        ("FILTER_budget", True),
        ("FILTER_field", True),
        ("FILTER_city", True),
        ("APPLY_FILTER_budget_low", True),
        ("APPLY_FILTER_field_stem", True),
        ("APPLY_FILTER_city_pp", True),
        ("INVALID_PATTERN", False),
    ]

    failed_tests = []

    for test_data, expected in test_cases:
        result = validate_callback_pattern(test_data)
        if result != expected:
            failed_tests.append(f"Pattern '{test_data}' expected {expected}, got {result}")

    if failed_tests:
        error_msg = "Callback pattern validation failed:\n" + "\n".join(failed_tests)
        logger.error(error_msg)
        raise ValueError(error_msg)

    logger.info(f"✅ All {len(CALLBACK_PATTERNS)} callback patterns validated successfully")
    return True
