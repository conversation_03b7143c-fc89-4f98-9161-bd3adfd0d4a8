#!/usr/bin/env python3
"""
Handler Registry System for EduGuideBot
Tracks all registered callback patterns and detects orphaned buttons
"""

import re
import logging
from typing import Dict, List, Set, Tuple, Optional, Callable
from collections import defaultdict

logger = logging.getLogger(__name__)


class HandlerRegistry:
    """Registry system for tracking bot handlers and callback patterns"""
    
    def __init__(self):
        self.registered_patterns: Dict[str, Callable] = {}
        self.pattern_regex: Dict[str, str] = {}
        self.handler_groups: Dict[str, List[str]] = defaultdict(list)
        self.orphaned_buttons: Set[str] = set()
        self.unregistered_callbacks: Set[str] = set()
        
    def register_handler(self,
                        pattern: str,
                        handler_func: Callable,
                        group: str = "general",
                        regex_pattern: Optional[str] = None) -> None:
        """
        Register a callback handler with its pattern

        Args:
            pattern: Callback pattern (e.g., "START_QUIZ", "DET_*")
            handler_func: Handler function
            group: Handler group for organization
            regex_pattern: Optional regex for pattern matching
        """
        self.registered_patterns[pattern] = handler_func
        self.handler_groups[group].append(pattern)

        if regex_pattern:
            self.pattern_regex[pattern] = regex_pattern

        # Safe logging that handles None handler functions
        handler_name = getattr(handler_func, '__name__', 'anonymous') if handler_func else 'none'
        logger.debug(f"Registered handler: {pattern} -> {handler_name}")
    
    def register_callback_patterns(self, patterns: List[str], group: str = "callbacks") -> None:
        """
        Register multiple callback patterns at once
        
        Args:
            patterns: List of callback patterns
            group: Handler group name
        """
        for pattern in patterns:
            self.register_handler(pattern, None, group)
    
    def is_pattern_registered(self, callback_data: str) -> bool:
        """
        Check if a callback pattern is registered
        
        Args:
            callback_data: Callback data to check
            
        Returns:
            bool: True if pattern is registered
        """
        # Direct match
        if callback_data in self.registered_patterns:
            return True
        
        # Regex pattern match
        for pattern, regex in self.pattern_regex.items():
            if re.match(regex, callback_data):
                return True
        
        # Wildcard pattern match
        for pattern in self.registered_patterns:
            if '*' in pattern:
                # Convert wildcard to regex
                regex_pattern = pattern.replace('*', '.*')
                if re.match(f"^{regex_pattern}$", callback_data):
                    return True
        
        return False
    
    def find_matching_handler(self, callback_data: str) -> Optional[Callable]:
        """
        Find the handler function for a callback
        
        Args:
            callback_data: Callback data
            
        Returns:
            Optional[Callable]: Handler function if found
        """
        # Direct match
        if callback_data in self.registered_patterns:
            return self.registered_patterns[callback_data]
        
        # Pattern matching
        for pattern, handler in self.registered_patterns.items():
            if '*' in pattern:
                regex_pattern = pattern.replace('*', '.*')
                if re.match(f"^{regex_pattern}$", callback_data):
                    return handler
        
        return None
    
    def add_orphaned_button(self, callback_data: str) -> None:
        """
        Mark a button as orphaned (no handler)
        
        Args:
            callback_data: Callback data of orphaned button
        """
        self.orphaned_buttons.add(callback_data)
        logger.warning(f"Orphaned button detected: {callback_data}")
    
    def add_unregistered_callback(self, callback_data: str) -> None:
        """
        Mark a callback as unregistered
        
        Args:
            callback_data: Unregistered callback data
        """
        self.unregistered_callbacks.add(callback_data)
        logger.warning(f"Unregistered callback detected: {callback_data}")
    
    def get_coverage_report(self) -> Dict[str, any]:
        """
        Generate comprehensive coverage report
        
        Returns:
            Dict: Coverage statistics and details
        """
        total_patterns = len(self.registered_patterns)
        orphaned_count = len(self.orphaned_buttons)
        unregistered_count = len(self.unregistered_callbacks)
        
        # Calculate coverage by group
        group_coverage = {}
        for group, patterns in self.handler_groups.items():
            group_total = len(patterns)
            group_orphaned = len([p for p in patterns if p in self.orphaned_buttons])
            group_coverage[group] = {
                "total": group_total,
                "orphaned": group_orphaned,
                "coverage": ((group_total - group_orphaned) / group_total * 100) if group_total > 0 else 0
            }
        
        overall_coverage = ((total_patterns - orphaned_count) / total_patterns * 100) if total_patterns > 0 else 0
        
        return {
            "overall_coverage": overall_coverage,
            "total_patterns": total_patterns,
            "orphaned_buttons": orphaned_count,
            "unregistered_callbacks": unregistered_count,
            "group_coverage": group_coverage,
            "orphaned_list": list(self.orphaned_buttons),
            "unregistered_list": list(self.unregistered_callbacks),
            "registered_patterns": list(self.registered_patterns.keys())
        }
    
    def validate_all_patterns(self) -> List[str]:
        """
        Validate all registered patterns for common issues
        
        Returns:
            List[str]: List of validation warnings
        """
        warnings = []
        
        # Check for duplicate patterns
        pattern_counts = defaultdict(int)
        for pattern in self.registered_patterns:
            pattern_counts[pattern] += 1
        
        duplicates = [p for p, count in pattern_counts.items() if count > 1]
        if duplicates:
            warnings.append(f"Duplicate patterns detected: {duplicates}")
        
        # Check for conflicting wildcard patterns
        wildcard_patterns = [p for p in self.registered_patterns if '*' in p]
        for i, pattern1 in enumerate(wildcard_patterns):
            for pattern2 in wildcard_patterns[i+1:]:
                if self._patterns_conflict(pattern1, pattern2):
                    warnings.append(f"Conflicting patterns: {pattern1} and {pattern2}")
        
        # Check for orphaned buttons
        if self.orphaned_buttons:
            warnings.append(f"Orphaned buttons found: {len(self.orphaned_buttons)}")
        
        # Check for unregistered callbacks
        if self.unregistered_callbacks:
            warnings.append(f"Unregistered callbacks found: {len(self.unregistered_callbacks)}")
        
        return warnings
    
    def _patterns_conflict(self, pattern1: str, pattern2: str) -> bool:
        """Check if two wildcard patterns might conflict"""
        # Simple conflict detection - can be enhanced
        regex1 = pattern1.replace('*', '.*')
        regex2 = pattern2.replace('*', '.*')
        
        # Test with some common callback formats
        test_cases = [
            "DET_123", "CMP_456", "SAVE_789", "REMOVE_ABC",
            "SEC_123_1", "UNI_456", "LOC_789", "CONTACT_ABC"
        ]
        
        for test_case in test_cases:
            if re.match(f"^{regex1}$", test_case) and re.match(f"^{regex2}$", test_case):
                return True
        
        return False
    
    def get_handler_statistics(self) -> Dict[str, any]:
        """
        Get detailed handler statistics
        
        Returns:
            Dict: Handler statistics
        """
        stats = {
            "total_handlers": len(self.registered_patterns),
            "handlers_by_group": {group: len(patterns) for group, patterns in self.handler_groups.items()},
            "wildcard_patterns": len([p for p in self.registered_patterns if '*' in p]),
            "exact_patterns": len([p for p in self.registered_patterns if '*' not in p]),
            "orphaned_buttons": len(self.orphaned_buttons),
            "unregistered_callbacks": len(self.unregistered_callbacks)
        }
        
        return stats
    
    def export_patterns(self) -> Dict[str, any]:
        """
        Export all patterns for external analysis
        
        Returns:
            Dict: All pattern data
        """
        return {
            "registered_patterns": list(self.registered_patterns.keys()),
            "pattern_regex": self.pattern_regex,
            "handler_groups": dict(self.handler_groups),
            "orphaned_buttons": list(self.orphaned_buttons),
            "unregistered_callbacks": list(self.unregistered_callbacks)
        }


# Global registry instance
handler_registry = HandlerRegistry()


def register_handler(pattern: str, handler_func: Callable, group: str = "general") -> None:
    """Convenience function for registering handlers"""
    handler_registry.register_handler(pattern, handler_func, group)


def register_callback_patterns(patterns: List[str], group: str = "callbacks") -> None:
    """Convenience function for registering callback patterns"""
    handler_registry.register_callback_patterns(patterns, group)


def is_callback_registered(callback_data: str) -> bool:
    """Convenience function for checking if callback is registered"""
    return handler_registry.is_pattern_registered(callback_data)


def get_handler_coverage() -> Dict[str, any]:
    """Convenience function for getting coverage report"""
    return handler_registry.get_coverage_report()


def initialize_default_patterns():
    """Initialize default EduGuideBot callback patterns"""
    
    # Home screen patterns
    home_patterns = [
        "START_QUIZ", "HOME", "QS_SURPRISE", "QS_PP", "QS_SR", "QS_BB",
        "BROWSE_MAJORS", "SHORTLIST_VIEW", "HELP_INFO", "LANG_TOGGLE"
    ]
    register_callback_patterns(home_patterns, "home_screen")
    
    # Assessment patterns
    assessment_patterns = [
        "answer_*", "NEXT_QUESTION", "PREV_QUESTION", "SKIP_QUESTION"
    ]
    register_callback_patterns(assessment_patterns, "assessment")
    
    # Recommendation patterns
    recommendation_patterns = [
        "DET_*", "CMP_*", "SAVE_*", "REMOVE_*", "MORE_*", "TREND_*",
        "SEC_*", "UNI_*", "LOC_*", "CONTACT_*"
    ]
    register_callback_patterns(recommendation_patterns, "recommendations")
    
    # Navigation patterns
    navigation_patterns = [
        "BACK", "REFRESH", "RESTART", "FILTERS_HOME", "HOME"
    ]
    register_callback_patterns(navigation_patterns, "navigation")
    
    # Feedback patterns
    feedback_patterns = [
        "FB_UP", "FB_DOWN", "WIZ_*"
    ]
    register_callback_patterns(feedback_patterns, "feedback")


if __name__ == "__main__":
    # Example usage
    initialize_default_patterns()
    
    # Test pattern registration
    print("Handler Registry Test")
    print("=" * 40)
    
    # Check some patterns
    test_callbacks = [
        "START_QUIZ", "DET_123", "answer_location_pp", "INVALID_CALLBACK"
    ]
    
    for callback in test_callbacks:
        is_registered = is_callback_registered(callback)
        print(f"{callback}: {'✅ Registered' if is_registered else '❌ Not registered'}")
    
    # Get coverage report
    coverage = get_handler_coverage()
    print(f"\nCoverage Report:")
    print(f"Total patterns: {coverage['total_patterns']}")
    print(f"Overall coverage: {coverage['overall_coverage']:.1f}%")
