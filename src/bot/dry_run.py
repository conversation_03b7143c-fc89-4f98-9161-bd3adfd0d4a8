#!/usr/bin/env python3
"""
Dry-run harness for EduGuideBot
Tests core functionality without Telegram network calls
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

def main():
    print("🧪 EduGuideBot Dry-run Test")
    print("=" * 40)

    # Test 1: Import modules
    try:
        from src.bot.keyboards import ASSESSMENT_QUESTIONS
        print(f"✅ Questions loaded: {len(ASSESSMENT_QUESTIONS)}")
    except Exception as e:
        print(f"❌ Failed to load questions: {e}")
        return

    # Test 2: Create sample answers (pick first option for each question)
    answers = {q["id"]: q["options"][0][0] for q in ASSESSMENT_QUESTIONS}
    print(f"✅ Sample answers created: {len(answers)} questions")

    # Test 3: Load data modules
    try:
        from src.core.data_loader import load_raw
        from src.core.feature_engineering import add_derived_features
        from src.core.mcda import score
        print("✅ Core modules imported")
    except Exception as e:
        print(f"❌ Failed to import core modules: {e}")
        return

    # Test 4: Load and enhance data
    try:
        print("📊 Loading program data...")
        data = add_derived_features(load_raw())
        print(f"✅ Programs loaded: {len(data)}")
    except Exception as e:
        print(f"❌ Failed to load data: {e}")
        return

    # Test 5: Calculate scores for sample programs
    try:
        print("🎯 Calculating MCDA scores...")
        scores = []
        test_programs = data[:20]  # Test first 20 programs for speed

        for p in test_programs:
            program_score = score(answers, p)
            scores.append((
                p.get("university_name_kh", "Unknown"),
                p.get("major_name_kh", "Unknown"),
                program_score
            ))

        # Sort by score and get top 5
        scores.sort(key=lambda x: x[2], reverse=True)
        top_5 = scores[:5]

        print("\n🏆 TOP-5 RECOMMENDATIONS:")
        print("-" * 60)
        for i, (uni, major, score_val) in enumerate(top_5, 1):
            print(f"{i}. {uni}")
            print(f"   📚 {major}")
            print(f"   📊 Score: {score_val:.3f}")
            print()

        # Test 6: Verify score variation
        all_scores = [s[2] for s in scores]
        min_score = min(all_scores)
        max_score = max(all_scores)
        score_range = max_score - min_score

        print(f"📈 Score Analysis:")
        print(f"   Min: {min_score:.3f}")
        print(f"   Max: {max_score:.3f}")
        print(f"   Range: {score_range:.3f}")

        if score_range > 0.1:
            print("✅ Good score variation detected")
        else:
            print("⚠️  Low score variation - check MCDA weights")

        print("\n🎉 Dry-run completed successfully!")

    except Exception as e:
        print(f"❌ Failed during scoring: {e}")
        return

if __name__ == "__main__":
    main()
