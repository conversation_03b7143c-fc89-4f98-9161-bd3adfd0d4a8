"""
Telegram Bot Handlers Module
Manages conversation flow and user interactions
"""

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, ConversationHandler
from typing import Dict, Any
import logging
import sys
from pathlib import Path

# Add project root to path for metrics import
sys.path.append(str(Path(__file__).parents[2]))
from src.infra.metrics import count_calls, track_latency, log_event
from .i18n import t
from .ui import tr
from .telegram_safe import safe_answer_callback, safe_edit_message, log_telegram_errors, offload_heavy_task
from .utils import (
    cache_recommendations,
    cache_user_answers,
    format_recommendation_card,
    log_recommendation_metrics
)

# Conversation states
ASKING_QUESTIONS = 1
SHOWING_RESULTS = 2

logger = logging.getLogger(__name__)


def get_lang(user_data: dict) -> str:
    """
    Get language from user_data with fallback to default

    Args:
        user_data: User data dictionary

    Returns:
        str: Language code ('en' or 'kh')
    """
    # Check for 'language' key first (used by handlers)
    if 'language' in user_data:
        lang = user_data['language']
        if lang in ['en', 'kh']:
            return lang

    # Check for 'lang' key (used by i18n system)
    if 'lang' in user_data:
        lang = user_data['lang']
        if lang in ['en', 'kh']:
            return lang

    # Default to Khmer
    return 'kh'


def get_default_user_settings() -> dict:
    """
    Get default user settings

    Returns:
        dict: Default user settings with all filter options
    """
    return {
        "low_cost_only": False,
        "phnom_penh_only": False,
        "language": "kh"
    }


# STEP 1: Language Selection
@count_calls("start")
async def start_assessment(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    STEP 1: Language Selection - Clean 4-step bot flow

    Args:
        update: Telegram update object
        context: Bot context

    Returns:
        int: Next conversation state
    """
    # Removed deep-link handling for MVP - no social sharing features

    # STEP 1: Show language selection
    await show_language_selection(update, context)
    return ConversationHandler.END


async def show_language_selection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    STEP 1: Show language selection screen

    Args:
        update: Telegram update object
        context: Bot context
    """
    welcome_text = """🎓 **Welcome to EduGuideBot!**

Please select your preferred language:
សូមជ្រើសរើសភាសាដែលអ្នកចង់ប្រើ៖"""

    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("🇰🇭 ភាសាខ្មែរ", callback_data="LANG_KH"),
            InlineKeyboardButton("🇺🇸 English", callback_data="LANG_EN")
        ]
    ])

    if update.callback_query:
        await safe_edit_message(
            update.callback_query,
            text=welcome_text,
            reply_markup=keyboard
        )
    else:
        await update.message.reply_text(
            text=welcome_text,
            reply_markup=keyboard
        )


# Language Selection Handlers
@log_telegram_errors(logger)
async def handle_language_selection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle language selection and proceed to STEP 2: Quiz

    Args:
        update: Telegram update object
        context: Bot context
    """
    query = update.callback_query
    await safe_answer_callback(query)

    # Extract language from callback data
    if query.data == "LANG_KH":
        context.user_data['language'] = 'kh'
        context.user_data['lang'] = 'kh'
    elif query.data == "LANG_EN":
        context.user_data['language'] = 'en'
        context.user_data['lang'] = 'en'
    else:
        # Default to Khmer
        context.user_data['language'] = 'kh'
        context.user_data['lang'] = 'kh'

    # Proceed to STEP 2: Start Quiz
    await start_quiz_callback(update, context)


# STEP 2: 15-Question Quiz
async def start_quiz_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    STEP 2: Start the 15-question assessment quiz

    Args:
        update: Telegram update object
        context: Bot context

    Returns:
        int: Next conversation state
    """
    from .keyboards import get_question_by_index, create_question_keyboard, create_progress_text

    # Initialize user session
    context.user_data['answers'] = {}
    context.user_data['current_question'] = 0

    # Get user language preference
    lang = get_lang(context.user_data)

    # Get first question
    question = get_question_by_index(0)
    keyboard = create_question_keyboard(question, lang)
    progress = create_progress_text(1, lang)

    # Send first question
    message = f"{t('assessment_title', lang)}\n\n{progress}\n\n{t(question['question_key'], lang)}"

    if update.callback_query:
        await safe_edit_message(
            update.callback_query,
            text=message,
            reply_markup=keyboard
        )
    else:
        await update.message.reply_text(
            text=message,
            reply_markup=keyboard
        )

    return ASKING_QUESTIONS


# STEP 4: Detail View
async def show_major_details(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    STEP 4: Show detailed program info with contact options and back button

    Args:
        update: Telegram update object
        context: Bot context
    """
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # Extract major_id from callback data: "DET_major_id"
    callback_data = query.data
    if not callback_data.startswith("DET_"):
        await safe_edit_message(query, text="❌ Invalid callback data")
        return

    major_id = callback_data[4:]  # Remove "DET_" prefix

    # Get programme from cached recommendations
    cached_recs = context.user_data.get("last_recs", {})
    programme = cached_recs.get(major_id)

    if not programme:
        await safe_edit_message(query, text=f"❌ Program {major_id} not found in cache")
        return

    # Create detailed view with full program info
    message_text = create_detailed_program_view(programme, lang)
    reply_markup = create_detail_view_keyboard(programme, lang)

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


def create_detailed_program_view(programme: Dict[str, Any], lang: str) -> str:
    """
    Create detailed program information view

    Args:
        programme: Programme data dictionary
        lang: Language code

    Returns:
        str: Formatted program details
    """
    major_name = programme.get('major_name_kh' if lang == 'kh' else 'major_name_en', 'មិនមានឈ្មោះ')
    university_name = programme.get('university_name_kh' if lang == 'kh' else 'university_name_en', 'មិនមានឈ្មោះ')
    city = programme.get('city', 'មិនមានទីតាំង')

    # Get program details
    duration = programme.get('programme_duration', 'N/A')
    tuition = programme.get('tuition_fees_usd', 'N/A')
    employment_rate = programme.get('employment_rate', 'N/A')
    field_tag = programme.get('field_tag', 'N/A')
    degree_type = programme.get('degree_type', 'N/A')
    language_instruction = programme.get('language_of_instruction', 'N/A')

    # Format tuition display
    tuition_display = f"${tuition} USD" if tuition != 'N/A' else 'N/A'
    employment_display = f"{employment_rate}%" if employment_rate != 'N/A' else 'N/A'

    # Check for scholarships and internships
    scholarship_available = "✅ Available" if programme.get('scholarship_available', False) else "❌ Not Available"
    internship_available = "✅ Available" if programme.get('internship_available', False) else "❌ Not Available"

    if lang == 'kh':
        message_text = f"""🎓 **{major_name}**

🏛️ **សាកលវិទ្យាល័យ:** {university_name}
📍 **ទីតាំង:** {city}
⏱️ **រយៈពេលសិក្សា:** {duration}
💰 **ថ្លៃសិក្សា:** {tuition_display}
🎯 **ប្រភេទសញ្ញាបត្រ:** {degree_type}
🔬 **វិស័យសិក្សា:** {field_tag}
🌐 **ភាសាបង្រៀន:** {language_instruction}
📈 **អត្រាការងារ:** {employment_display}

💡 **ការផ្តល់អាហារូបករណ៍:** {scholarship_available}
🏢 **កម្មសិក្សា:** {internship_available}

📋 **ការពិពណ៌នាអាជីព:**
{programme.get('career_prospects', 'ព័ត៌មានលម្អិតអំពីអាជីពនឹងត្រូវបានបន្ថែមនាពេលខាងមុខ។')}

📞 **ព័ត៌មានទំនាក់ទំនង:**
{programme.get('contact_info', 'សូមទាក់ទងសាកលវិទ្យាល័យដោយផ្ទាល់។')}"""
    else:
        message_text = f"""🎓 **{major_name}**

🏛️ **University:** {university_name}
📍 **Location:** {city}
⏱️ **Duration:** {duration}
💰 **Tuition Fee:** {tuition_display}
🎯 **Degree Type:** {degree_type}
🔬 **Field of Study:** {field_tag}
🌐 **Language of Instruction:** {language_instruction}
📈 **Employment Rate:** {employment_display}

💡 **Scholarship Availability:** {scholarship_available}
🏢 **Internship Availability:** {internship_available}

📋 **Career Prospects:**
{programme.get('career_prospects', 'Detailed career information will be added soon.')}

📞 **Contact Information:**
{programme.get('contact_info', 'Please contact the university directly.')}"""

    return message_text


def create_detail_view_keyboard(programme: Dict[str, Any], lang: str) -> InlineKeyboardMarkup:
    """
    Create keyboard for detail view with contact and navigation buttons

    Args:
        programme: Programme data dictionary
        lang: Language code

    Returns:
        InlineKeyboardMarkup: Keyboard with contact and navigation buttons
    """
    keyboard_rows = []

    # Contact buttons row
    contact_row = []
    if programme.get('contact_url'):
        contact_text = "📞 ទំនាក់ទំនង" if lang == 'kh' else "📞 Contact"
        contact_row.append(InlineKeyboardButton(contact_text, url=programme['contact_url']))

    if programme.get('website_url'):
        website_text = "🌐 គេហទំព័រ" if lang == 'kh' else "🌐 Website"
        contact_row.append(InlineKeyboardButton(website_text, url=programme['website_url']))

    if contact_row:
        keyboard_rows.append(contact_row)

    # Back to list button
    back_text = "🔙 ត្រឡប់ទៅបញ្ជី" if lang == 'kh' else "🔙 Back to List"
    keyboard_rows.append([
        InlineKeyboardButton(back_text, callback_data="BACK")
    ])

    return InlineKeyboardMarkup(keyboard_rows)


@log_telegram_errors(logger)
async def handle_back_to_list(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle back to list navigation from detail view

    Args:
        update: Telegram update object
        context: Bot context
    """
    query = update.callback_query
    await safe_answer_callback(query)

    # Get cached recommendations
    cached_recs = context.user_data.get("last_recs", {})
    if not cached_recs:
        lang = get_lang(context.user_data)
        await safe_edit_message(query, text="❌ No recommendations found. Please start a new assessment.")
        return

    # Convert cached recommendations back to list format
    recommendations = list(cached_recs.values())

    # Show recommendations again
    lang = get_lang(context.user_data)
    from .ui import create_enhanced_recommendations_view
    message_text, reply_markup = create_enhanced_recommendations_view(recommendations, lang)

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


async def show_major_detail(update: Update, context: ContextTypes.DEFAULT_TYPE, major_id: str, section: int = 0) -> None:
    """
    Show detailed view of a specific major (for deep-links) - Legacy function

    Args:
        update: Telegram update object
        context: Bot context
        major_id: Major ID to display
        section: Section index to show
    """
    try:
        # Load programme data
        from src.core.data_loader import load_raw
        programmes = load_raw()

        # Find the programme
        programme = None
        for prog in programmes:
            if prog.get('major_id') == major_id:
                programme = prog
                break

        if not programme:
            lang = get_lang(context.user_data)
            await update.message.reply_text(f"❌ {tr('programme_not_found', lang)}")
            return

        # Show detail view
        lang = get_lang(context.user_data)
        from .ui import render_section
        message_text, reply_markup = render_section(programme, section, lang)

        await update.message.reply_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error showing major detail: {e}")
        lang = get_lang(context.user_data)
        await update.message.reply_text(f"❌ {tr('error_loading_programme', lang)}")


@log_telegram_errors(logger)
async def handle_question_answer(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    Handle user's answer to assessment question

    Args:
        update: Telegram update object
        context: Bot context

    Returns:
        int: Next conversation state
    """
    from .keyboards import get_question_by_index, create_question_keyboard, create_progress_text, ACTIVE_QUESTIONS

    query = update.callback_query
    await safe_answer_callback(query)

    # Parse callback data: "answer_question_id_option_value"
    callback_data = query.data
    if not callback_data.startswith("answer_"):
        return ASKING_QUESTIONS

    # Remove "answer_" prefix and split on last underscore to preserve compound question IDs
    # Example: answer_location_preference_pp -> question_id="location_preference", option_value="pp"
    remaining = callback_data[7:]  # Remove "answer_"
    parts = remaining.rsplit("_", 1)  # Split from right to preserve compound question IDs

    if len(parts) < 2:
        return ASKING_QUESTIONS

    question_id = parts[0]  # e.g., "location_preference"
    option_value = parts[1]  # e.g., "pp"

    # Initialize answers dict if it doesn't exist
    if 'answers' not in context.user_data:
        context.user_data['answers'] = {}

    # Store answer
    context.user_data['answers'][question_id] = option_value

    # Initialize current_question if it doesn't exist
    if 'current_question' not in context.user_data:
        context.user_data['current_question'] = 0

    # Move to next question
    current_index = context.user_data['current_question']
    next_index = current_index + 1

    if next_index >= len(ACTIVE_QUESTIONS):
        # All questions answered, show results
        return await show_recommendations(update, context)

    # Show next question
    context.user_data['current_question'] = next_index
    lang = get_lang(context.user_data)
    question = get_question_by_index(next_index)
    keyboard = create_question_keyboard(question, lang)
    progress = create_progress_text(next_index + 1, lang)

    message = f"{t('assessment_title', lang)}\n\n{progress}\n\n{t(question['question_key'], lang)}"

    await safe_edit_message(
        query,
        text=message,
        reply_markup=keyboard
    )

    return ASKING_QUESTIONS


# STEP 3: Top 5 Recommendations
@track_latency("recommendation_latency")
async def show_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    STEP 3: Show top 5 recommendations with clickable actions

    Args:
        update: Telegram update object
        context: Bot context

    Returns:
        int: Next conversation state
    """
    query = update.callback_query
    user_answers = context.user_data.get('answers', {})
    lang = get_lang(context.user_data)

    # Log recommendation request
    log_event("recommendation_request", {
        "user_id": update.effective_user.id,
        "answers_count": len(user_answers)
    })

    try:
        # Validate user answers before processing
        if not user_answers:
            error_text = "❌ សូមអភ័យទោស! មិនមានចម្លើយសម្រាប់ការវិភាគ។ សូមចាប់ផ្តើមការវាយតម្លៃម្តងទៀត។"
            await _send_error_message(update, error_text)
            return ConversationHandler.END

        # Get hybrid recommendations with comprehensive error handling
        from src.core.hybrid_recommender import get_recommendations
        recommendations = []

        try:
            recommendations = get_recommendations(user_answers, top_k=5)
        except ImportError as e:
            logger.error(f"Data loading error in recommendations: {e}")
            error_text = "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការផ្ទុកទិន្នន័យ។ សូមព្យាយាមម្តងទៀត។"
            await _send_error_message(update, error_text)
            return ConversationHandler.END
        except Exception as e:
            logger.error(f"MCDA/ML scoring error: {e}")
            error_text = "❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញអនុសាសន៍។ សូមព្យាយាមម្តងទៀត។"
            await _send_error_message(update, error_text)
            return ConversationHandler.END

        # Validate recommendations
        if not recommendations:
            error_text = "❌ សូមអភ័យទោស! រកមិនឃើញកម្មវិធីសិក្សាដែលសមស្រប។ សូមព្យាយាមម្តងទៀត។"
            await _send_error_message(update, error_text)
            return ConversationHandler.END

        # Use enhanced UI for recommendations display
        from .ui import create_enhanced_recommendations_view
        message_text, reply_markup = create_enhanced_recommendations_view(recommendations, lang)

        if query:
            await safe_edit_message(
                query,
                text=message_text,
                reply_markup=reply_markup,
                parse_mode="Markdown"
            )
        else:
            await update.message.reply_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode="Markdown"
            )

        # Cache recommendations and user answers using utility functions
        cache_recommendations(recommendations, context)
        cache_user_answers(user_answers, context)

        # Log recommendation metrics
        log_recommendation_metrics(recommendations)
        log_event("recommendation_sent", {"count": len(recommendations)})

    except Exception as e:
        logger.error(f"Critical error in show_recommendations: {e}", exc_info=True)
        error_text = "❌ សូមអភ័យទោស! មានបញ្ហាធ្ងន់ធ្ងរ។ សូមទាក់ទងអ្នកគ្រប់គ្រង។"
        await _send_error_message(update, error_text)

    # Clear user data to prevent memory leaks (but preserve cache for details)
    cache_recs = context.user_data.get("last_recs", {})
    cache_answers = context.user_data.get("last_answers", {})
    context.user_data.clear()
    context.user_data["last_recs"] = cache_recs
    context.user_data["last_answers"] = cache_answers

    return ConversationHandler.END


async def _send_error_message(update: Update, error_text: str) -> None:
    """
    Helper function to send error messages safely

    Args:
        update: Telegram update object
        error_text: Error message to send
    """
    query = update.callback_query

    try:
        if query:
            await safe_edit_message(query, text=error_text)
        else:
            await update.message.reply_text(text=error_text)
    except Exception as e:
        logger.error(f"Failed to send error message: {e}")
        # Last resort - try to send a basic message
        try:
            if query:
                await query.message.reply_text("❌ មានបញ្ហាក្នុងការដំណើរការ។")
            else:
                await update.message.reply_text("❌ មានបញ្ហាក្នុងការដំណើរការ។")
        except:
            pass  # Give up gracefully


async def display_recommendations_list(update: Update, context: ContextTypes.DEFAULT_TYPE, recommendations: list, query=None) -> None:
    """
    Display a list of recommendations with inline buttons

    Args:
        update: Telegram update object
        context: Bot context
        recommendations: List of recommendation dictionaries
        query: Optional callback query for editing messages
    """
    lang = get_lang(context)

    # Send title message
    title_text = t('recommendations_title', lang)

    if query:
        await safe_edit_message(query, text=title_text)
    else:
        await update.message.reply_text(text=title_text)

    # Send each recommendation as a separate message
    for i, program in enumerate(recommendations, 1):
        card_text = format_recommendation_card(program, i)

        # Create inline keyboard for each recommendation
        keyboard = [
            [
                InlineKeyboardButton(
                    t('button_back_to_list', lang),
                    callback_data=f"back_to_list"
                ),
                InlineKeyboardButton(
                    t('button_why_recommended', lang),
                    callback_data=f"explain_rec:{program.get('major_id', 'unknown')}"
                )
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        if query:
            await context.bot.send_message(
                chat_id=query.message.chat_id,
                text=card_text.strip(),
                reply_markup=reply_markup
            )
        else:
            await update.message.reply_text(
                text=card_text.strip(),
                reply_markup=reply_markup
            )


async def handle_details_request(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handle request for detailed program information

    Args:
        update: Telegram update object
        context: Bot context
    """
    # This will be implemented in future versions
    lang = get_lang(context.user_data)
    await update.message.reply_text(
        t("details_coming_soon", lang)
    )


def create_conversation_handler() -> ConversationHandler:
    """
    Create the main conversation handler

    Returns:
        ConversationHandler: Configured conversation handler
    """
    from telegram.ext import CommandHandler, CallbackQueryHandler, MessageHandler, filters

    return ConversationHandler(
        entry_points=[
            CommandHandler('recommend', start_assessment),
            CommandHandler('start', start_assessment)
        ],
        states={
            ASKING_QUESTIONS: [
                CallbackQueryHandler(handle_question_answer, pattern=r'^answer_')
            ],
            SHOWING_RESULTS: [
                MessageHandler(filters.TEXT, handle_details_request)
            ]
        },
        fallbacks=[
            CommandHandler('cancel', cancel_conversation),
            CommandHandler('start', start_assessment)
        ]
    )


async def cancel_conversation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Cancel the current conversation"""
    lang = get_lang(context.user_data)
    await update.message.reply_text(
        t("cancel_message", lang)
    )

    # Clear user data to prevent memory leaks
    context.user_data.clear()

    return ConversationHandler.END


@log_telegram_errors(logger)
async def back_to_list_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'Back to list' button callback"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context)

    # Get last recommendations from cache
    last_recs = context.user_data.get("last_recs", {})
    if not last_recs:
        await safe_edit_message(
            query,
            text="❌ No previous recommendations found. Please run /start to get new recommendations."
        )
        return

    # Convert cache back to list format
    recommendations = list(last_recs.values())

    # Sort by hybrid score if available
    recommendations.sort(key=lambda x: x.get('hybrid_score', x.get('mcda_score', 0)), reverse=True)

    # Show recommendations list
    await display_recommendations_list(update, context, recommendations, query=query)


@log_telegram_errors(logger)
async def explain_rec_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'Why this recommendation?' button callback"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context)

    # Extract program ID from callback data
    callback_data = query.data
    if not callback_data.startswith("explain_rec:"):
        await safe_edit_message(query, text="❌ Invalid callback data")
        return

    program_id = callback_data.split(":", 1)[1]

    # Get program from cache
    last_recs = context.user_data.get("last_recs", {})
    programme = last_recs.get(program_id)

    if not programme:
        await safe_edit_message(query, text=f"❌ Program {program_id} not found in cache")
        return

    # Create explanation message
    mcda_score = programme.get('mcda_score', 0.0)
    ml_score = programme.get('ml_score', 0.0)
    hybrid_score = programme.get('hybrid_score', 0.0)
    reason = programme.get('mcda_reason', 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ')

    explanation_text = f"""{t('hybrid_score_explanation', lang)}

{t('score_breakdown', lang).format(
    mcda_score=mcda_score,
    ml_score=ml_score,
    hybrid_score=hybrid_score
)}

{t('why_recommended', lang).format(reason=reason)}

{t('mcda_explanation', lang)}
{t('ml_explanation', lang)}"""

    # Create back button
    keyboard = [[InlineKeyboardButton(t('button_back_to_list', lang), callback_data="back_to_list")]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await safe_edit_message(
        query,
        text=explanation_text,
        reply_markup=reply_markup
    )


@log_telegram_errors(logger)
async def more_info_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'More Info' button callback for detailed programme view"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # Extract major_id from callback data: "MORE_major_id"
    callback_data = query.data
    if not callback_data.startswith("MORE_"):
        await safe_edit_message(query, text="❌ Invalid callback data")
        return

    major_id = callback_data[5:]  # Remove "MORE_" prefix

    # Get programme from cached recommendations
    cached_recs = context.user_data.get("last_recs", {})
    programme = cached_recs.get(major_id)

    if not programme:
        await safe_edit_message(query, text=f"❌ Program {major_id} not found in cache")
        return

    # Create detailed view
    from .ui import create_detail_view
    message_text, reply_markup = create_detail_view(programme, lang)

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


@log_telegram_errors(logger)
async def back_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'Back' button callback to return to recommendations list"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # Get cached recommendations
    cached_recs = context.user_data.get("last_recs", {})
    if not cached_recs:
        await safe_edit_message(
            query,
            text="❌ No previous recommendations found. Please run /start to get new recommendations."
        )
        return

    # Convert cache back to list format and sort by score
    recommendations = list(cached_recs.values())
    recommendations.sort(key=lambda x: x.get('hybrid_score', x.get('mcda_score', 0)), reverse=True)

    # Show recommendations using new UI
    from .ui import create_recommendations_view
    message_text, reply_markup = create_recommendations_view(recommendations, lang)

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


@log_telegram_errors(logger)
async def refresh_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'Refresh' button callback to regenerate recommendations"""
    query = update.callback_query
    await safe_answer_callback(query)

    # Get cached user answers
    user_answers = context.user_data.get("last_answers", {})
    if not user_answers:
        await safe_edit_message(
            query,
            text="❌ No previous assessment found. Please run /start to take the assessment."
        )
        return

    # Regenerate recommendations
    try:
        from src.core.hybrid_recommender import get_recommendations
        recommendations = get_recommendations(user_answers, top_k=5)

        # Cache new recommendations
        cache_recommendations(recommendations, context)
        cache_user_answers(user_answers, context)

        lang = get_lang(context.user_data)

        # Show refreshed recommendations
        from .ui import create_recommendations_view
        message_text, reply_markup = create_recommendations_view(recommendations, lang)

        await safe_edit_message(
            query,
            text=message_text,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )

    except Exception as e:
        logger.error(f"Error refreshing recommendations: {e}")
        await safe_edit_message(
            query,
            text=f"❌ Error refreshing recommendations: {str(e)}"
        )


@log_telegram_errors(logger)
async def restart_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'Restart' button callback to start new assessment"""
    query = update.callback_query
    await safe_answer_callback(query)

    # Clear user data and start new assessment
    context.user_data.clear()
    await start_assessment(update, context)


@log_telegram_errors(logger)
async def detail_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'DET_{major_id}' button callback for enhanced detail view"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # Extract major_id from callback data: "DET_major_id"
    callback_data = query.data
    if not callback_data.startswith("DET_"):
        await safe_edit_message(query, text="❌ Invalid callback data")
        return

    major_id = callback_data[4:]  # Remove "DET_" prefix

    # Get programme from cached recommendations
    cached_recs = context.user_data.get("last_recs", {})
    programme = cached_recs.get(major_id)

    if not programme:
        await safe_edit_message(query, text=f"❌ Program {major_id} not found in cache")
        return

    # Get full recommendations list for navigation
    recommendations = list(cached_recs.values())
    recommendations.sort(key=lambda x: x.get('hybrid_score', x.get('mcda_score', 0)), reverse=True)

    # Emit analytics metric
    try:
        from ..infra.metrics import count_calls
        count_calls("detail_opened")
    except ImportError:
        pass

    # Create enhanced detail view with navigation
    from .ui import create_detail_view
    message_text, reply_markup = create_detail_view(programme, lang, recommendations)

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


@log_telegram_errors(logger)
async def compare_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'CMP_{major_id}' button callback for instant comparison"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # Extract major_id from callback data: "CMP_major_id"
    callback_data = query.data
    if not callback_data.startswith("CMP_"):
        await safe_edit_message(query, text="❌ Invalid callback data")
        return

    major_id = callback_data[4:]  # Remove "CMP_" prefix

    # Get programme from cached recommendations
    cached_recs = context.user_data.get("last_recs", {})
    programme = cached_recs.get(major_id)

    if not programme:
        await safe_edit_message(query, text=f"❌ Program {major_id} not found in cache")
        return

    # Get top-ranked programme for comparison
    recommendations = list(cached_recs.values())
    recommendations.sort(key=lambda x: x.get('hybrid_score', x.get('mcda_score', 0)), reverse=True)

    if not recommendations:
        await safe_edit_message(query, text="❌ No recommendations available for comparison")
        return

    top_programme = recommendations[0]

    # Create instant comparison
    from .ui import create_comparison_view
    comparison_text = create_comparison_view(programme, top_programme, lang)

    # Create back button
    keyboard = [[InlineKeyboardButton(f"⬅️ {tr('back_to_list', lang)}", callback_data="BACK")]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await safe_edit_message(
        query,
        text=comparison_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


@log_telegram_errors(logger)
async def filters_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'FILTERS' button callback to show filter menu"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)
    callback_data = query.data

    # Handle specific filter actions
    if callback_data.startswith("FILTER_"):
        filter_type = callback_data.replace("FILTER_", "")
        await handle_filter_selection(update, context, filter_type)
        return

    # Show main filters menu
    from .ui import create_filters_menu
    message_text, reply_markup = create_filters_menu(lang)

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup
    )


@log_telegram_errors(logger)
async def handle_filter_selection(update: Update, context: ContextTypes.DEFAULT_TYPE, filter_type: str) -> None:
    """Handle specific filter type selection (budget, field, city)"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # Create filter options based on type
    if filter_type == "budget":
        message_text = f"💰 {tr('select_budget_filter', lang)}"
        keyboard_rows = [
            [
                InlineKeyboardButton(f"💸 {tr('low_budget', lang)}", callback_data="APPLY_FILTER_budget_low"),
                InlineKeyboardButton(f"💰 {tr('mid_budget', lang)}", callback_data="APPLY_FILTER_budget_mid")
            ],
            [
                InlineKeyboardButton(f"💎 {tr('high_budget', lang)}", callback_data="APPLY_FILTER_budget_high"),
                InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="FILTERS_HOME")
            ]
        ]
    elif filter_type == "field":
        message_text = f"🔬 {tr('select_field_filter', lang)}"
        keyboard_rows = [
            [
                InlineKeyboardButton(f"🔬 {tr('stem_field', lang)}", callback_data="APPLY_FILTER_field_stem"),
                InlineKeyboardButton(f"💼 {tr('business_field', lang)}", callback_data="APPLY_FILTER_field_business")
            ],
            [
                InlineKeyboardButton(f"🏥 {tr('health_field', lang)}", callback_data="APPLY_FILTER_field_health"),
                InlineKeyboardButton(f"🎨 {tr('arts_field', lang)}", callback_data="APPLY_FILTER_field_arts")
            ],
            [
                InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="FILTERS_HOME")
            ]
        ]
    elif filter_type == "city":
        message_text = f"🏛️ {tr('select_city_filter', lang)}"
        keyboard_rows = [
            [
                InlineKeyboardButton(f"🏛️ {tr('phnom_penh', lang)}", callback_data="APPLY_FILTER_city_pp"),
                InlineKeyboardButton(f"🏛️ {tr('siem_reap', lang)}", callback_data="APPLY_FILTER_city_sr")
            ],
            [
                InlineKeyboardButton(f"🏛️ {tr('battambang', lang)}", callback_data="APPLY_FILTER_city_btb"),
                InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="FILTERS_HOME")
            ]
        ]
    else:
        # Unknown filter type, go back to main filters menu
        await filters_callback(update, context)
        return

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=InlineKeyboardMarkup(keyboard_rows)
    )


@log_telegram_errors(logger)
async def apply_filter_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle filter application (APPLY_FILTER_type_value)"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)
    callback_data = query.data

    # Parse filter data: APPLY_FILTER_type_value
    if not callback_data.startswith("APPLY_FILTER_"):
        await unknown_callback(update, context)
        return

    filter_parts = callback_data.replace("APPLY_FILTER_", "").split("_", 1)
    if len(filter_parts) != 2:
        await unknown_callback(update, context)
        return

    filter_type, filter_value = filter_parts

    # Get current recommendations
    last_recs = context.user_data.get("last_recs", {})
    if not last_recs:
        await safe_edit_message(
            query,
            text=f"❌ {tr('no_recommendations_to_filter', lang)}\n\n{tr('take_quiz_first', lang)}",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(f"🧮 {tr('take_quiz', lang)}", callback_data="START_QUIZ"),
                InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="HOME")
            ]])
        )
        return

    # Convert recommendations to list format for filtering
    recommendations_list = list(last_recs.values())

    # Apply filter
    from .ui import apply_filter
    filtered_recs = apply_filter(recommendations_list, filter_type, filter_value)

    if not filtered_recs:
        filter_name = tr(f'{filter_type}_filter', lang)
        filter_value_name = tr(f'{filter_value}_{filter_type}', lang) if tr(f'{filter_value}_{filter_type}', lang) != f'{filter_value}_{filter_type}' else filter_value

        await safe_edit_message(
            query,
            text=f"🔍 {tr('no_results_for_filter', lang)}\n\n"
                 f"🏷️ {tr('filter', lang)}: {filter_name} - {filter_value_name}\n\n"
                 f"{tr('try_different_filter', lang)}",
            reply_markup=InlineKeyboardMarkup([
                [
                    InlineKeyboardButton(f"🏷️ {tr('try_other_filters', lang)}", callback_data="FILTERS_HOME"),
                    InlineKeyboardButton(f"🔄 {tr('show_all', lang)}", callback_data="BACK")
                ],
                [
                    InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="HOME")
                ]
            ])
        )
        return

    # Convert filtered results back to dict format and update context
    filtered_dict = {rec['major_id']: rec for rec in filtered_recs}
    context.user_data["last_recs"] = filtered_dict

    # Show filtered recommendations
    from .ui import create_recommendations_view
    message_text, reply_markup = create_recommendations_view(filtered_recs, lang)

    # Add filter info to message
    filter_name = tr(f'{filter_type}_filter', lang)
    filter_value_name = tr(f'{filter_value}_{filter_type}', lang) if tr(f'{filter_value}_{filter_type}', lang) != f'{filter_value}_{filter_type}' else filter_value

    message_text = f"🔍 {tr('filtered_results', lang)}: {filter_name} - {filter_value_name}\n\n" + message_text

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


@log_telegram_errors(logger)
async def save_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'SAVE_{major_id}' button callback to save to shortlist"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # Extract major_id from callback data: "SAVE_major_id"
    callback_data = query.data
    if not callback_data.startswith("SAVE_"):
        await safe_edit_message(query, text="❌ Invalid callback data")
        return

    major_id = callback_data[5:]  # Remove "SAVE_" prefix

    # Initialize shortlist if not exists
    if "shortlist" not in context.user_data:
        context.user_data["shortlist"] = []

    # Add to shortlist if not already there
    if major_id not in context.user_data["shortlist"]:
        context.user_data["shortlist"].append(major_id)
        await query.answer(f"💾 {tr('saved', lang)}", show_alert=True)
    else:
        await query.answer(f"ℹ️ {tr('already_saved', lang)}", show_alert=True)


@log_telegram_errors(logger)
async def remove_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle 'REMOVE_{major_id}' button callback to remove from results"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # Extract major_id from callback data: "REMOVE_major_id"
    callback_data = query.data
    if not callback_data.startswith("REMOVE_"):
        await safe_edit_message(query, text="❌ Invalid callback data")
        return

    major_id = callback_data[7:]  # Remove "REMOVE_" prefix

    # Remove from cached recommendations
    cached_recs = context.user_data.get("last_recs", {})
    if major_id in cached_recs:
        del cached_recs[major_id]
        context.user_data["last_recs"] = cached_recs

        # Also remove from shortlist if it's there
        shortlist = context.user_data.get("shortlist", [])
        if major_id in shortlist:
            shortlist.remove(major_id)
            context.user_data["shortlist"] = shortlist

        # If we have fewer than 5 recommendations, try to get more
        recommendations = list(cached_recs.values())
        if len(recommendations) < 5:
            # Try to get more recommendations from original scoring
            user_answers = context.user_data.get("last_answers", {})
            if user_answers:
                try:
                    from src.core.hybrid_recommender import get_recommendations
                    fresh_recs = get_recommendations(user_answers, top_k=10)

                    # Add new recommendations that aren't already in cache
                    for rec in fresh_recs:
                        if rec.get('major_id') not in cached_recs and len(cached_recs) < 5:
                            cached_recs[rec.get('major_id')] = rec

                    context.user_data["last_recs"] = cached_recs
                except Exception:
                    pass

        # Return to recommendations list
        await back_callback(update, context)
    else:
        await query.answer(f"❌ {tr('not_found', lang)}", show_alert=True)


@log_telegram_errors(logger)
async def section_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle section navigation in ultra-detail view"""
    query = update.callback_query
    await safe_answer_callback(query)

    # Parse: SEC_major-id_section-number
    callback_data = query.data
    if not callback_data.startswith("SEC_"):
        return

    parts = callback_data[4:].rsplit("_", 1)
    if len(parts) != 2:
        return

    major_id, section_str = parts
    try:
        section_index = int(section_str)
    except ValueError:
        return

    # Get programme and render section
    cached_recs = context.user_data.get("last_recs", {})
    programme = cached_recs.get(major_id)

    if not programme:
        lang = get_lang(context.user_data)
        await safe_edit_message(query, text=f"❌ {tr('programme_not_found', lang)}")
        return

    lang = get_lang(context.user_data)
    from .ui import render_section
    message_text, reply_markup = render_section(programme, section_index, lang)

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


async def home_screen_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show enhanced home screen"""
    query = update.callback_query
    if query:
        await safe_answer_callback(query)

    lang = get_lang(context.user_data)
    from .ui import create_home_screen
    message_text, reply_markup = create_home_screen(lang)

    if query:
        await safe_edit_message(
            query,
            text=message_text,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )
    else:
        await update.message.reply_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode="Markdown"
        )


async def surprise_me_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Generate random recommendations"""
    query = update.callback_query
    await safe_answer_callback(query)

    # Generate random user answers
    import random
    random_answers = {
        'location_preference': random.choice(['pp', 'sr', 'btb', 'any']),
        'budget_range': random.choice(['low', 'mid', 'high']),
        'field_of_interest': random.choice(['stem', 'business', 'health', 'arts', 'social']),
        'career_goal': random.choice(['tech', 'finance', 'health', 'gov', 'entre']),
        'scholarship_need': random.choice(['yes', 'no'])
    }

    try:
        from src.core.hybrid_recommender import get_recommendations
        recommendations = get_recommendations(random_answers, top_k=5)

        # Cache recommendations and answers
        cache_recommendations(recommendations, context)
        cache_user_answers(random_answers, context)

        lang = get_lang(context.user_data)
        from .ui import create_recommendations_view
        message_text, reply_markup = create_recommendations_view(recommendations, lang)

        # Add surprise banner
        surprise_banner = f"🎲 {tr('surprise_recommendations', lang)}\n\n"
        message_text = surprise_banner + message_text

        await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )

    except Exception as e:
        logger.error(f"Error generating surprise recommendations: {e}")
        lang = get_lang(context.user_data)
        await safe_edit_message(
            query,
            text=f"❌ {tr('error_generating_recommendations', lang)}"
        )


async def browse_majors_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show paginated major browsing interface"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    # For now, show a simple message - full implementation would require pagination
    await safe_edit_message(
        query,
        text=f"🗂 {tr('browse_majors_coming_soon', lang)}\n\n{tr('use_quiz_instead', lang)}",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(f"🧮 {tr('take_quiz', lang)}", callback_data="START_QUIZ"),
            InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="HOME")
        ]])
    )


async def shortlist_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show user's saved majors"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)
    shortlist = context.user_data.get("shortlist", [])

    if not shortlist:
        await safe_edit_message(
            query,
            text=f"⭐ {tr('shortlist_empty', lang)}\n\n{tr('save_majors_hint', lang)}",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(f"🧮 {tr('take_quiz', lang)}", callback_data="START_QUIZ"),
                InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="HOME")
            ]])
        )
        return

    # Show shortlisted majors
    cached_recs = context.user_data.get("last_recs", {})
    shortlist_text = f"⭐ {tr('your_shortlist', lang)} ({len(shortlist)})\n\n"

    keyboard_rows = []
    for i, major_id in enumerate(shortlist, 1):
        programme = cached_recs.get(major_id)
        if programme:
            major_name = programme.get('major_name_kh' if lang == 'kh' else 'major_name_en', major_id)
            shortlist_text += f"{i}. {major_name}\n"
            keyboard_rows.append([
                InlineKeyboardButton(f"ℹ️ {major_name[:20]}...", callback_data=f"DET_{major_id}"),
                InlineKeyboardButton(f"🗑️", callback_data=f"REMOVE_{major_id}")
            ])

    keyboard_rows.append([
        InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="HOME")
    ])

    await safe_edit_message(
        query,
        text=shortlist_text,
        reply_markup=InlineKeyboardMarkup(keyboard_rows),
        parse_mode="Markdown"
    )


async def help_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Show help information"""
    query = update.callback_query
    await safe_answer_callback(query)

    lang = get_lang(context.user_data)

    help_text = f"""❓ *{tr('help_title', lang)}*

{tr('help_description', lang)}

🧮 *{tr('take_quiz', lang)}*: {tr('help_quiz', lang)}

🎲 *{tr('surprise_me', lang)}*: {tr('help_surprise', lang)}

🏷️ *{tr('filter', lang)}*: {tr('help_filter', lang)}

⭐ *{tr('shortlist', lang)}*: {tr('help_shortlist', lang)}

🌐 *{tr('language_toggle', lang)}*: {tr('help_language', lang)}"""

    await safe_edit_message(query, text=help_text, reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(f"⬅️ {tr('back', lang)}", callback_data="HOME")
        ]]),
        parse_mode="Markdown"
    )


def log_exceptions(logger):
    """Decorator to log exceptions with graceful error handling."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Exception in {func.__name__}: {e}", exc_info=True)
                # Attempt user notification without re-raising
                try:
                    update = args[0] if args else None
                    if update and hasattr(update, 'callback_query') and update.callback_query:
                        await update.callback_query.answer("⚠️ An error occurred", show_alert=True)
                except Exception:
                    pass  # Swallow secondary failures
                return None  # Graceful degradation
        return wrapper
    return decorator


@log_exceptions(logger)
async def language_toggle_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """CRITICAL FIX: Toggle language and update both language keys for system compatibility"""
    query = update.callback_query
    await safe_answer_callback(query)

    # Toggle language
    current_lang = get_lang(context.user_data)
    new_lang = 'en' if current_lang == 'kh' else 'kh'

    # CRITICAL FIX: Update both keys for compatibility
    context.user_data['language'] = new_lang
    context.user_data['lang'] = new_lang

    # Show home screen in new language
    from .ui import create_home_screen
    message_text, reply_markup = create_home_screen(new_lang)

    await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )


@log_exceptions(logger)
async def quick_start_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle QS_* patterns - map to preset answers, call show_recommendations()"""
    query = update.callback_query
    await safe_answer_callback(query)

    callback_data = query.data
    lang = get_lang(context.user_data)

    # Map quick start patterns to preset answers
    preset_answers = {}

    if callback_data == "QS_SURPRISE":
        # Random answers for surprise me
        import random
        preset_answers = {
            'location_preference': random.choice(['pp', 'sr', 'btb', 'any']),
            'budget_range': random.choice(['low', 'mid', 'high']),
            'field_of_interest': random.choice(['stem', 'business', 'health', 'arts', 'social']),
            'career_goal': random.choice(['tech', 'finance', 'health', 'gov', 'entre']),
            'scholarship_need': random.choice(['yes', 'no'])
        }
    elif callback_data == "QS_PP":
        # Phnom Penh focused
        preset_answers = {
            'location_preference': 'pp',
            'budget_range': 'mid',
            'field_of_interest': 'stem',
            'career_goal': 'tech',
            'scholarship_need': 'no'
        }
    elif callback_data == "QS_BUD_LOW":
        # Low budget focused
        preset_answers = {
            'location_preference': 'any',
            'budget_range': 'low',
            'field_of_interest': 'any',
            'career_goal': 'any',
            'scholarship_need': 'yes'
        }

    try:
        from src.core.hybrid_recommender import get_recommendations
        recommendations = get_recommendations(preset_answers, top_k=5)

        # Cache recommendations and answers
        cache_recommendations(recommendations, context)
        cache_user_answers(preset_answers, context)

        from .ui import create_recommendations_view
        message_text, reply_markup = create_recommendations_view(recommendations, lang)

        # Add quick start banner
        if callback_data == "QS_SURPRISE":
            banner = f"🎲 {tr('surprise_recommendations', lang)}\n\n"
        elif callback_data == "QS_PP":
            banner = f"🏛️ {tr('phnom_penh_focused', lang)}\n\n"
        elif callback_data == "QS_BUD_LOW":
            banner = f"💰 {tr('low_tuition_focused', lang)}\n\n"
        else:
            banner = f"⚡ {tr('quick_start', lang)}\n\n"

        message_text = banner + message_text

        await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )

    except Exception as e:
        logger.error(f"Error in quick start: {e}")
        await safe_edit_message(
            query,
            text=f"❌ {tr('error_generating_recommendations', lang)}"
        )


@log_exceptions(logger)
async def wizard_start_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Start 4-question wizard flow"""
    query = update.callback_query
    await safe_answer_callback(query)

    # Initialize wizard state
    context.user_data['wizard_step'] = 0
    context.user_data['wizard_answers'] = {}

    lang = get_lang(context.user_data)

    # Start with first wizard question (location)
    from .keyboards import ACTIVE_QUESTIONS

    # Find location question
    location_question = None
    for q in ACTIVE_QUESTIONS:
        if q['id'] == 'location_preference':
            location_question = q
            break

    if not location_question:
        await safe_edit_message(query, text=f"❌ {tr('wizard_error', lang)}")
        return

    from .keyboards import create_question_keyboard
    keyboard = create_question_keyboard(location_question, lang)

    wizard_text = f"""⚡ {tr('guide_me', lang)} (1/4)

{tr('wizard_intro', lang)}

{t(location_question['question_key'], lang)}"""

    await safe_edit_message(
        query,
        text=wizard_text,
        reply_markup=keyboard
    )


# Removed share_start_handler for MVP - no social sharing features


@log_exceptions(logger)
async def trending_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Load trending.json, route to detail view"""
    query = update.callback_query
    await safe_answer_callback(query)

    callback_data = query.data
    if not callback_data.startswith("TREND_"):
        return

    major_id = callback_data[6:]  # Remove "TREND_"

    try:
        # Load programme data
        from src.core.data_loader import load_raw
        programmes = load_raw()

        # Find the programme
        programme = None
        for prog in programmes:
            if prog.get('major_id') == major_id:
                programme = prog
                break

        if not programme:
            lang = get_lang(context.user_data)
            await safe_edit_message(query, text=f"❌ {tr('programme_not_found', lang)}")
            return

        # Show detail view (section 0 - overview)
        lang = get_lang(context.user_data)
        from .ui import render_section
        message_text, reply_markup = render_section(programme, 0, lang)

        await safe_edit_message(
        query,
        text=message_text,
        reply_markup=reply_markup,
        parse_mode="Markdown"
    )

    except Exception as e:
        logger.error(f"Error in trending callback: {e}")
        lang = get_lang(context.user_data)
        await safe_edit_message(query, text=f"❌ {tr('error_loading_programme', lang)}")


# Removed feedback_callback for MVP - no gamification features


async def unknown_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle unknown callback data with graceful fallback"""
    query = update.callback_query
    await safe_answer_callback(query, "🤔")

    # Log unhandled callback for debugging
    logger.warning(f"Unhandled callback: {query.data}")

    # Provide user feedback
    lang = get_lang(context.user_data)
    await safe_edit_message(
        query,
        text=f"❌ {tr('unknown_action', lang)}\n\n{tr('please_restart', lang)}",
        reply_markup=InlineKeyboardMarkup([[
            InlineKeyboardButton(f"↩️ {tr('restart', lang)}", callback_data="RESTART")
        ]])
    )
