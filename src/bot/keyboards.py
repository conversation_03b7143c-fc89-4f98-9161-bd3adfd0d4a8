"""
Telegram Keyboards Module
16 bilingual assessment questions with inline keyboards and progress tracking
"""

from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from typing import Dict, List, Any
from .i18n import t


# 16 Assessment Questions - Bilingual Support
ASSESSMENT_QUESTIONS = [
    {
        "id": "location_preference",
        "question_key": "question_location",
        "options": [
            ("pp", "option_pp"),
            ("sr", "option_sr"),
            ("btb", "option_btb"),
            ("any", "option_any")
        ]
    },
    {
        "id": "budget_range",
        "question_key": "question_budget",
        "options": [
            ("low", "option_low"),
            ("mid", "option_mid"),
            ("high", "option_high"),
            ("flex", "option_flex")
        ]
    },
    {
        "id": "field_of_interest",
        "question_key": "question_field",
        "options": [
            ("stem", "option_stem"),
            ("business", "option_business"),
            ("health", "option_health"),
            ("arts", "option_arts"),
            ("social", "option_social"),
            ("education", "option_education")
        ]
    },
    {
        "id": "career_goal",
        "question_key": "question_career",
        "options": [
            ("tech", "option_tech"),
            ("finance", "option_finance"),
            ("health", "option_health"),
            ("gov", "option_gov"),
            ("entre", "option_entre"),
            ("unsure", "option_unsure")
        ]
    },
    {
        "id": "academic_strength",
        "question_key": "question_academic",
        "options": [
            ("math", "option_math"),
            ("language", "option_language"),
            ("social", "option_social"),
            ("arts", "option_arts"),
            ("hands_on", "option_hands_on"),
            ("all", "option_all")
        ]
    },
    {
        "id": "learning_style",
        "question_key": "question_learning",
        "options": [
            ("theory", "option_theory"),
            ("practical", "option_practical"),
            ("group", "option_group"),
            ("self", "option_self"),
            ("mixed", "option_mixed")
        ]
    },
    {
        "id": "study_mode",
        "question_key": "question_study_mode",
        "options": [
            ("full", "option_full"),
            ("part", "option_part"),
            ("evening", "option_evening"),
            ("weekend", "option_weekend"),
            ("flex", "option_flex")
        ]
    },
    {
        "id": "language_pref",
        "question_key": "question_language",
        "options": [
            ("kh", "option_kh"),
            ("en", "option_en"),
            ("both", "option_both")
        ]
    },
    {
        "id": "scholarship_need",
        "question_key": "question_scholarship",
        "options": [
            ("yes", "option_yes"),
            ("partial", "option_partial"),
            ("no", "option_no")
        ]
    },
    {
        "id": "campus_life",
        "question_key": "question_campus",
        "options": [
            ("very", "option_very"),
            ("some", "option_some"),
            ("little", "option_little"),
            ("none", "option_none")
        ]
    },
    {
        "id": "extracurricular",
        "question_key": "question_extracurricular",
        "options": [
            ("sports", "option_sports"),
            ("arts", "option_arts"),
            ("volunteer", "option_volunteer"),
            ("clubs", "option_clubs"),
            ("none", "option_none")
        ]
    },
    {
        "id": "employment_priority",
        "question_key": "question_employment",
        "options": [
            ("salary", "option_salary"),
            ("stability", "option_stability"),
            ("passion", "option_passion"),
            ("growth", "option_growth"),
            ("balance", "option_balance")
        ]
    },
    {
        "id": "mental_health_support",
        "question_key": "question_mental_health",
        "options": [
            ("high", "option_high"),
            ("medium", "option_medium"),
            ("low", "option_low")
        ]
    },
    {
        "id": "international_exposure",
        "question_key": "question_international",
        "options": [
            ("yes", "option_yes"),
            ("maybe", "option_maybe"),
            ("no", "option_no")
        ]
    },
    {
        "id": "study_plan_assistance",
        "question_key": "question_study_plan",
        "options": [
            ("yes", "option_yes"),
            ("no", "option_no")
        ]
    }
]

# Active questions for assessment (first 15 questions only)
ACTIVE_QUESTIONS = ASSESSMENT_QUESTIONS[:15]


def create_question_keyboard(question_data: Dict[str, Any], lang: str = "kh") -> InlineKeyboardMarkup:
    """
    Create inline keyboard for a specific question

    Args:
        question_data: Question dictionary with options
        lang: Language code for translations

    Returns:
        InlineKeyboardMarkup: Telegram inline keyboard
    """
    keyboard = []

    # Create buttons for each option
    for option_value, option_key in question_data["options"]:
        button = InlineKeyboardButton(
            text=t(option_key, lang),
            callback_data=f"answer_{question_data['id']}_{option_value}"
        )
        keyboard.append([button])

    return InlineKeyboardMarkup(keyboard)


def create_progress_text(current_question: int, lang: str = "kh", total_questions: int = 15) -> str:
    """
    Create progress indicator text

    Args:
        current_question: Current question number (1-based)
        lang: Language code for translations
        total_questions: Total number of questions

    Returns:
        str: Progress text in specified language
    """
    # Create progress bar
    progress_percent = (current_question / total_questions) * 100
    filled_blocks = int(progress_percent / 10)
    progress_bar = "█" * filled_blocks + "░" * (10 - filled_blocks)

    progress_text = t("assessment_progress", lang).format(current=current_question, total=total_questions)
    return f"{progress_text}\n{progress_bar} {progress_percent:.0f}%"


def get_question_by_id(question_id: str) -> Dict[str, Any]:
    """
    Get question data by ID

    Args:
        question_id: Question identifier

    Returns:
        Dict: Question data
    """
    for question in ASSESSMENT_QUESTIONS:
        if question["id"] == question_id:
            return question

    raise ValueError(f"Question with ID '{question_id}' not found")


def get_question_by_index(index: int) -> Dict[str, Any]:
    """
    Get question data by index (using active questions only)

    Args:
        index: Question index (0-based)

    Returns:
        Dict: Question data
    """
    if 0 <= index < len(ACTIVE_QUESTIONS):
        return ACTIVE_QUESTIONS[index]

    raise IndexError(f"Question index {index} out of range")
