"""
EduGuideBot Main Application
Entry point for the Telegram bot
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler
from dotenv import load_dotenv
from .telegram_safe import validate_callback_pattern

# Add project root to path for imports
if __name__ == '__main__':
    sys.path.append(str(Path(__file__).parents[2]))

# Add project root to path for script execution
if __name__ == '__main__':
    sys.path.append(str(Path(__file__).parents[2]))
    from src.bot.commands import (start_command, recommend_command, help_command, cancel_command,
                                  filter_language_command, campus_map_command, study_plan_command,
                                  mental_health_command, settings_command, handle_language_setting, details_command)
    from src.bot.handlers import create_conversation_handler
else:
    from .commands import (start_command, recommend_command, help_command, cancel_command,
                          filter_language_command, campus_map_command, study_plan_command,
                          mental_health_command, settings_command, handle_language_setting, details_command)
    from .handlers import create_conversation_handler

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Global flag for handler registration
_handlers_registered = False


async def set_commands_safe(application: Application) -> None:
    """Set bot commands with test environment compatibility."""
    try:
        from telegram import BotCommand
        await application.bot.set_my_commands([
            BotCommand("start", "Start the bot and see main menu"),
            BotCommand("quiz", "Take the 15-question assessment"),
            BotCommand("browse", "Browse all available majors"),
            BotCommand("shortlist", "View your saved majors"),
            BotCommand("settings", "Language and preferences"),
            BotCommand("help", "Get help and instructions")
        ])
        logger.info("Bot commands set successfully")
    except (TypeError, AttributeError):
        # Handle MagicMock in test environment
        logger.debug("Skipping bot commands in test environment")
    except Exception as e:
        logger.debug(f"Could not set bot commands: {e}")


def register_handlers(application: Application) -> None:
    """Register all command & callback handlers to app exactly once (idempotent)."""
    global _handlers_registered

    if _handlers_registered:
        logger.info("Handlers already registered, skipping duplicate registration")
        return

    from telegram.ext import MessageHandler, filters

    # Basic command handlers
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("cancel", cancel_command))
    application.add_handler(CommandHandler("filterlanguage", filter_language_command))
    application.add_handler(CommandHandler("campusmap", campus_map_command))
    application.add_handler(CommandHandler("studyplan", study_plan_command))
    application.add_handler(CommandHandler("mentalhealth", mental_health_command))
    application.add_handler(CommandHandler("settings", settings_command))

    # Details command handler (pattern-based for dynamic IDs)
    application.add_handler(MessageHandler(filters.Regex(r'^/details_'), details_command))

    # Conversation handler
    conversation_handler = create_conversation_handler()
    application.add_handler(conversation_handler)

    # Callback handlers
    application.add_handler(CallbackQueryHandler(handle_language_setting, pattern=r'^lang_'))

    # Import and register inline button callbacks
    from .handlers import (
        back_to_list_callback, explain_rec_callback,
        more_info_callback, back_callback, refresh_callback, restart_callback,
        detail_callback, compare_callback, filters_callback,
        save_callback, remove_callback, unknown_callback,
        section_callback, home_screen_callback, start_quiz_callback,
        surprise_me_callback, browse_majors_callback, shortlist_callback,
        help_callback, language_toggle_callback,
        # NEW v4.0 handlers
        quick_start_callback, wizard_start_callback, trending_callback,
        feedback_callback, share_start_handler
    )

    application.add_handler(CallbackQueryHandler(back_to_list_callback, pattern=r'^back_to_list$'))
    application.add_handler(CallbackQueryHandler(explain_rec_callback, pattern=r'^explain_rec:'))

    # New streamlined UI callbacks
    application.add_handler(CallbackQueryHandler(more_info_callback, pattern=r'^MORE_'))
    application.add_handler(CallbackQueryHandler(back_callback, pattern=r'^BACK$'))
    application.add_handler(CallbackQueryHandler(refresh_callback, pattern=r'^REFRESH$'))
    application.add_handler(CallbackQueryHandler(restart_callback, pattern=r'^RESTART$'))

    # Enhanced navigation callbacks
    application.add_handler(CallbackQueryHandler(detail_callback, pattern=r'^DET_'))
    application.add_handler(CallbackQueryHandler(compare_callback, pattern=r'^CMP_'))
    application.add_handler(CallbackQueryHandler(filters_callback, pattern=r'^FILTERS$'))
    application.add_handler(CallbackQueryHandler(save_callback, pattern=r'^SAVE_'))
    application.add_handler(CallbackQueryHandler(remove_callback, pattern=r'^REMOVE_'))

    # Ultra-detail section navigation
    application.add_handler(CallbackQueryHandler(section_callback, pattern=r'^SEC_'))

    # Home screen and enhanced features
    application.add_handler(CallbackQueryHandler(home_screen_callback, pattern=r'^HOME$'))
    application.add_handler(CallbackQueryHandler(start_quiz_callback, pattern=r'^START_QUIZ$'))
    application.add_handler(CallbackQueryHandler(filters_callback, pattern=r'^FILTERS_HOME$'))
    application.add_handler(CallbackQueryHandler(browse_majors_callback, pattern=r'^BROWSE_MAJORS$'))
    application.add_handler(CallbackQueryHandler(shortlist_callback, pattern=r'^SHORTLIST_VIEW$'))
    application.add_handler(CallbackQueryHandler(help_callback, pattern=r'^HELP_INFO$'))
    application.add_handler(CallbackQueryHandler(language_toggle_callback, pattern=r'^LANG_TOGGLE$'))

    # NEW v4.0 Instant-Engage Features (CRITICAL: Register BEFORE unknown_callback)
    application.add_handler(CallbackQueryHandler(quick_start_callback, pattern=r'^QS_'))
    application.add_handler(CallbackQueryHandler(wizard_start_callback, pattern=r'^WIZARD_START$'))
    application.add_handler(CallbackQueryHandler(trending_callback, pattern=r'^TREND_'))
    application.add_handler(CallbackQueryHandler(feedback_callback, pattern=r'^FB_(UP|DOWN)$'))

    # Catch-all for unknown callbacks (must be last)
    application.add_handler(CallbackQueryHandler(unknown_callback))

    # Import and register filters callback
    try:
        from .commands import handle_filters_callback
        application.add_handler(CallbackQueryHandler(handle_filters_callback, pattern=r'^(show_filters|toggle_low_cost|toggle_phnom_penh|back_to_settings)$'))
    except ImportError as e:
        logger.warning(f"Could not import handle_filters_callback: {e}")

    # Add error handler
    if __name__ == '__main__':
        from src.bot.error_handler import error_handler
    else:
        from .error_handler import error_handler
    application.add_error_handler(error_handler)

    _handlers_registered = True


def validate_callback_patterns() -> None:
    """
    Validate that all callback patterns are properly configured and don't overlap.
    This helps prevent unhandled callback errors in production.
    """
    # Test common callback patterns that should be handled
    test_patterns = [
        "answer_location_preference_pp",
        "answer_budget_range_low",
        "FILTER_city",
        "FILTERS_HOME",
        "BACK",
        "HOME",
        "RESTART",
        "DET_123",
        "SEC_456_2",
        "QS_SURPRISE",
        "TREND_789",
        "FB_UP",
        "MORE_123",
        "SAVE_456",
        "REMOVE_789",
        "CMP_123",
        "lang_kh",
        "WIZARD_START"
    ]

    unhandled_patterns = []
    for pattern in test_patterns:
        if not validate_callback_pattern(pattern):
            unhandled_patterns.append(pattern)

    if unhandled_patterns:
        logger.warning(f"Potentially unhandled callback patterns detected: {unhandled_patterns}")
        logger.warning("Consider updating callback patterns in telegram_safe.py or handler registration")
    else:
        logger.info("All callback patterns validated successfully")


def main(dry_run: bool = False, test_lang: str = None) -> None:
    """
    Main function to run the Telegram bot

    Args:
        dry_run: If True, skip network calls and exit cleanly
        test_lang: Language to test in dry-run mode
    """
    # Get bot token from environment
    bot_token = os.getenv('BOT_TOKEN')

    # Handle dry-run mode
    if dry_run or not bot_token or bot_token in ["test_token", "dry"]:
        print("🛈 Bot initialised (dry-run mode) – no network calls")

        # Test i18n functionality if language specified
        if test_lang:
            if __name__ == '__main__':
                from src.bot.i18n import t
            else:
                from .i18n import t
            greeting = t("start_greeting", test_lang)
            print(f"🌐 Testing {test_lang}: {greeting}")

        return

    # Create Application instance with post_init hook for commands
    async def post_init(application):
        """Set bot commands after initialization"""
        await set_commands_safe(application)

    application = Application.builder().token(bot_token).post_init(post_init).build()

    # Register handlers (works in both test and production)
    register_handlers(application)

    # Validate callback patterns at startup
    validate_callback_patterns()

    # Start bot polling
    logger.info("Starting EduGuideBot...")
    application.run_polling(allowed_updates=["message", "callback_query"])


# Legacy function for backward compatibility
def _add_command_handlers(application: Application) -> None:
    """Legacy function - use register_handlers() instead."""
    register_handlers(application)


if __name__ == '__main__':
    """
    Entry point when running as script
    """
    parser = argparse.ArgumentParser(description='EduGuideBot Telegram Application')
    parser.add_argument('--dry-run', action='store_true',
                       help='Run in dry-run mode (no network calls)')
    parser.add_argument('--lang', choices=['kh', 'en'],
                       help='Test language in dry-run mode')
    args = parser.parse_args()

    try:
        main(dry_run=args.dry_run, test_lang=args.lang)
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot crashed: {e}")
        raise


# Export symbols for testing
__all__ = [
    "_add_command_handlers",
    "create_conversation_handler",
    "MessageHandler",
    "error_handler",
]
