"""
Core Utilities
Helper functions for type coercion and data handling
"""

from typing import Union, Any


def to_int(value: Any, default: int = 0) -> int:
    """
    Coerce strings like '0', '1', 'low', 'mid', 'high' → int.
    
    Args:
        value: Value to convert to int
        default: Default value if conversion fails
        
    Returns:
        int: Converted integer value
    """
    if isinstance(value, int):
        return value
    if isinstance(value, str):
        BUDGET_MAP = {"low": 0, "mid": 1, "high": 2}
        if value.lower() in BUDGET_MAP:
            return BUDGET_MAP[value.lower()]
        try:
            return int(value)
        except ValueError:
            return default
    return default


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    Safely convert value to float with fallback.
    
    Args:
        value: Value to convert to float
        default: Default value if conversion fails
        
    Returns:
        float: Converted float value
    """
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        try:
            return float(value)
        except ValueError:
            return default
    return default
