"""
Hybrid Recommender - 70% MCDA / 30% ML Blend Strategy
Combines MCDA baseline scoring with ML re-ranking using weighted blend
"""

import os
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from .mcda import score_vectorized, generate_reason
from .ml_reranker import MLReranker
from .data_loader import load_raw
from .feature_engineering import add_derived_features

# Import metrics for tracking
try:
    from ..infra.metrics import histogram
except ImportError:
    # Fallback if metrics not available
    def histogram(name: str, value: float) -> None:
        pass

# Blend weights for hybrid scoring
MCDA_WEIGHT = 0.7
ML_WEIGHT = 0.3


def _city_match(city_tag: str, preference: str) -> bool:
    """
    Check if a city matches the user's location preference

    Args:
        city_tag: City tag from programme data
        preference: User's location preference (pp/sr/btb/any)

    Returns:
        bool: True if city matches preference
    """
    if preference == "any":
        return True

    # Map preference codes to city names
    city_mapping = {
        "pp": ["ភ្នំពេញ", "phnom penh", "pp"],
        "sr": ["សៀមរាប", "siem reap", "sr"],
        "btb": ["បាត់ដំបង", "battambang", "btb"]
    }

    if preference not in city_mapping:
        return True  # Unknown preference, don't filter

    city_lower = city_tag.lower()
    return any(city_name.lower() in city_lower for city_name in city_mapping[preference])


def get_recommendations(user_answers: Dict[str, Any], programmes: List[Dict[str, Any]] = None, top_k: int = 5) -> List[Dict[str, Any]]:
    """
    Get hybrid recommendations using 70% MCDA / 30% ML blend strategy

    Args:
        user_answers: User's assessment answers
        programmes: Optional list of programmes to use (for testing)
        top_k: Number of recommendations to return

    Returns:
        List[Dict]: Top-k recommendations with hybrid scores
    """
    # Load and enhance program data (or use provided programmes for testing)
    try:
        raw_data = programmes or load_raw()
    except ImportError:
        return []                     # graceful fail on missing data-files

    enhanced_data = add_derived_features(raw_data) if programmes is None else programmes

    if not enhanced_data:
        return []

    # Apply hard city filtering BEFORE MCDA scoring
    location_preference = user_answers.get("location_preference", "any")
    if location_preference != "any":
        filtered_data = []
        for programme in enhanced_data:
            city_tag = programme.get("city", "")
            if _city_match(city_tag, location_preference):
                filtered_data.append(programme)

        # If no programmes match the city preference, fall back to all programmes
        if filtered_data:
            enhanced_data = filtered_data
        # else: keep all programmes as fallback

    # Calculate MCDA scores for all programs using vectorized operations
    mcda_scores = score_vectorized(user_answers, enhanced_data)

    # Get ML scores
    ml_scores = _get_ml_scores(user_answers, enhanced_data)

    # Ensure both are numpy arrays for calculation with fallback values
    mcda_scores = np.asarray(mcda_scores, dtype=float)
    ml_scores = np.asarray(ml_scores, dtype=float)

    # Add fallback values for missing data
    if len(mcda_scores) == 0:
        mcda_scores = np.full(len(enhanced_data), 0.5)
    if len(ml_scores) == 0:
        ml_scores = np.full(len(enhanced_data), 0.5)

    # Ensure arrays have same length
    if len(mcda_scores) != len(ml_scores):
        min_len = min(len(mcda_scores), len(ml_scores))
        mcda_scores = mcda_scores[:min_len]
        ml_scores = ml_scores[:min_len]
        enhanced_data = enhanced_data[:min_len]

    # Calculate hybrid scores using weighted blend: (MCDA * 0.7) + (ML * 0.3)
    hybrid_scores = MCDA_WEIGHT * mcda_scores + ML_WEIGHT * ml_scores

    # Create program records with all scores
    scored_programs = []
    for i, programme in enumerate(enhanced_data):
        program_copy = programme.copy()
        program_copy['mcda_score'] = float(mcda_scores[i])
        program_copy['ml_score'] = float(ml_scores[i])
        program_copy['hybrid_score'] = float(hybrid_scores[i])
        program_copy['mcda_reason'] = generate_reason(user_answers, programme, mcda_scores[i])
        scored_programs.append(program_copy)

    # Sort by hybrid score descending
    scored_programs.sort(key=lambda x: x['hybrid_score'], reverse=True)

    # Return top-k results
    top_programs = scored_programs[:top_k]

    # Log metrics
    if top_programs:
        avg_hybrid_score = sum(prog['hybrid_score'] for prog in top_programs) / len(top_programs)
        avg_mcda_score = sum(prog['mcda_score'] for prog in top_programs) / len(top_programs)
        avg_ml_score = sum(prog['ml_score'] for prog in top_programs) / len(top_programs)

        histogram("hybrid_score", avg_hybrid_score)
        histogram("mcda_component", avg_mcda_score)
        histogram("ml_component", avg_ml_score)

    return top_programs


def _get_ml_scores(user_answers: Dict[str, Any], programmes: List[Dict[str, Any]]) -> np.ndarray:
    """
    Get ML scores for programs with fallback handling

    Args:
        user_answers: User's assessment answers
        programmes: List of program data

    Returns:
        np.ndarray: Array of ML scores (0.0 to 1.0)
    """
    n_programs = len(programmes)

    # ENV validation
    try:
        ml_version = int(os.getenv("ML_VERSION", "1"))
    except ValueError:
        return np.full(len(programmes), 0.5)

    env = ml_version

    try:
        # Map version to model file with fallback chain 3 → 2 → 1
        model_paths = {
            3: "models/rf_ranker_v3.joblib",
            2: "models/rf_ranker_v2.joblib",
            1: "models/rf_ranker.joblib"
        }

        # Try to load preferred version, fallback if not found
        model_path = None
        for version in [env, 2, 1]:
            if version in model_paths:
                candidate_path = model_paths[version]
                if Path(candidate_path).exists():
                    model_path = candidate_path
                    break

        if not model_path:
            raise FileNotFoundError("No ML model found")

        # Load ML reranker and get scores
        ml_reranker = MLReranker(model_path)

        # Get ML predictions for all programs
        ml_predictions = []
        for programme in programmes:
            try:
                # Extract features and predict
                from .ml_reranker import extract_features
                features = extract_features(user_answers, programme)

                # Predict score (assuming model returns 0-1 range)
                if ml_reranker.model is not None and ml_reranker.scaler is not None:
                    features_scaled = ml_reranker.scaler.transform([features])
                    prediction = ml_reranker.model.predict(features_scaled)[0]
                    # Ensure score is in 0-1 range
                    ml_score = max(0.0, min(1.0, prediction))
                else:
                    ml_score = 0.5  # Neutral score if model not loaded

                ml_predictions.append(ml_score)

            except Exception:
                ml_predictions.append(0.5)  # Fallback score

        return np.array(ml_predictions)

    except Exception as e:
        print(f"ML scoring failed, using neutral scores: {e}")
        # Return neutral scores (0.5) for all programs
        return np.full(n_programs, 0.5)
