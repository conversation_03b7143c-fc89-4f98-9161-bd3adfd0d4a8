"""
Feature Schema for ML Re-ranker
Defines feature extraction for RandomForest training
Cold-start resilient: handles missing/unknown values gracefully
"""

import numpy as np
from typing import Dict, List, Any

# Dataset medians for imputation (calculated from full dataset)
DATASET_MEDIANS = {
    'employment_rate': 0.75,  # 75%
    'tuition_usd': 1500.0,    # $1500
    'mcda_score': 0.5         # 0.5
}


FEATURES = [
    "mcda_score",          # baseline score (float)
    "location_pref==prog", # 1/0
    "budget_bracket_diff", # |user - programme| (int 0-2)
    "field_match",         # 1/0
    "has_scholarship",     # 1/0
    "employment_rate",     # numeric 0-1
    "tuition_usd",         # raw number (scaled later)
    # New enriched features
    "field_tag_stem",      # one-hot for STEM
    "field_tag_business",  # one-hot for Business
    "field_tag_health",    # one-hot for Health
    "field_tag_arts",      # one-hot for Arts & Humanities
    "field_tag_social",    # one-hot for Social Sciences
    "field_tag_education", # one-hot for Education
    "language_kh",         # bool: Khmer language available
    "scholarship_available", # bool: scholarship available
    "tuition_usd_log",     # log1p transform of tuition
]


def extract_features(user_answers: Dict[str, Any], programme_dict: Dict[str, Any]) -> List[float]:
    """
    Extract feature vector for ML training/prediction
    Cold-start resilient: handles missing/unknown values gracefully

    Args:
        user_answers: User's assessment answers
        programme_dict: Program data dictionary

    Returns:
        List[float]: Feature vector matching FEATURES schema
    """
    features = []

    # 1. MCDA score (computed separately and passed in programme_dict)
    mcda_score = programme_dict.get('mcda_score')
    if mcda_score is None:
        mcda_score = DATASET_MEDIANS['mcda_score']
    features.append(float(mcda_score))
    
    # 2. Location preference match (1/0)
    user_location = user_answers.get('location_preference', '')
    program_city = programme_dict.get('city', '')
    
    location_match = 0.0
    if user_location == 'pp' and 'ភ្នំពេញ' in program_city:
        location_match = 1.0
    elif user_location == 'sr' and 'សៀមរាប' in program_city:
        location_match = 1.0
    elif user_location == 'btb' and 'បាត់ដំបង' in program_city:
        location_match = 1.0
    elif user_location == 'any':
        location_match = 0.5
    
    features.append(location_match)
    
    # 3. Budget bracket difference |user - programme| (0-2)
    user_budget = user_answers.get('budget_range', 'mid')
    program_bracket = programme_dict.get('tuition_bracket', 'Unknown')
    
    # Map to numeric values
    budget_map = {'low': 0, 'mid': 1, 'high': 2, 'flex': 1}
    program_map = {'Low': 0, 'Medium': 1, 'High': 2, 'Unknown': 1}
    
    user_budget_num = budget_map.get(user_budget, 1)
    program_budget_num = program_map.get(program_bracket, 1)
    
    budget_diff = abs(user_budget_num - program_budget_num)
    features.append(float(budget_diff))
    
    # 4. Field match (1/0)
    user_field = user_answers.get('field_of_interest', '')
    program_field = programme_dict.get('field_tag', 'Other')
    
    field_match = 0.0
    if user_field == 'stem' and program_field == 'STEM':
        field_match = 1.0
    elif user_field == 'business' and program_field == 'Business':
        field_match = 1.0
    elif user_field == 'health' and program_field == 'Health':
        field_match = 1.0
    elif user_field == 'arts' and program_field == 'Arts & Humanities':
        field_match = 1.0
    elif user_field == 'social' and program_field == 'Social Sciences':
        field_match = 1.0
    elif user_field == 'education' and program_field == 'Education':
        field_match = 1.0
    
    features.append(field_match)
    
    # 5. Has scholarship (1/0)
    has_scholarship = 1.0 if programme_dict.get('has_scholarship', False) else 0.0
    features.append(has_scholarship)
    
    # 6. Employment rate (0-1) with cold-start fallback
    employment_rate_str = programme_dict.get('employment_rate', '')
    employment_rate = DATASET_MEDIANS['employment_rate']  # Default to median
    if employment_rate_str:
        try:
            # Extract percentage and convert to 0-1
            rate_num = float(str(employment_rate_str).replace('%', ''))
            if 0 <= rate_num <= 100:
                employment_rate = rate_num / 100.0
        except (ValueError, TypeError):
            pass  # Keep median default

    features.append(employment_rate)
    
    # 7. Tuition USD (raw number, will be scaled later) with cold-start fallback
    tuition_str = programme_dict.get('tuition_fees_usd', '')
    tuition_usd = DATASET_MEDIANS['tuition_usd']  # Default to median
    if tuition_str:
        try:
            # Extract numeric value from string like "$1,200" or "1200"
            tuition_clean = str(tuition_str).replace('$', '').replace(',', '')
            tuition_num = float(tuition_clean)
            if tuition_num > 0:
                tuition_usd = tuition_num
        except (ValueError, TypeError):
            pass  # Keep median default

    features.append(tuition_usd)

    # 8-13. Field tag one-hot encoding
    program_field = programme_dict.get('field_tag', 'Other')
    field_tags = ['STEM', 'Business', 'Health', 'Arts & Humanities', 'Social Sciences', 'Education']
    feature_names = ['field_tag_stem', 'field_tag_business', 'field_tag_health',
                    'field_tag_arts', 'field_tag_social', 'field_tag_education']

    for tag in field_tags:
        features.append(1.0 if program_field == tag else 0.0)

    # 14. Language Khmer available
    language_instruction = programme_dict.get('language_of_instruction', [])
    if language_instruction is None:
        language_instruction = []
    elif isinstance(language_instruction, str):
        language_instruction = [language_instruction]

    language_kh = 0.0
    if language_instruction and any('ខ្មែរ' in str(lang) or 'Khmer' in str(lang) for lang in language_instruction):
        language_kh = 1.0
    features.append(language_kh)

    # 15. Scholarship available (same as has_scholarship but explicit)
    scholarship_available = 1.0 if programme_dict.get('has_scholarship', False) else 0.0
    features.append(scholarship_available)

    # 16. Tuition USD log transform (cold-start safe)
    tuition_usd_log = np.log1p(max(tuition_usd, 0))  # log(1 + x), ensure non-negative
    features.append(float(tuition_usd_log))

    return features
