#!/usr/bin/env python3
"""
MCDA Demo CLI
Demonstrates the MCDA recommendation system
"""

import argparse
import json
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.core.mcda import recommender_mcda
from src.core.data_loader import load_raw
from src.core.feature_engineering import add_derived_features


def create_sample_answers() -> dict:
    """Create sample user answers for testing"""
    return {
        'location_preference': 'phnom_penh',
        'budget_range': 'medium',
        'field_of_interest': 'stem',
        'career_aspiration': 'engineer',
        'scholarship_need': 'yes'
    }


def main():
    """Main demo function"""
    parser = argparse.ArgumentParser(description='MCDA Recommendation Demo')
    parser.add_argument('--answers', help='JSON file with user answers')
    parser.add_argument('--top-n', type=int, default=5, help='Number of recommendations')
    
    args = parser.parse_args()
    
    # Load user answers
    if args.answers and Path(args.answers).exists():
        with open(args.answers, 'r') as f:
            user_answers = json.load(f)
    else:
        print("Using sample answers...")
        user_answers = create_sample_answers()
    
    print(f"User answers: {user_answers}")
    print()
    
    # Load program data
    print("Loading program data...")
    raw_data = load_raw()
    enhanced_data = add_derived_features(raw_data)
    print(f"Loaded {len(enhanced_data)} programs")
    print()
    
    # Get recommendations
    print("Generating recommendations...")
    recommendations = recommender_mcda(user_answers, enhanced_data, args.top_n)
    
    # Display results
    print(f"\n🎓 Top {len(recommendations)} Recommendations:")
    print("=" * 60)
    
    for i, (program, score, reason) in enumerate(recommendations, 1):
        print(f"\n{i}. {program.get('major_name_en', 'Unknown Major')}")
        print(f"   📍 {program.get('university_name_en', 'Unknown University')}")
        print(f"   🏛️ {program.get('city', 'Unknown City')}")
        print(f"   💰 {program.get('tuition_fees_usd', 'N/A')} USD")
        print(f"   📊 Score: {score:.3f}")
        print(f"   💡 {reason}")
        
        # Additional details
        if program.get('field_tag'):
            print(f"   🔬 Field: {program.get('field_tag')}")
        if program.get('employment_rate'):
            print(f"   💼 Employment Rate: {program.get('employment_rate')}")
        if program.get('has_scholarship'):
            print(f"   🎓 Scholarship Available")
    
    print("\n" + "=" * 60)
    print("Demo completed!")


if __name__ == '__main__':
    main()
