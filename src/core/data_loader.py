"""
Data Loader Module
Loads JSON university files into list[dict] format
"""

import json
import os
from functools import lru_cache
from pathlib import Path
from typing import List, Dict, Any


@lru_cache(maxsize=1)
def load_raw(data_dir: str = None) -> List[Dict[str, Any]]:
    """
    Load all raw university JSON files from data/raw/ directory

    Args:
        data_dir: Optional custom data directory path (for testing)

    Returns:
        List[Dict]: Combined list of all university programs
    """
    # Get project root directory (2 levels up from this file)
    root = Path(__file__).parents[2]
    all_programs = []

    # Use custom data directory if provided (for testing)
    if data_dir:
        search_path = Path(data_dir)
        json_files = list(search_path.glob("**/*.json"))
    else:
        # Recursively find all .json files in default location
        json_files = list(root.glob("data/raw/**/*.json"))

    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)

            # Extract university info and majors
            if 'university' in file_data and 'majors' in file_data:
                university = file_data['university']
                majors = file_data['majors']

                # Combine university info with each major
                for major in majors:
                    program = {
                        # University information
                        'university_id': university.get('id', ''),
                        'university_name_kh': university.get('name_kh', ''),
                        'university_name_en': university.get('name_en', ''),
                        'university_type': university.get('type', ''),
                        'city': university.get('location', {}).get('city', ''),
                        'website': university.get('website', ''),

                        # Major information
                        'major_id': major.get('major_info', {}).get('id', ''),
                        'major_name_kh': major.get('major_info', {}).get('name_kh', ''),
                        'major_name_en': major.get('major_info', {}).get('name_en', ''),
                        'degree_level_kh': major.get('major_info', {}).get('degree_level_kh', ''),
                        'degree_level_en': major.get('major_info', {}).get('degree_level_en', ''),
                        'study_duration_kh': major.get('major_info', {}).get('study_duration_kh', ''),
                        'language_of_instruction': major.get('major_info', {}).get('language_of_instruction', []),

                        # Faculty information
                        'faculty_name_kh': major.get('faculty', {}).get('name_kh', ''),
                        'faculty_name_en': major.get('faculty', {}).get('name_en', ''),

                        # Financial information
                        'tuition_fees_usd': major.get('practical_information', {}).get('tuition_fees_usd', ''),
                        'tuition_fees_khr': major.get('practical_information', {}).get('tuition_fees_khr', ''),
                        'scholarship_availability': major.get('practical_information', {}).get('scholarship_availability', False),

                        # Career information
                        'potential_careers_kh': major.get('career_prospects', {}).get('potential_careers_kh', []),
                        'potential_careers_en': major.get('career_prospects', {}).get('potential_careers_en', []),
                        'employment_rate': major.get('career_prospects', {}).get('employment_statistics', {}).get('employment_rate', ''),
                        'average_starting_salary': major.get('career_prospects', {}).get('employment_statistics', {}).get('average_starting_salary', ''),

                        # Academic information
                        'total_credits': major.get('program_details', {}).get('total_credits', 0) or 0,
                        'minimum_gpa': major.get('academic_requirements', {}).get('minimum_gpa', 0.0) or 0.0,

                        # Source tracking
                        'source_file': str(json_file),
                    }

                    if validate_program_data(program):
                        all_programs.append(program)

        except Exception as e:
            print(f"Error loading {json_file}: {e}")
            continue

    return all_programs


def validate_program_data(program: Dict[str, Any]) -> bool:
    """
    Validate that a program dict has required fields

    Args:
        program: Program dictionary to validate

    Returns:
        bool: True if valid, False otherwise
    """
    # Fast-fail on obvious garbage
    if not isinstance(program, dict):
        return False

    required_fields = [
        'university_name_kh', 'university_name_en',
        'major_name_kh', 'major_name_en',
        'city'
    ]

    # Check for required fields
    for field in required_fields:
        # guard against None / "" / 0
        if not (programme_field := program.get(field)):
            return False

    # Additional validation
    if not program.get('university_id') and not program.get('major_id'):
        return False

    return True
