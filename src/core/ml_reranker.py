"""
ML Re-ranker Module
RandomForest-based machine learning model for program ranking
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
import joblib

from .feature_schema import extract_features, FEATURES


class MLReranker:
    """
    Machine Learning Re-ranker using RandomForest
    """

    def __init__(self, model_path: str | None = None):
        """
        Initialize ML reranker

        Args:
            model_path: Path to saved model file
        """
        self.model_path = model_path
        self.model = None
        self.scaler = None

        # Load model if path provided and file exists
        if model_path and Path(model_path).exists():
            self._load_model()

    def _load_model(self) -> None:
        """Load trained model and scaler from file"""
        try:
            model_data = joblib.load(self.model_path)
            self.model = model_data['model']
            self.scaler = model_data['scaler']
        except Exception as e:
            print(f"Warning: Could not load model from {self.model_path}: {e}")
            self.model = None
            self.scaler = None

    def train(self, dataset_csv: str, model_path: str, params: dict = None) -> None:
        """
        Train RandomForest model on synthetic dataset

        Args:
            dataset_csv: Path to training CSV file
            model_path: Path to save trained model
            params: Optional hyperparameters dict
        """
        # Load training data
        df = pd.read_csv(dataset_csv)

        # Extract features and labels
        X = df[FEATURES].values
        y = df['label'].values

        # Scale features (except MCDA score which is already 0-1)
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)

        # Determine model type and parameters
        if params and 'model_type' in params:
            model_type = params['model_type']
        else:
            model_type = 'RandomForest'  # Default

        # Train model with custom or default parameters
        if params:
            model_params = params.copy()
            model_params.pop('model_type', None)  # Remove model_type from params
            model_params['random_state'] = 42

            if model_type != 'GradientBoosting':
                model_params['n_jobs'] = -1
        else:
            model_params = {
                'n_estimators': 150,
                'max_depth': 10,
                'random_state': 42,
                'n_jobs': -1
            }

        # Create appropriate model
        if model_type == 'ExtraTrees':
            self.model = ExtraTreesRegressor(**model_params)
        elif model_type == 'GradientBoosting':
            self.model = GradientBoostingRegressor(**model_params)
        else:  # Default to RandomForest
            self.model = RandomForestRegressor(**model_params)

        self.model.fit(X_scaled, y)

        # Save model and scaler
        os.makedirs(Path(model_path).parent, exist_ok=True)
        joblib.dump({
            'model': self.model,
            'scaler': self.scaler
        }, model_path)

        self.model_path = model_path
        print(f"Model trained and saved to {model_path}")

    def rank(self, user_answers: dict, programmes: list[dict], top_k: int = 5) -> list[dict]:
        """
        Rank programmes using trained ML model

        Args:
            user_answers: User's assessment answers
            programmes: List of program dictionaries
            top_k: Number of top programs to return

        Returns:
            List[dict]: Top-k ranked programs with ml_score added
        """
        if self.model is None or self.scaler is None:
            # Fallback: return original programs with dummy ML scores
            for prog in programmes:
                prog['ml_score'] = 0.5
            return programmes[:top_k]

        # Extract features for each program
        features_list = []
        for prog in programmes:
            features = extract_features(user_answers, prog)
            features_list.append(features)

        # Convert to numpy array and scale
        X = np.array(features_list)
        try:
            X_scaled = self.scaler.transform(X)
            ml_scores = self.model.predict(X_scaled)
        except Exception:
            # any failure ⇒ neutral .5
            ml_scores = np.full(len(programmes), 0.5)

        # Add ML scores to programs and sort
        ranked_programmes = []
        for i, prog in enumerate(programmes):
            prog_copy = prog.copy()
            prog_copy['ml_score'] = float(ml_scores[i])
            # if earlier MCDA step forgot to add one
            prog_copy.setdefault("mcda_score", 0.5)
            ranked_programmes.append(prog_copy)

        # Sort by ML score descending
        ranked_programmes.sort(key=lambda x: x['ml_score'], reverse=True)

        return ranked_programmes[:top_k]
