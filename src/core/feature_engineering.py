"""
Feature Engineering Module
Derives additional features from raw university data
"""

from typing import List, Dict, Any


def add_derived_features(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Add derived features to program data

    Args:
        data: List of program dictionaries

    Returns:
        List[Dict]: Enhanced data with derived features

    Derived features to add:
    - field_tag: Categorized field (STEM, Business, Arts, etc.)
    - tuition_bracket: Low/Medium/High based on tuition fees
    - has_scholarship: Boolean indicating scholarship availability
    """
    enhanced_data = []

    for program in data:
        # Create a copy to avoid modifying original
        enhanced_program = program.copy()

        # Add field_tag
        enhanced_program['field_tag'] = categorize_field(
            program.get('major_name_en', ''),
            program.get('faculty_name_en', '')
        )

        # Add tuition_bracket
        tuition_usd = program.get('tuition_fees_usd', '')
        try:
            tuition_amount = float(str(tuition_usd).replace('$', '').replace(',', '')) if tuition_usd else 0
        except (ValueError, TypeError):
            tuition_amount = 0
        enhanced_program['tuition_bracket'] = calculate_tuition_bracket(tuition_amount)

        # Add has_scholarship
        enhanced_program['has_scholarship'] = bool(program.get('scholarship_availability', False))

        # Add location flags
        enhanced_program['is_phnom_penh'] = program.get("city") in ("ភ្នំពេញ", "Phnom Penh")

        enhanced_data.append(enhanced_program)

    return enhanced_data


def categorize_field(major_name: str, faculty_name: str = None) -> str:
    """
    Categorize academic field based on major name and faculty

    Args:
        major_name: Name of the major/program
        faculty_name: Optional faculty name for additional context

    Returns:
        str: Field category (STEM, Business, Arts, etc.)
    """
    major_lower = major_name.lower() if major_name else ""
    faculty_lower = faculty_name.lower() if faculty_name else ""

    # STEM fields
    stem_keywords = [
        'engineering', 'computer', 'information technology', 'it', 'science',
        'mathematics', 'physics', 'chemistry', 'biology', 'technology',
        'software', 'data', 'artificial intelligence', 'ai', 'robotics'
    ]

    # Business fields
    business_keywords = [
        'business', 'management', 'finance', 'accounting', 'economics',
        'marketing', 'administration', 'commerce', 'entrepreneurship',
        'banking', 'investment'
    ]

    # Arts & Humanities
    arts_keywords = [
        'art', 'literature', 'language', 'english', 'khmer', 'history',
        'philosophy', 'music', 'design', 'creative', 'humanities',
        'cultural', 'linguistics'
    ]

    # Social Sciences
    social_keywords = [
        'psychology', 'sociology', 'political', 'international relations',
        'social', 'anthropology', 'geography', 'communication', 'media',
        'journalism', 'law', 'legal'
    ]

    # Health & Medicine
    health_keywords = [
        'medicine', 'medical', 'health', 'nursing', 'pharmacy', 'dental',
        'public health', 'nutrition', 'therapy'
    ]

    # Education
    education_keywords = [
        'education', 'teaching', 'pedagogy', 'curriculum'
    ]

    # Check categories in order of specificity
    text_to_check = f"{major_lower} {faculty_lower}"

    if any(keyword in text_to_check for keyword in stem_keywords):
        return "STEM"
    elif any(keyword in text_to_check for keyword in business_keywords):
        return "Business"
    elif any(keyword in text_to_check for keyword in health_keywords):
        return "Health"
    elif any(keyword in text_to_check for keyword in education_keywords):
        return "Education"
    elif any(keyword in text_to_check for keyword in social_keywords):
        return "Social Sciences"
    elif any(keyword in text_to_check for keyword in arts_keywords):
        return "Arts & Humanities"
    else:
        return "Other"


def calculate_tuition_bracket(tuition_fee: float) -> str:
    """
    Calculate tuition bracket based on fee amount

    Args:
        tuition_fee: Annual tuition fee in USD

    Returns:
        str: Tuition bracket (Low/Medium/High)
    """
    if tuition_fee <= 0:
        return "Unknown"
    elif tuition_fee < 500:
        return "Low"
    elif tuition_fee < 1500:
        return "Medium"
    else:
        return "High"
