version: '3.8'

services:
  bot:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: eduguidebot
    env_file:
      - .env
    environment:
      - BOT_TOKEN=${BOT_TOKEN:-dry}
      - ML_VERSION=${ML_VERSION:-1}
    profiles:
      - staging
    healthcheck:
      test: ["CMD", "python", "src/bot/app.py", "--dry-run"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    volumes:
      # Optional: mount logs directory
      - ./logs:/app/logs
    networks:
      - eduguidebot-network

  metrics:
    build: .
    command: python -m src.infra.exporter
    environment:
      - METRICS_ENABLED=1
    ports:
      - "8000:8000"
    depends_on:
      - bot
    profiles:
      - staging
    volumes:
      - ./logs:/app/logs
    networks:
      - eduguidebot-network

networks:
  eduguidebot-network:
    driver: bridge
