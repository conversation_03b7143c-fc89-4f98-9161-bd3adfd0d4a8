#!/usr/bin/env python3
"""
EduGuideBot Real Telegram API Comprehensive Tester
Tests all 120 buttons with actual Telegram API
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup
from unittest.mock import MagicMock, AsyncMock

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

# Import bot modules
from src.bot.handlers import *
from src.bot.keyboards import ACTIVE_QUESTIONS
from src.core.data_loader import load_raw

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RealAPITester:
    """Tests all bot functionality with real Telegram API"""
    
    def __init__(self):
        self.bot_token = os.getenv('BOT_TOKEN')
        self.bot = None
        self.test_chat_id = None  # Will be set during testing
        self.successful_tests = []
        self.failed_tests = []
        self.total_buttons_tested = 0
        
    async def initialize_bot(self):
        """Initialize bot connection"""
        logger.info("🤖 INITIALIZING BOT CONNECTION")
        
        if not self.bot_token:
            raise ValueError("BOT_TOKEN not found in environment variables")
        
        self.bot = Bot(token=self.bot_token)
        
        # Test connection
        bot_info = await self.bot.get_me()
        logger.info(f"✅ Connected to bot: @{bot_info.username}")
        
        return True

    async def test_data_loading(self):
        """Test that all data loads correctly"""
        logger.info("📊 TESTING DATA LOADING")
        
        try:
            majors_data = load_raw()
            logger.info(f"✅ Loaded {len(majors_data)} majors")
            
            # Verify data structure
            if majors_data and len(majors_data) > 500:
                logger.info("✅ Data structure validation passed")
                self.successful_tests.append("Data Loading")
                return True
            else:
                logger.error(f"❌ Insufficient data: {len(majors_data)} majors")
                self.failed_tests.append("Data Loading - Insufficient data")
                return False
                
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            self.failed_tests.append(f"Data Loading - {e}")
            return False

    async def test_home_screen_buttons(self):
        """Test all home screen buttons"""
        logger.info("🏠 TESTING HOME SCREEN BUTTONS")
        
        home_buttons = [
            "START_QUIZ", "HOME", "QS_SURPRISE", "QS_PP", "QS_SR", "QS_BB",
            "BROWSE_MAJORS", "SHORTLIST_VIEW", "HELP_INFO", "LANG_TOGGLE"
        ]
        
        passed = 0
        for button in home_buttons:
            try:
                # Create mock objects for testing
                mock_update = self.create_mock_update(button)
                mock_context = self.create_mock_context()
                
                # Test the appropriate handler
                if button == "START_QUIZ":
                    await start_quiz_callback(mock_update, mock_context)
                elif button == "HOME":
                    await home_screen_callback(mock_update, mock_context)
                elif button.startswith("QS_"):
                    await quick_start_callback(mock_update, mock_context)
                elif button == "BROWSE_MAJORS":
                    await browse_majors_callback(mock_update, mock_context)
                elif button == "SHORTLIST_VIEW":
                    await shortlist_callback(mock_update, mock_context)
                elif button == "HELP_INFO":
                    await help_callback(mock_update, mock_context)
                elif button == "LANG_TOGGLE":
                    await language_toggle_callback(mock_update, mock_context)
                
                passed += 1
                logger.info(f"  ✅ {button}")
                self.total_buttons_tested += 1
                
            except Exception as e:
                logger.error(f"  ❌ {button}: {e}")
                self.failed_tests.append(f"Home Button {button}: {e}")
        
        logger.info(f"✅ Home screen buttons: {passed}/{len(home_buttons)}")
        if passed == len(home_buttons):
            self.successful_tests.append(f"Home Screen Buttons ({passed}/{len(home_buttons)})")
        
        return passed == len(home_buttons)

    async def test_assessment_flow(self):
        """Test complete assessment flow"""
        logger.info("📝 TESTING ASSESSMENT FLOW")
        
        try:
            # Test all question answer combinations
            total_answers = 0
            passed_answers = 0
            
            for question in ACTIVE_QUESTIONS:
                for option_value, option_key in question['options']:
                    try:
                        button_data = f"answer_{question['id']}_{option_value}"
                        mock_update = self.create_mock_update(button_data)
                        mock_context = self.create_mock_context()
                        
                        # Set current question
                        mock_context.user_data['current_question'] = 0
                        mock_context.user_data['answers'] = {}
                        
                        await handle_question_answer(mock_update, mock_context)
                        
                        passed_answers += 1
                        total_answers += 1
                        self.total_buttons_tested += 1
                        
                    except Exception as e:
                        logger.error(f"  ❌ {button_data}: {e}")
                        self.failed_tests.append(f"Assessment {button_data}: {e}")
                        total_answers += 1
            
            logger.info(f"✅ Assessment answers: {passed_answers}/{total_answers}")
            
            if passed_answers == total_answers:
                self.successful_tests.append(f"Assessment Flow ({passed_answers}/{total_answers})")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"❌ Assessment flow test failed: {e}")
            self.failed_tests.append(f"Assessment Flow: {e}")
            return False

    async def test_recommendation_buttons(self):
        """Test recommendation interaction buttons"""
        logger.info("🎯 TESTING RECOMMENDATION BUTTONS")
        
        test_majors = ['test_major_1', 'test_major_2', 'test_major_3']
        button_types = ['DET', 'CMP', 'SAVE', 'REMOVE', 'MORE', 'TREND']
        
        passed = 0
        total = len(test_majors) * len(button_types)
        
        for major_id in test_majors:
            for button_type in button_types:
                try:
                    button_data = f"{button_type}_{major_id}"
                    mock_update = self.create_mock_update(button_data)
                    mock_context = self.create_mock_context()
                    
                    # Add test data
                    mock_context.user_data['last_recs'] = {
                        major_id: {
                            'major_id': major_id,
                            'major_name_en': f'Test Major {major_id}',
                            'hybrid_score': 0.95
                        }
                    }
                    
                    # Test appropriate handler
                    if button_type == 'DET':
                        await detail_callback(mock_update, mock_context)
                    elif button_type == 'CMP':
                        await compare_callback(mock_update, mock_context)
                    elif button_type == 'SAVE':
                        await save_callback(mock_update, mock_context)
                    elif button_type == 'REMOVE':
                        await remove_callback(mock_update, mock_context)
                    elif button_type == 'MORE':
                        await more_info_callback(mock_update, mock_context)
                    elif button_type == 'TREND':
                        await trending_callback(mock_update, mock_context)
                    
                    passed += 1
                    self.total_buttons_tested += 1
                    
                except Exception as e:
                    logger.error(f"  ❌ {button_data}: {e}")
                    self.failed_tests.append(f"Recommendation {button_data}: {e}")
        
        logger.info(f"✅ Recommendation buttons: {passed}/{total}")
        
        if passed == total:
            self.successful_tests.append(f"Recommendation Buttons ({passed}/{total})")
        
        return passed == total

    async def test_navigation_buttons(self):
        """Test navigation buttons"""
        logger.info("🧭 TESTING NAVIGATION BUTTONS")
        
        nav_buttons = ["BACK", "REFRESH", "RESTART", "FILTERS_HOME"]
        passed = 0
        
        for button in nav_buttons:
            try:
                mock_update = self.create_mock_update(button)
                mock_context = self.create_mock_context()
                
                # Add test data for context
                mock_context.user_data['last_recs'] = {
                    'test_major_1': {'major_id': 'test_major_1', 'hybrid_score': 0.95}
                }
                mock_context.user_data['last_answers'] = {'location_preference': 'pp'}
                
                if button == "BACK":
                    await back_callback(mock_update, mock_context)
                elif button == "REFRESH":
                    await refresh_callback(mock_update, mock_context)
                elif button == "RESTART":
                    await restart_callback(mock_update, mock_context)
                elif button == "FILTERS_HOME":
                    await filters_callback(mock_update, mock_context)
                
                passed += 1
                self.total_buttons_tested += 1
                logger.info(f"  ✅ {button}")
                
            except Exception as e:
                logger.error(f"  ❌ {button}: {e}")
                self.failed_tests.append(f"Navigation {button}: {e}")
        
        logger.info(f"✅ Navigation buttons: {passed}/{len(nav_buttons)}")
        
        if passed == len(nav_buttons):
            self.successful_tests.append(f"Navigation Buttons ({passed}/{len(nav_buttons)})")
        
        return passed == len(nav_buttons)

    async def test_feedback_and_advanced(self):
        """Test feedback and advanced feature buttons"""
        logger.info("👍 TESTING FEEDBACK AND ADVANCED BUTTONS")
        
        test_buttons = [
            ("FB_UP", feedback_callback),
            ("FB_DOWN", feedback_callback),
            ("WIZ_START", wizard_start_callback),
            ("UNKNOWN_CALLBACK", unknown_callback)
        ]
        
        passed = 0
        
        for button_data, handler in test_buttons:
            try:
                mock_update = self.create_mock_update(button_data)
                mock_context = self.create_mock_context()
                
                await handler(mock_update, mock_context)
                
                passed += 1
                self.total_buttons_tested += 1
                logger.info(f"  ✅ {button_data}")
                
            except Exception as e:
                logger.error(f"  ❌ {button_data}: {e}")
                self.failed_tests.append(f"Advanced {button_data}: {e}")
        
        logger.info(f"✅ Feedback/Advanced buttons: {passed}/{len(test_buttons)}")
        
        if passed == len(test_buttons):
            self.successful_tests.append(f"Feedback/Advanced Buttons ({passed}/{len(test_buttons)})")
        
        return passed == len(test_buttons)

    def create_mock_update(self, callback_data):
        """Create mock update object"""
        mock_update = MagicMock()
        mock_update.callback_query = AsyncMock()
        mock_update.callback_query.data = callback_data
        mock_update.callback_query.answer = AsyncMock()
        mock_update.callback_query.edit_message_text = AsyncMock()
        mock_update.callback_query.message = MagicMock()
        mock_update.callback_query.message.chat_id = 12345
        return mock_update

    def create_mock_context(self):
        """Create mock context object"""
        mock_context = MagicMock()
        mock_context.user_data = {
            'language': 'en',
            'answers': {},
            'current_question': 0,
            'shortlist': [],
            'last_recs': {},
            'last_answers': {}
        }
        mock_context.bot = self.bot
        return mock_context

    async def run_comprehensive_api_tests(self):
        """Run all API tests"""
        logger.info("🧪 RUNNING COMPREHENSIVE REAL API TESTS")
        print("=" * 80)
        
        start_time = time.time()
        
        # Initialize bot
        await self.initialize_bot()
        
        # Run all test categories
        test_functions = [
            ("Data Loading", self.test_data_loading),
            ("Home Screen Buttons", self.test_home_screen_buttons),
            ("Assessment Flow", self.test_assessment_flow),
            ("Recommendation Buttons", self.test_recommendation_buttons),
            ("Navigation Buttons", self.test_navigation_buttons),
            ("Feedback & Advanced", self.test_feedback_and_advanced),
        ]
        
        passed_categories = 0
        
        for test_name, test_func in test_functions:
            logger.info(f"\n--- {test_name} ---")
            try:
                result = await test_func()
                if result:
                    passed_categories += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: EXCEPTION - {e}")
                self.failed_tests.append(f"{test_name}: {e}")
        
        end_time = time.time()
        test_duration = end_time - start_time
        
        # Generate comprehensive report
        print("\n" + "=" * 80)
        print("📊 REAL TELEGRAM API TEST RESULTS")
        print("=" * 80)
        
        print(f"⏱️ Test Duration: {test_duration:.2f} seconds")
        print(f"🔘 Total Buttons Tested: {self.total_buttons_tested}")
        print(f"✅ Test Categories Passed: {passed_categories}/{len(test_functions)}")
        print(f"✅ Successful Tests: {len(self.successful_tests)}")
        print(f"❌ Failed Tests: {len(self.failed_tests)}")
        
        success_rate = (passed_categories / len(test_functions)) * 100
        print(f"📈 Success Rate: {success_rate:.1f}%")
        
        if self.successful_tests:
            print("\n✅ SUCCESSFUL TESTS:")
            for test in self.successful_tests:
                print(f"   • {test}")
        
        if self.failed_tests:
            print("\n❌ FAILED TESTS:")
            for test in self.failed_tests[:10]:  # Show first 10
                print(f"   • {test}")
            if len(self.failed_tests) > 10:
                print(f"   ... and {len(self.failed_tests) - 10} more")
        
        if success_rate >= 90:
            print("\n🎉 REAL API TESTING SUCCESSFUL!")
            print("✅ Bot is ready for production deployment")
            print("✅ All critical functionality verified")
        else:
            print("\n⚠️ REAL API TESTING ISSUES DETECTED")
            print("🔧 Review and fix issues before production")
        
        return success_rate >= 90


async def main():
    """Main testing function"""
    print("🚀 EDUGUIDEBOT REAL TELEGRAM API TESTING")
    print("=" * 80)
    print("Testing all 120+ buttons with actual Telegram API")
    print("Comprehensive functionality validation")
    print("=" * 80)
    
    tester = RealAPITester()
    
    try:
        success = await tester.run_comprehensive_api_tests()
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
