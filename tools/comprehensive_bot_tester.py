#!/usr/bin/env python3
"""
EduGuideBot Comprehensive End-to-End Functional Tester
Systematically tests EVERY button, callback, and flow in the bot
Zero tolerance for failures - every function must work perfectly
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, List, Any, Tuple

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

# Import all handlers and functions
from src.bot.handlers import *
from src.bot.keyboards import ACTIVE_QUESTIONS, create_question_keyboard
from src.bot.ui import *
from src.bot.telegram_safe import validate_callback_pattern
from src.core.hybrid_recommender import get_recommendations

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveBotTester:
    """Comprehensive end-to-end bot tester - tests EVERY button and function"""
    
    def __init__(self):
        self.test_results = []
        self.errors = []
        self.warnings = []
        self.button_tests = 0
        self.flow_tests = 0
        self.function_tests = 0
        
    def log_error(self, test_name: str, error: str):
        """Log a critical error - ZERO TOLERANCE"""
        self.errors.append(f"{test_name}: {error}")
        logger.error(f"❌ CRITICAL FAILURE - {test_name}: {error}")
        
    def log_warning(self, test_name: str, warning: str):
        """Log a warning - needs investigation"""
        self.warnings.append(f"{test_name}: {warning}")
        logger.warning(f"⚠️ WARNING - {test_name}: {warning}")
        
    def log_success(self, test_name: str, message: str = ""):
        """Log successful test"""
        self.test_results.append(test_name)
        logger.info(f"✅ {test_name}: {message}")

    def create_mock_objects(self) -> Tuple[MagicMock, MagicMock]:
        """Create comprehensive mock objects for testing"""
        mock_update = MagicMock()
        mock_context = MagicMock()
        
        # Mock callback query
        mock_update.callback_query = AsyncMock()
        mock_update.callback_query.answer = AsyncMock()
        mock_update.callback_query.edit_message_text = AsyncMock()
        mock_update.callback_query.message = MagicMock()
        mock_update.callback_query.message.chat_id = 12345
        
        # Mock message
        mock_update.message = AsyncMock()
        mock_update.message.reply_text = AsyncMock()
        mock_update.message.chat_id = 12345
        
        # Mock context with comprehensive user data
        mock_context.user_data = {
            'language': 'en',
            'lang': 'en',
            'answers': {},
            'current_question': 0,
            'shortlist': [],
            'last_recs': {
                'test_major_1': {
                    'major_id': 'test_major_1',
                    'major_name_en': 'Computer Science',
                    'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
                    'university_name_en': 'Test University',
                    'hybrid_score': 0.95,
                    'mcda_score': 0.90,
                    'ml_score': 0.85
                },
                'test_major_2': {
                    'major_id': 'test_major_2',
                    'major_name_en': 'Software Engineering',
                    'major_name_kh': 'វិស្វកម្មកម្មវិធី',
                    'university_name_en': 'Test University 2',
                    'hybrid_score': 0.88,
                    'mcda_score': 0.85,
                    'ml_score': 0.80
                }
            },
            'last_answers': {
                'location_preference': 'pp',
                'budget_range': 'mid',
                'field_of_interest': 'stem',
                'career_goal': 'tech',
                'scholarship_need': 'no'
            }
        }
        
        # Mock bot
        mock_context.bot = AsyncMock()
        mock_context.bot.send_message = AsyncMock()
        
        return mock_update, mock_context

    async def test_home_screen_buttons(self):
        """Test ALL home screen buttons systematically"""
        test_name = "Home Screen Buttons"
        self.button_tests += 1
        
        try:
            mock_update, mock_context = self.create_mock_objects()
            
            # Test each home screen button
            home_buttons = [
                ("START_QUIZ", start_quiz_callback),
                ("HOME", home_screen_callback),
                ("QS_SURPRISE", quick_start_callback),
                ("QS_PP", quick_start_callback),
                ("BROWSE_MAJORS", browse_majors_callback),
                ("SHORTLIST_VIEW", shortlist_callback),
                ("HELP_INFO", help_callback),
                ("LANG_TOGGLE", language_toggle_callback),
            ]
            
            for callback_data, handler_func in home_buttons:
                mock_update.callback_query.data = callback_data
                
                with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                     patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                     patch('src.bot.handlers.get_lang', return_value='en'), \
                     patch('src.bot.ui.create_home_screen', return_value=("Home", MagicMock())), \
                     patch('src.bot.ui.create_recommendations_view', return_value=("Recs", MagicMock())), \
                     patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"), \
                     patch('src.core.hybrid_recommender.get_recommendations', return_value=[]), \
                     patch('src.bot.utils.cache_recommendations'), \
                     patch('src.bot.utils.cache_user_answers'):
                    
                    await handler_func(mock_update, mock_context)
                    
                logger.info(f"  ✅ Button {callback_data} works correctly")
            
            self.log_success(test_name, f"All {len(home_buttons)} home screen buttons functional")
            
        except Exception as e:
            self.log_error(test_name, f"Exception: {e}")

    async def test_assessment_flow_complete(self):
        """Test COMPLETE assessment flow from start to finish"""
        test_name = "Complete Assessment Flow"
        self.flow_tests += 1

        try:
            # Test assessment flow logic without heavy mocking

            # Step 1: Verify all 15 questions are properly structured
            if len(ACTIVE_QUESTIONS) != 15:
                self.log_error(test_name, f"Expected 15 questions, found {len(ACTIVE_QUESTIONS)}")
                return

            # Step 2: Verify each question has proper structure
            for i, question in enumerate(ACTIVE_QUESTIONS):
                if 'id' not in question:
                    self.log_error(test_name, f"Question {i+1} missing 'id' field")
                    return
                if 'options' not in question:
                    self.log_error(test_name, f"Question {i+1} missing 'options' field")
                    return
                if not question['options']:
                    self.log_error(test_name, f"Question {i+1} has no options")
                    return

                logger.info(f"  ✅ Question {i+1}/15 structure valid: {question['id']}")

            # Step 3: Test question keyboard generation
            for question in ACTIVE_QUESTIONS:
                try:
                    keyboard = create_question_keyboard(question, 'en')
                    if not keyboard.inline_keyboard:
                        self.log_error(test_name, f"Question {question['id']} generated empty keyboard")
                        return
                except Exception as e:
                    self.log_error(test_name, f"Question {question['id']} keyboard generation failed: {e}")
                    return

            # Step 4: Test assessment flow handlers with minimal mocking
            mock_update, mock_context = self.create_mock_objects()

            # Test start quiz
            mock_update.callback_query.data = "START_QUIZ"
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'):

                await start_quiz_callback(mock_update, mock_context)

                if 'answers' not in mock_context.user_data:
                    self.log_error(test_name, "Assessment start failed - no answers initialized")
                    return
                if 'current_question' not in mock_context.user_data:
                    self.log_error(test_name, "Assessment start failed - no current_question initialized")
                    return

            # Step 5: Test answer handling for each question type
            test_answers = {
                'location_preference': 'pp',
                'budget_range': 'low',
                'field_of_interest': 'stem',
                'career_goal': 'tech',
                'academic_strength': 'math'
            }

            for question_id, answer_value in test_answers.items():
                mock_update.callback_query.data = f"answer_{question_id}_{answer_value}"
                mock_context.user_data['current_question'] = 0

                with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                     patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                     patch('src.bot.handlers.get_lang', return_value='en'), \
                     patch('src.bot.keyboards.create_question_keyboard', return_value=MagicMock()):

                    await handle_question_answer(mock_update, mock_context)

                logger.info(f"  ✅ Answer handler works for: {question_id} = {answer_value}")

            self.log_success(test_name, "Complete 15-question assessment flow structure and handlers work perfectly")

        except Exception as e:
            self.log_error(test_name, f"Exception: {e}")

    async def test_recommendation_interaction_buttons(self):
        """Test ALL recommendation interaction buttons"""
        test_name = "Recommendation Interaction Buttons"
        self.button_tests += 1
        
        try:
            mock_update, mock_context = self.create_mock_objects()
            
            # Test recommendation buttons
            rec_buttons = [
                ("DET_test_major_1", detail_callback),
                ("CMP_test_major_1", compare_callback),
                ("SAVE_test_major_1", save_callback),
                ("REMOVE_test_major_1", remove_callback),
                ("MORE_test_major_1", more_info_callback),
                ("SEC_test_major_1_0", section_callback),
                ("BACK", back_callback),
                ("REFRESH", refresh_callback),
                ("RESTART", restart_callback),
            ]
            
            for callback_data, handler_func in rec_buttons:
                mock_update.callback_query.data = callback_data
                
                with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                     patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                     patch('src.bot.handlers.get_lang', return_value='en'), \
                     patch('src.bot.ui.create_detail_view', return_value=("Detail", MagicMock())), \
                     patch('src.bot.ui.create_comparison_view', return_value="Comparison"), \
                     patch('src.bot.ui.create_recommendations_view', return_value=("Recs", MagicMock())), \
                     patch('src.bot.ui.render_section', return_value=("Section", MagicMock())), \
                     patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"), \
                     patch('src.core.hybrid_recommender.get_recommendations', return_value=[]), \
                     patch('src.bot.utils.cache_recommendations'), \
                     patch('src.bot.utils.cache_user_answers'), \
                     patch('src.bot.handlers.start_assessment', new_callable=AsyncMock):
                    
                    await handler_func(mock_update, mock_context)
                    
                logger.info(f"  ✅ Button {callback_data} works correctly")
            
            self.log_success(test_name, f"All {len(rec_buttons)} recommendation buttons functional")
            
        except Exception as e:
            self.log_error(test_name, f"Exception: {e}")

    async def test_advanced_features(self):
        """Test advanced features and edge cases"""
        test_name = "Advanced Features"
        self.function_tests += 1
        
        try:
            mock_update, mock_context = self.create_mock_objects()
            
            # Test feedback buttons
            feedback_buttons = [("FB_UP", feedback_callback), ("FB_DOWN", feedback_callback)]
            for callback_data, handler_func in feedback_buttons:
                mock_update.callback_query.data = callback_data
                
                with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                     patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                     patch('src.bot.handlers.get_lang', return_value='en'), \
                     patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"):
                    
                    await handler_func(mock_update, mock_context)
                    
                logger.info(f"  ✅ Feedback button {callback_data} works")
            
            # Test trending callback
            mock_update.callback_query.data = "TREND_test_major_1"
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'), \
                 patch('src.core.data_loader.load_raw', return_value=[{'major_id': 'test_major_1'}]), \
                 patch('src.bot.ui.render_section', return_value=("Section", MagicMock())):
                
                await trending_callback(mock_update, mock_context)
                logger.info("  ✅ Trending callback works")
            
            # Test filters
            mock_update.callback_query.data = "FILTERS_HOME"
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'), \
                 patch('src.bot.ui.create_filters_menu', return_value=("Filters", MagicMock())):
                
                await filters_callback(mock_update, mock_context)
                logger.info("  ✅ Filters callback works")
            
            # Test unknown callback handling
            mock_update.callback_query.data = "UNKNOWN_CALLBACK"
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'), \
                 patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"):
                
                await unknown_callback(mock_update, mock_context)
                logger.info("  ✅ Unknown callback handling works")
            
            self.log_success(test_name, "All advanced features functional")
            
        except Exception as e:
            self.log_error(test_name, f"Exception: {e}")

    async def test_language_switching_flow(self):
        """Test complete language switching across all screens"""
        test_name = "Language Switching Flow"
        self.flow_tests += 1
        
        try:
            mock_update, mock_context = self.create_mock_objects()
            
            # Test language toggle from English to Khmer
            mock_context.user_data['language'] = 'en'
            mock_update.callback_query.data = "LANG_TOGGLE"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', side_effect=['en', 'kh']), \
                 patch('src.bot.ui.create_home_screen', return_value=("Home KH", MagicMock())):
                
                await language_toggle_callback(mock_update, mock_context)
                
                # Verify language was changed
                if mock_context.user_data.get('language') != 'kh':
                    self.log_error(test_name, "Language toggle failed - language not changed")
                    return
            
            # Test language toggle from Khmer to English
            mock_context.user_data['language'] = 'kh'
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', side_effect=['kh', 'en']), \
                 patch('src.bot.ui.create_home_screen', return_value=("Home EN", MagicMock())):
                
                await language_toggle_callback(mock_update, mock_context)
                
                # Verify language was changed back
                if mock_context.user_data.get('language') != 'en':
                    self.log_error(test_name, "Language toggle back failed")
                    return
            
            self.log_success(test_name, "Language switching works perfectly in both directions")
            
        except Exception as e:
            self.log_error(test_name, f"Exception: {e}")

    async def test_callback_pattern_validation(self):
        """Test ALL callback patterns are properly registered"""
        test_name = "Callback Pattern Validation"
        self.function_tests += 1
        
        try:
            # Test all known callback patterns
            test_patterns = [
                "START_QUIZ", "HOME", "QS_SURPRISE", "QS_PP", "BROWSE_MAJORS",
                "SHORTLIST_VIEW", "HELP_INFO", "LANG_TOGGLE", "RESTART",
                "BACK", "REFRESH", "FILTERS_HOME", "DET_123", "CMP_456",
                "SAVE_789", "REMOVE_101", "SEC_123_1", "TREND_456",
                "FB_UP", "FB_DOWN", "MORE_123", "answer_location_preference_pp"
            ]
            
            unhandled_patterns = []
            for pattern in test_patterns:
                if not validate_callback_pattern(pattern):
                    unhandled_patterns.append(pattern)
            
            if unhandled_patterns:
                self.log_error(test_name, f"Unhandled callback patterns: {unhandled_patterns}")
                return
            
            self.log_success(test_name, f"All {len(test_patterns)} callback patterns properly registered")
            
        except Exception as e:
            self.log_error(test_name, f"Exception: {e}")

    async def stress_test_rapid_clicking(self):
        """Stress test with rapid button clicking simulation"""
        test_name = "Stress Test - Rapid Clicking"
        self.function_tests += 1
        
        try:
            mock_update, mock_context = self.create_mock_objects()
            
            # Simulate rapid clicking of home button
            for i in range(10):
                mock_update.callback_query.data = "HOME"
                
                with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                     patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                     patch('src.bot.handlers.get_lang', return_value='en'), \
                     patch('src.bot.ui.create_home_screen', return_value=("Home", MagicMock())):
                    
                    await home_screen_callback(mock_update, mock_context)
                
                # Small delay to simulate rapid clicking
                await asyncio.sleep(0.01)
            
            # Simulate rapid navigation between different screens
            navigation_sequence = ["HOME", "START_QUIZ", "HOME", "SHORTLIST_VIEW", "HOME", "HELP_INFO"]
            for callback_data in navigation_sequence:
                mock_update.callback_query.data = callback_data
                
                with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                     patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                     patch('src.bot.handlers.get_lang', return_value='en'), \
                     patch('src.bot.ui.create_home_screen', return_value=("Home", MagicMock())), \
                     patch('src.bot.keyboards.create_question_keyboard', return_value=MagicMock()), \
                     patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"):
                    
                    if callback_data == "HOME":
                        await home_screen_callback(mock_update, mock_context)
                    elif callback_data == "START_QUIZ":
                        await start_quiz_callback(mock_update, mock_context)
                    elif callback_data == "SHORTLIST_VIEW":
                        await shortlist_callback(mock_update, mock_context)
                    elif callback_data == "HELP_INFO":
                        await help_callback(mock_update, mock_context)
                
                await asyncio.sleep(0.01)
            
            self.log_success(test_name, "Bot handles rapid clicking and navigation perfectly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception: {e}")

    async def run_comprehensive_test_suite(self):
        """Run ALL tests systematically"""
        print("🚀 STARTING COMPREHENSIVE END-TO-END BOT TESTING")
        print("=" * 80)
        print("Testing EVERY button, EVERY flow, EVERY function")
        print("Zero tolerance for failures - everything must work perfectly")
        print("=" * 80)
        
        start_time = time.time()
        
        # Run all test categories
        await self.test_home_screen_buttons()
        await self.test_assessment_flow_complete()
        await self.test_recommendation_interaction_buttons()
        await self.test_advanced_features()
        await self.test_language_switching_flow()
        await self.test_callback_pattern_validation()
        await self.stress_test_rapid_clicking()
        
        end_time = time.time()
        test_duration = end_time - start_time
        
        # Generate comprehensive report
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE END-TO-END TEST RESULTS")
        print("=" * 80)
        
        total_tests = len(self.test_results) + len(self.errors)
        passed_tests = len(self.test_results)
        
        print(f"⏱️ Test Duration: {test_duration:.2f} seconds")
        print(f"🔘 Button Tests: {self.button_tests}")
        print(f"🔄 Flow Tests: {self.flow_tests}")
        print(f"⚙️ Function Tests: {self.function_tests}")
        print(f"✅ Tests Passed: {passed_tests}")
        print(f"❌ Tests Failed: {len(self.errors)}")
        print(f"⚠️ Warnings: {len(self.warnings)}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "📈 Success Rate: 0%")
        
        if self.errors:
            print("\n🔥 CRITICAL FAILURES - MUST BE FIXED:")
            for error in self.errors:
                print(f"   ❌ {error}")
        
        if self.warnings:
            print("\n⚠️ WARNINGS - NEED INVESTIGATION:")
            for warning in self.warnings:
                print(f"   ⚠️ {warning}")
        
        if not self.errors and not self.warnings:
            print("\n🎉 PERFECT SCORE - ALL TESTS PASSED!")
            print("✅ Every button works perfectly")
            print("✅ Every flow completes successfully")
            print("✅ Every function operates correctly")
            print("✅ Bot is 100% operational and ready for production")
            print("✅ Zero failures, zero warnings, zero issues")
        else:
            print("\n🚨 ISSUES FOUND - BOT NOT READY")
            print("❌ Critical issues must be resolved")
            print("🔧 Fix all failures before deployment")
        
        return len(self.errors) == 0 and len(self.warnings) == 0


async def main():
    """Main entry point for comprehensive testing"""
    tester = ComprehensiveBotTester()
    perfect_score = await tester.run_comprehensive_test_suite()
    
    if perfect_score:
        print("\n🚀 EduGuideBot: PERFECT OPERATIONAL STATUS")
        print("✅ Ready for production deployment")
        return 0
    else:
        print("\n🛑 EduGuideBot: ISSUES DETECTED")
        print("❌ Fix all issues before deployment")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
