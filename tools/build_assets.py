"""
Asset Builder Tool
Converts JSON university data to Parquet format with hash-based caching
"""

import json
import hashlib
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any
import argparse
import logging

logger = logging.getLogger(__name__)


def calculate_source_hash(data_dir: Path) -> str:
    """
    Calculate hash of all source JSON files

    Args:
        data_dir: Path to data/raw directory

    Returns:
        str: MD5 hash of all source files
    """
    hasher = hashlib.md5()

    # Get all JSON files sorted for consistent hashing
    json_files = sorted(data_dir.rglob("*.json"))

    for json_file in json_files:
        # Add file path to hash for uniqueness
        hasher.update(str(json_file).encode('utf-8'))

        # Add file content to hash
        with open(json_file, 'rb') as f:
            hasher.update(f.read())

    return hasher.hexdigest()


def load_all_json_files(data_dir: Path) -> List[Dict[str, Any]]:
    """
    Load all JSON files from data directory

    Args:
        data_dir: Path to data/raw directory

    Returns:
        List[Dict]: Combined program data
    """
    # Import here to avoid circular imports
    import sys
    sys.path.append(str(Path(__file__).parent.parent))

    from src.core.data_loader import load_raw
    from src.core.feature_engineering import add_derived_features

    # Load raw data
    raw_data = load_raw()

    # Add derived features
    enhanced_data = add_derived_features(raw_data)

    return enhanced_data


def convert_to_parquet(data: List[Dict[str, Any]], output_path: Path, lang: str = "kh") -> None:
    """
    Convert program data to Parquet format with language-specific field names

    Args:
        data: Program data list
        output_path: Output Parquet file path
        lang: Language code for field names ("kh" or "en")
    """
    # Convert to DataFrame
    df = pd.DataFrame(data)

    # Apply language-specific field renaming for English
    if lang == "en":
        field_mapping = {
            'major_name_kh': 'name_en',
            'university_name_kh': 'university_en',
            'description_kh': 'description_en',
            'career_prospects_kh': 'career_prospects_en',
            'admission_requirements_kh': 'admission_requirements_en'
        }

        # Rename columns if they exist
        for kh_field, en_field in field_mapping.items():
            if kh_field in df.columns:
                df = df.rename(columns={kh_field: en_field})

    # Clean numeric columns
    numeric_columns = ['total_credits', 'minimum_gpa']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

    # Clean string columns - replace empty strings with None
    string_columns = df.select_dtypes(include=['object']).columns
    for col in string_columns:
        df[col] = df[col].replace('', None)

    # Ensure output directory exists
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Save as Parquet with compression
    df.to_parquet(output_path, compression='snappy', index=False)

    logger.info(f"Saved {len(data)} programs to {output_path} (lang: {lang})")
    logger.info(f"Columns: {list(df.columns)}")
    logger.info(f"File size: {output_path.stat().st_size / 1024:.1f} KB")


def should_rebuild(data_dir: Path, build_dir: Path, lang: str = "kh") -> bool:
    """
    Check if assets need to be rebuilt based on source hash

    Args:
        data_dir: Source data directory
        build_dir: Build output directory
        lang: Language code for output files

    Returns:
        bool: True if rebuild needed
    """
    hash_file = build_dir / f"source_hash_{lang}.txt"
    parquet_file = build_dir / f"programmes_{lang}.parquet"

    # If output files don't exist, rebuild needed
    if not hash_file.exists() or not parquet_file.exists():
        return True

    # Calculate current hash
    current_hash = calculate_source_hash(data_dir)

    # Read stored hash
    try:
        with open(hash_file, 'r') as f:
            stored_hash = f.read().strip()
    except FileNotFoundError:
        return True

    # Compare hashes
    return current_hash != stored_hash


def main():
    """
    Main asset builder function
    """
    # Configure logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

    parser = argparse.ArgumentParser(description='Build EduGuideBot assets')
    parser.add_argument('--force', action='store_true', help='Force rebuild')
    parser.add_argument('--data-dir', default='data/raw', help='Source data directory')
    parser.add_argument('--build-dir', default='build', help='Build output directory')
    parser.add_argument('--lang', default='kh', choices=['kh', 'en'], help='Language for field names')

    args = parser.parse_args()

    data_dir = Path(args.data_dir)
    build_dir = Path(args.build_dir)

    # Check if data directory exists
    if not data_dir.exists():
        logger.error(f"Data directory {data_dir} does not exist")
        return 1

    # Check if rebuild needed
    if not args.force and not should_rebuild(data_dir, build_dir, args.lang):
        logger.info(f"Assets are up to date for language '{args.lang}', no rebuild needed")
        return 0

    logger.info(f"Building assets for language '{args.lang}'...")

    try:
        # Load source data
        logger.info("Loading source data...")
        data = load_all_json_files(data_dir)
        logger.info(f"Loaded {len(data)} programs")

        # Convert to Parquet
        parquet_file = build_dir / f"programmes_{args.lang}.parquet"
        logger.info(f"Converting to Parquet: {parquet_file}")
        convert_to_parquet(data, parquet_file, args.lang)

        # Update hash file
        hash_file = build_dir / f"source_hash_{args.lang}.txt"
        current_hash = calculate_source_hash(data_dir)
        with open(hash_file, 'w') as f:
            f.write(current_hash)
        logger.info(f"Updated source hash for {args.lang}: {current_hash[:8]}...")

        logger.info("Asset build completed successfully!")
        return 0

    except Exception as e:
        logger.error(f"Asset build failed: {e}")
        return 1


if __name__ == '__main__':
    main()
