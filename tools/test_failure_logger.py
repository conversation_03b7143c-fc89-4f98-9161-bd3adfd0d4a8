#!/usr/bin/env python3
"""
Test Failure Logger for EduGuideBot
Captures detailed failure information for debugging and analysis
"""

import json
import time
import traceback
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime


class TestFailureLogger:
    """Comprehensive test failure logging system"""
    
    def __init__(self, log_dir: str = "tests/logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.failures_log = self.log_dir / "failures.log"
        self.coverage_log = self.log_dir / "callback_coverage.log"
        self.performance_log = self.log_dir / "performance.log"
        
        # Initialize log files if they don't exist
        for log_file in [self.failures_log, self.coverage_log, self.performance_log]:
            if not log_file.exists():
                log_file.write_text("[]")
    
    def log_failure(self,
                   test_name: str,
                   user_action: str,
                   expected_behavior: str,
                   actual_behavior: str,
                   error: Optional[Exception] = None,
                   context: Optional[Dict[str, Any]] = None,
                   callback_data: Optional[str] = None,
                   bot_response: Optional[str] = None) -> None:
        """
        Log detailed test failure information with enhanced fields

        Args:
            test_name: Name of the failing test
            user_action: What the user did (e.g., "/start", "clicked button")
            expected_behavior: What should have happened
            actual_behavior: What actually happened
            error: Exception object if available
            context: Additional context data
            callback_data: Telegram callback data if applicable
            bot_response: Bot's response message if available
        """

        failure_entry = {
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat(),
            "test_name": test_name,
            "user_action": user_action,
            "bot_response": bot_response or "No response captured",
            "callback_data": callback_data or "N/A",
            "expected_behavior": expected_behavior,
            "actual_behavior": actual_behavior,
            "context": context or {},
        }

        if error:
            failure_entry["error_message"] = str(error)
            failure_entry["error"] = {
                "type": type(error).__name__,
                "message": str(error),
                "traceback": traceback.format_exc()
            }

        self._append_to_log(self.failures_log, failure_entry)
        
        # Also print to console for immediate feedback
        print(f"❌ FAILURE LOGGED: {test_name}")
        print(f"   User Action: {user_action}")
        print(f"   Expected: {expected_behavior}")
        print(f"   Actual: {actual_behavior}")
        if error:
            print(f"   Error: {str(error)}")
    
    def log_callback_coverage(self, 
                             registered_patterns: list,
                             tested_patterns: list,
                             orphaned_buttons: list) -> None:
        """
        Log callback coverage analysis
        
        Args:
            registered_patterns: All registered callback patterns
            tested_patterns: Patterns that were tested
            orphaned_buttons: Buttons without handlers
        """
        
        total_patterns = len(registered_patterns)
        tested_count = len(tested_patterns)
        coverage_percentage = (tested_count / total_patterns * 100) if total_patterns > 0 else 0
        
        coverage_entry = {
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat(),
            "total_patterns": total_patterns,
            "tested_patterns": tested_count,
            "coverage_percentage": coverage_percentage,
            "registered_patterns": registered_patterns,
            "tested_patterns": tested_patterns,
            "orphaned_buttons": orphaned_buttons,
            "missing_handlers": [p for p in registered_patterns if p not in tested_patterns]
        }
        
        self._append_to_log(self.coverage_log, coverage_entry)
        
        print(f"📊 CALLBACK COVERAGE: {coverage_percentage:.1f}%")
        print(f"   Registered: {total_patterns}, Tested: {tested_count}")
        if orphaned_buttons:
            print(f"   ⚠️ Orphaned buttons: {len(orphaned_buttons)}")
    
    def log_performance_metrics(self,
                               test_name: str,
                               execution_time: float,
                               memory_usage: Optional[float] = None,
                               response_time: Optional[float] = None,
                               additional_metrics: Optional[Dict[str, Any]] = None) -> None:
        """
        Log performance metrics
        
        Args:
            test_name: Name of the test
            execution_time: Total execution time in seconds
            memory_usage: Memory usage in MB
            response_time: Response time in seconds
            additional_metrics: Additional performance data
        """
        
        performance_entry = {
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat(),
            "test_name": test_name,
            "execution_time": execution_time,
            "memory_usage_mb": memory_usage,
            "response_time": response_time,
            "additional_metrics": additional_metrics or {}
        }
        
        self._append_to_log(self.performance_log, performance_entry)
    
    def _append_to_log(self, log_file: Path, entry: Dict[str, Any]) -> None:
        """Append entry to log file"""
        try:
            # Read existing entries
            if log_file.exists() and log_file.stat().st_size > 0:
                with open(log_file, 'r', encoding='utf-8') as f:
                    entries = json.load(f)
            else:
                entries = []
            
            # Append new entry
            entries.append(entry)
            
            # Write back to file
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(entries, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"⚠️ Failed to write to log file {log_file}: {e}")
    
    def get_failure_summary(self) -> Dict[str, Any]:
        """Get summary of recent failures"""
        try:
            with open(self.failures_log, 'r', encoding='utf-8') as f:
                failures = json.load(f)
            
            if not failures:
                return {"total_failures": 0, "recent_failures": []}
            
            # Get failures from last 24 hours
            current_time = time.time()
            recent_failures = [
                f for f in failures 
                if current_time - f["timestamp"] < 86400  # 24 hours
            ]
            
            # Group by test name
            failure_counts = {}
            for failure in recent_failures:
                test_name = failure["test_name"]
                failure_counts[test_name] = failure_counts.get(test_name, 0) + 1
            
            return {
                "total_failures": len(failures),
                "recent_failures": len(recent_failures),
                "failure_counts": failure_counts,
                "most_recent": failures[-1] if failures else None
            }
            
        except Exception as e:
            print(f"⚠️ Failed to read failure summary: {e}")
            return {"error": str(e)}
    
    def get_coverage_report(self) -> Dict[str, Any]:
        """Get latest coverage report"""
        try:
            with open(self.coverage_log, 'r', encoding='utf-8') as f:
                coverage_entries = json.load(f)
            
            if not coverage_entries:
                return {"coverage_percentage": 0, "status": "No coverage data"}
            
            latest_entry = coverage_entries[-1]
            return {
                "coverage_percentage": latest_entry["coverage_percentage"],
                "total_patterns": latest_entry["total_patterns"],
                "tested_patterns": latest_entry["tested_patterns"],
                "orphaned_buttons": len(latest_entry["orphaned_buttons"]),
                "last_updated": latest_entry["datetime"]
            }
            
        except Exception as e:
            print(f"⚠️ Failed to read coverage report: {e}")
            return {"error": str(e)}
    
    def cleanup_old_logs(self, days_to_keep: int = 30) -> None:
        """Remove log entries older than specified days"""
        cutoff_time = time.time() - (days_to_keep * 86400)
        
        for log_file in [self.failures_log, self.coverage_log, self.performance_log]:
            try:
                if not log_file.exists():
                    continue
                
                with open(log_file, 'r', encoding='utf-8') as f:
                    entries = json.load(f)
                
                # Keep only recent entries
                recent_entries = [
                    entry for entry in entries 
                    if entry.get("timestamp", 0) > cutoff_time
                ]
                
                with open(log_file, 'w', encoding='utf-8') as f:
                    json.dump(recent_entries, f, indent=2, ensure_ascii=False)
                
                removed_count = len(entries) - len(recent_entries)
                if removed_count > 0:
                    print(f"🧹 Cleaned up {removed_count} old entries from {log_file.name}")
                    
            except Exception as e:
                print(f"⚠️ Failed to cleanup {log_file}: {e}")


# Global logger instance
failure_logger = TestFailureLogger()


def log_test_failure(test_name: str,
                    user_action: str,
                    expected: str,
                    actual: str,
                    error: Optional[Exception] = None,
                    callback_data: Optional[str] = None,
                    bot_response: Optional[str] = None,
                    **context) -> None:
    """Convenience function for logging test failures with enhanced fields"""
    failure_logger.log_failure(
        test_name=test_name,
        user_action=user_action,
        expected_behavior=expected,
        actual_behavior=actual,
        error=error,
        callback_data=callback_data,
        bot_response=bot_response,
        context=context
    )


def log_callback_coverage(registered: list, tested: list, orphaned: list) -> None:
    """Convenience function for logging callback coverage"""
    failure_logger.log_callback_coverage(registered, tested, orphaned)


def log_performance(test_name: str, execution_time: float, **metrics) -> None:
    """Convenience function for logging performance metrics"""
    failure_logger.log_performance_metrics(test_name, execution_time, **metrics)


if __name__ == "__main__":
    # Example usage
    logger = TestFailureLogger()
    
    # Log a test failure
    logger.log_failure(
        test_name="Button Click Test",
        user_action="Clicked START_QUIZ button",
        expected_behavior="Should start quiz flow",
        actual_behavior="Got error message",
        error=Exception("Handler not found"),
        context={"callback_data": "START_QUIZ", "user_id": 12345}
    )
    
    # Get failure summary
    summary = logger.get_failure_summary()
    print("Failure Summary:", json.dumps(summary, indent=2))
