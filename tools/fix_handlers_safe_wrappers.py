#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to systematically update all handlers to use safe wrappers
This will fix the remaining issues in the assessment flow analysis
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

def main():
    """Apply safe wrapper fixes to all handlers"""
    
    handlers_file = Path(__file__).parents[1] / "src" / "bot" / "handlers.py"
    
    # Read the current file
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define replacements to apply
    replacements = [
        # Fix home_screen_callback
        (
            "async def home_screen_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Show enhanced home screen\"\"\"\n"
            "    query = update.callback_query\n"
            "    if query:\n"
            "        await query.answer()",
            "@log_telegram_errors(logger)\n"
            "async def home_screen_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Show enhanced home screen\"\"\"\n"
            "    query = update.callback_query\n"
            "    if query:\n"
            "        await safe_answer_callback(query)"
        ),
        
        # Fix home_screen_callback edit_message_text
        (
            "    if query:\n"
            "        await query.edit_message_text(\n"
            "            text=message_text,\n"
            "            reply_markup=reply_markup,\n"
            "            parse_mode=\"Markdown\"\n"
            "        )",
            "    if query:\n"
            "        await safe_edit_message(\n"
            "            query,\n"
            "            text=message_text,\n"
            "            reply_markup=reply_markup,\n"
            "            parse_mode=\"Markdown\"\n"
            "        )"
        ),
        
        # Fix surprise_me_callback
        (
            "async def surprise_me_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Generate random recommendations\"\"\"\n"
            "    query = update.callback_query\n"
            "    await query.answer()",
            "@log_telegram_errors(logger)\n"
            "async def surprise_me_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Generate random recommendations\"\"\"\n"
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix browse_majors_callback
        (
            "async def browse_majors_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Show paginated major browsing interface\"\"\"\n"
            "    query = update.callback_query\n"
            "    await query.answer()",
            "@log_telegram_errors(logger)\n"
            "async def browse_majors_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Show paginated major browsing interface\"\"\"\n"
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix shortlist_callback
        (
            "async def shortlist_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Show user's saved majors\"\"\"\n"
            "    query = update.callback_query\n"
            "    await query.answer()",
            "@log_telegram_errors(logger)\n"
            "async def shortlist_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Show user's saved majors\"\"\"\n"
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix help_callback
        (
            "async def help_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Show help information\"\"\"\n"
            "    query = update.callback_query\n"
            "    await query.answer()",
            "@log_telegram_errors(logger)\n"
            "async def help_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Show help information\"\"\"\n"
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix language_toggle_callback
        (
            "    query = update.callback_query\n"
            "    await query.answer()",
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix quick_start_callback
        (
            "    query = update.callback_query\n"
            "    await query.answer()",
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix wizard_start_callback
        (
            "    query = update.callback_query\n"
            "    await query.answer()",
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix trending_callback
        (
            "    query = update.callback_query\n"
            "    await query.answer()",
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix feedback_callback
        (
            "    query = update.callback_query\n"
            "    await query.answer()",
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix unknown_callback
        (
            "    query = update.callback_query\n"
            "    await query.answer(\"🤔\")",
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query, \"🤔\")"
        ),
        
        # Fix restart_callback
        (
            "async def restart_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Handle 'Restart' button callback to start new assessment\"\"\"\n"
            "    query = update.callback_query\n"
            "    await query.answer()",
            "@log_telegram_errors(logger)\n"
            "async def restart_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Handle 'Restart' button callback to start new assessment\"\"\"\n"
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix refresh_callback
        (
            "async def refresh_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Handle 'Refresh' button callback to regenerate recommendations\"\"\"\n"
            "    query = update.callback_query\n"
            "    await query.answer()",
            "@log_telegram_errors(logger)\n"
            "async def refresh_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n"
            "    \"\"\"Handle 'Refresh' button callback to regenerate recommendations\"\"\"\n"
            "    query = update.callback_query\n"
            "    await safe_answer_callback(query)"
        ),
        
        # Fix various edit_message_text calls to use safe_edit_message
        (
            "        await query.edit_message_text(\n"
            "            text=message_text,\n"
            "            reply_markup=reply_markup,\n"
            "            parse_mode=\"Markdown\"\n"
            "        )",
            "        await safe_edit_message(\n"
            "            query,\n"
            "            text=message_text,\n"
            "            reply_markup=reply_markup,\n"
            "            parse_mode=\"Markdown\"\n"
            "        )"
        ),
        
        # Fix other edit_message_text patterns
        (
            "    await query.edit_message_text(\n"
            "        text=message_text,\n"
            "        reply_markup=reply_markup,\n"
            "        parse_mode=\"Markdown\"\n"
            "    )",
            "    await safe_edit_message(\n"
            "        query,\n"
            "        text=message_text,\n"
            "        reply_markup=reply_markup,\n"
            "        parse_mode=\"Markdown\"\n"
            "    )"
        ),
    ]
    
    # Apply replacements
    for old_text, new_text in replacements:
        if old_text in content:
            content = content.replace(old_text, new_text)
            print(f"✅ Applied fix: {old_text[:50]}...")
        else:
            print(f"⚠️ Pattern not found: {old_text[:50]}...")
    
    # Write the updated content back
    with open(handlers_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\n🎉 Updated {handlers_file}")
    print("All handlers should now use safe wrappers!")

if __name__ == "__main__":
    main()
