#!/usr/bin/env python3
"""
Test Filter Functionality
Comprehensive test of all filter-related functions
"""

import asyncio
import logging
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

# Import bot modules
from src.bot.handlers import filters_callback, handle_filter_selection, apply_filter_callback
from src.bot.ui import create_filters_menu, apply_filter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FilterFunctionalityTester:
    """Test all filter functionality"""
    
    def __init__(self):
        self.test_results = []
        self.errors = []
        
    def create_mock_objects(self):
        """Create mock objects for testing"""
        mock_update = MagicMock()
        mock_context = MagicMock()
        
        # Mock callback query
        mock_update.callback_query = AsyncMock()
        mock_update.callback_query.answer = AsyncMock()
        mock_update.callback_query.edit_message_text = AsyncMock()
        mock_update.callback_query.message = MagicMock()
        mock_update.callback_query.message.chat_id = 12345
        
        # Mock context with test data
        mock_context.user_data = {
            'language': 'en',
            'last_recs': {
                'major_1': {
                    'major_id': 'major_1',
                    'major_name_en': 'Computer Science',
                    'tuition_fees_usd': '400',
                    'field_tag': 'stem',
                    'city': 'ភ្នំពេញ'
                },
                'major_2': {
                    'major_id': 'major_2',
                    'major_name_en': 'Business Administration',
                    'tuition_fees_usd': '800',
                    'field_tag': 'business',
                    'city': 'ភ្នំពេញ'
                },
                'major_3': {
                    'major_id': 'major_3',
                    'major_name_en': 'Medicine',
                    'tuition_fees_usd': '2000',
                    'field_tag': 'health',
                    'city': 'សៀមរាប'
                }
            }
        }
        
        return mock_update, mock_context

    async def test_filters_menu(self):
        """Test filters menu creation"""
        test_name = "Filters Menu Creation"
        
        try:
            # Test menu creation
            message_text, reply_markup = create_filters_menu('en')
            
            if message_text and reply_markup:
                logger.info(f"✅ {test_name}: Menu created successfully")
                self.test_results.append(test_name)
                return True
            else:
                logger.error(f"❌ {test_name}: Menu creation failed")
                self.errors.append(f"{test_name}: Menu creation failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ {test_name}: Exception - {e}")
            self.errors.append(f"{test_name}: {e}")
            return False

    async def test_filters_callback(self):
        """Test filters callback handler"""
        test_name = "Filters Callback Handler"
        
        try:
            mock_update, mock_context = self.create_mock_objects()
            mock_update.callback_query.data = "FILTERS_HOME"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'):
                
                await filters_callback(mock_update, mock_context)
                
            logger.info(f"✅ {test_name}: Handler executed successfully")
            self.test_results.append(test_name)
            return True
            
        except Exception as e:
            logger.error(f"❌ {test_name}: Exception - {e}")
            self.errors.append(f"{test_name}: {e}")
            return False

    async def test_filter_selection(self):
        """Test filter type selection"""
        test_name = "Filter Type Selection"
        
        try:
            mock_update, mock_context = self.create_mock_objects()
            
            # Test budget filter selection
            mock_update.callback_query.data = "FILTER_budget"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'), \
                 patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"):
                
                await handle_filter_selection(mock_update, mock_context, "budget")
                
            logger.info(f"✅ {test_name}: Budget filter selection works")
            
            # Test field filter selection
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'), \
                 patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"):
                
                await handle_filter_selection(mock_update, mock_context, "field")
                
            logger.info(f"✅ {test_name}: Field filter selection works")
            
            # Test city filter selection
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'), \
                 patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"):
                
                await handle_filter_selection(mock_update, mock_context, "city")
                
            logger.info(f"✅ {test_name}: City filter selection works")
            
            self.test_results.append(test_name)
            return True
            
        except Exception as e:
            logger.error(f"❌ {test_name}: Exception - {e}")
            self.errors.append(f"{test_name}: {e}")
            return False

    async def test_filter_application(self):
        """Test filter application"""
        test_name = "Filter Application"
        
        try:
            mock_update, mock_context = self.create_mock_objects()
            
            # Test budget filter application
            mock_update.callback_query.data = "APPLY_FILTER_budget_low"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'), \
                 patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"), \
                 patch('src.bot.ui.create_recommendations_view', return_value=("Filtered Recs", MagicMock())):
                
                await apply_filter_callback(mock_update, mock_context)
                
            logger.info(f"✅ {test_name}: Budget filter application works")
            
            # Test field filter application
            mock_update.callback_query.data = "APPLY_FILTER_field_stem"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang', return_value='en'), \
                 patch('src.bot.ui.tr', side_effect=lambda key, lang: f"Translated: {key}"), \
                 patch('src.bot.ui.create_recommendations_view', return_value=("Filtered Recs", MagicMock())):
                
                await apply_filter_callback(mock_update, mock_context)
                
            logger.info(f"✅ {test_name}: Field filter application works")
            
            self.test_results.append(test_name)
            return True
            
        except Exception as e:
            logger.error(f"❌ {test_name}: Exception - {e}")
            self.errors.append(f"{test_name}: {e}")
            return False

    def test_filter_logic(self):
        """Test filter logic"""
        test_name = "Filter Logic"
        
        try:
            # Test data
            recommendations = [
                {
                    'major_id': 'major_1',
                    'major_name_en': 'Computer Science',
                    'tuition_fees_usd': '400',
                    'field_tag': 'stem',
                    'city': 'ភ្នំពេញ'
                },
                {
                    'major_id': 'major_2',
                    'major_name_en': 'Business Administration',
                    'tuition_fees_usd': '800',
                    'field_tag': 'business',
                    'city': 'ភ្នំពេញ'
                },
                {
                    'major_id': 'major_3',
                    'major_name_en': 'Medicine',
                    'tuition_fees_usd': '2000',
                    'field_tag': 'health',
                    'city': 'សៀមរាប'
                }
            ]
            
            # Test budget filter
            low_budget_results = apply_filter(recommendations, "budget", "low")
            if len(low_budget_results) == 1 and low_budget_results[0]['major_id'] == 'major_1':
                logger.info(f"✅ {test_name}: Budget filter (low) works correctly")
            else:
                logger.error(f"❌ {test_name}: Budget filter (low) failed")
                self.errors.append(f"{test_name}: Budget filter failed")
                return False
            
            # Test field filter
            stem_results = apply_filter(recommendations, "field", "stem")
            if len(stem_results) == 1 and stem_results[0]['major_id'] == 'major_1':
                logger.info(f"✅ {test_name}: Field filter (stem) works correctly")
            else:
                logger.error(f"❌ {test_name}: Field filter (stem) failed")
                self.errors.append(f"{test_name}: Field filter failed")
                return False
            
            # Test city filter
            pp_results = apply_filter(recommendations, "city", "pp")
            if len(pp_results) == 2:
                logger.info(f"✅ {test_name}: City filter (pp) works correctly")
            else:
                logger.error(f"❌ {test_name}: City filter (pp) failed")
                self.errors.append(f"{test_name}: City filter failed")
                return False
            
            self.test_results.append(test_name)
            return True
            
        except Exception as e:
            logger.error(f"❌ {test_name}: Exception - {e}")
            self.errors.append(f"{test_name}: {e}")
            return False

    async def run_all_tests(self):
        """Run all filter functionality tests"""
        print("🔍 TESTING FILTER FUNCTIONALITY")
        print("=" * 50)
        
        # Run all tests
        tests = [
            ("Filters Menu", self.test_filters_menu),
            ("Filters Callback", self.test_filters_callback),
            ("Filter Selection", self.test_filter_selection),
            ("Filter Application", self.test_filter_application),
            ("Filter Logic", self.test_filter_logic),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n--- Testing {test_name} ---")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                if result:
                    passed += 1
            except Exception as e:
                logger.error(f"❌ {test_name}: Unexpected error - {e}")
                self.errors.append(f"{test_name}: {e}")
        
        # Generate report
        print("\n" + "=" * 50)
        print("📊 FILTER FUNCTIONALITY TEST RESULTS")
        print("=" * 50)
        
        print(f"✅ Tests Passed: {passed}/{total}")
        print(f"❌ Tests Failed: {len(self.errors)}")
        print(f"📈 Success Rate: {(passed/total)*100:.1f}%")
        
        if self.test_results:
            print("\n✅ SUCCESSFUL TESTS:")
            for test in self.test_results:
                print(f"   • {test}")
        
        if self.errors:
            print("\n❌ FAILED TESTS:")
            for error in self.errors:
                print(f"   • {error}")
        
        if passed == total:
            print("\n🎉 ALL FILTER FUNCTIONALITY TESTS PASSED!")
            print("✅ Filter system is fully operational")
            print("✅ Ready for production use")
        else:
            print("\n⚠️ SOME FILTER TESTS FAILED")
            print("🔧 Review and fix issues")
        
        return passed == total


async def main():
    """Main test function"""
    tester = FilterFunctionalityTester()
    success = await tester.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
