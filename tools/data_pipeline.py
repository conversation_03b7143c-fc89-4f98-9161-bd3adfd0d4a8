#!/usr/bin/env python3
"""
Data Pipeline Tool - Clean JSON to Parquet Conversion
Replaces the legacy build_assets.py with a cleaner, more robust pipeline
"""

import json
import hashlib
import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional
import argparse
import logging
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DataPipeline:
    """Clean data pipeline for JSON to Parquet conversion"""
    
    def __init__(self, data_dir: Path, build_dir: Path):
        self.data_dir = data_dir
        self.build_dir = build_dir
        self.build_dir.mkdir(parents=True, exist_ok=True)
    
    def calculate_source_hash(self) -> str:
        """Calculate MD5 hash of all source JSON files"""
        hasher = hashlib.md5()
        json_files = sorted(self.data_dir.rglob("*.json"))
        
        for json_file in json_files:
            hasher.update(str(json_file).encode('utf-8'))
            with open(json_file, 'rb') as f:
                hasher.update(f.read())
        
        return hasher.hexdigest()
    
    def load_json_files(self) -> List[Dict[str, Any]]:
        """Load and validate all JSON files"""
        all_programs = []
        json_files = list(self.data_dir.rglob("*.json"))
        
        logger.info(f"Found {len(json_files)} JSON files")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                
                # Extract programs from university structure
                programs = self._extract_programs(file_data, json_file)
                all_programs.extend(programs)
                
            except Exception as e:
                logger.error(f"Error loading {json_file}: {e}")
                continue
        
        logger.info(f"Loaded {len(all_programs)} programs total")
        return all_programs
    
    def _extract_programs(self, file_data: Dict[str, Any], source_file: Path) -> List[Dict[str, Any]]:
        """Extract program records from university JSON structure"""
        programs = []
        
        if 'university' not in file_data or 'majors' not in file_data:
            logger.warning(f"Invalid structure in {source_file}")
            return programs
        
        university = file_data['university']
        majors = file_data['majors']
        
        for major in majors:
            program = self._build_program_record(university, major, source_file)
            if self._validate_program(program):
                programs.append(program)
        
        return programs
    
    def _build_program_record(self, university: Dict, major: Dict, source_file: Path) -> Dict[str, Any]:
        """Build a clean program record from university and major data"""
        return {
            # University information
            'university_id': university.get('id', ''),
            'university_name_kh': university.get('name_kh', ''),
            'university_name_en': university.get('name_en', ''),
            'university_type': university.get('type', ''),
            'city': university.get('location', {}).get('city', ''),
            'website': university.get('website', ''),
            
            # Major information
            'major_id': major.get('major_info', {}).get('id', ''),
            'major_name_kh': major.get('major_info', {}).get('name_kh', ''),
            'major_name_en': major.get('major_info', {}).get('name_en', ''),
            'degree_level_kh': major.get('major_info', {}).get('degree_level_kh', ''),
            'degree_level_en': major.get('major_info', {}).get('degree_level_en', ''),
            'study_duration_kh': major.get('major_info', {}).get('study_duration_kh', ''),
            'language_of_instruction': major.get('major_info', {}).get('language_of_instruction', []),
            
            # Faculty information
            'faculty_name_kh': major.get('faculty', {}).get('name_kh', ''),
            'faculty_name_en': major.get('faculty', {}).get('name_en', ''),
            
            # Financial information
            'tuition_fees_usd': self._clean_numeric(major.get('practical_information', {}).get('tuition_fees_usd', '')),
            'tuition_fees_khr': self._clean_numeric(major.get('practical_information', {}).get('tuition_fees_khr', '')),
            'scholarship_availability': major.get('practical_information', {}).get('scholarship_availability', False),
            
            # Career information
            'potential_careers_kh': major.get('career_prospects', {}).get('potential_careers_kh', []),
            'potential_careers_en': major.get('career_prospects', {}).get('potential_careers_en', []),
            'employment_rate': self._clean_numeric(major.get('career_prospects', {}).get('employment_statistics', {}).get('employment_rate', '')),
            'average_starting_salary': self._clean_numeric(major.get('career_prospects', {}).get('employment_statistics', {}).get('average_starting_salary', '')),
            
            # Academic information
            'total_credits': self._clean_numeric(major.get('program_details', {}).get('total_credits', 0)),
            'minimum_gpa': self._clean_numeric(major.get('academic_requirements', {}).get('minimum_gpa', 0.0)),
            
            # Source tracking
            'source_file': str(source_file),
        }
    
    def _clean_numeric(self, value: Any) -> Optional[float]:
        """Clean and convert numeric values"""
        if value is None or value == '':
            return None
        
        try:
            # Handle string numbers
            if isinstance(value, str):
                # Remove common non-numeric characters
                cleaned = value.replace(',', '').replace('$', '').replace('%', '').strip()
                if not cleaned:
                    return None
                return float(cleaned)
            
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _validate_program(self, program: Dict[str, Any]) -> bool:
        """Validate program record has required fields"""
        required_fields = [
            'university_name_kh', 'university_name_en',
            'major_name_kh', 'major_name_en',
            'city'
        ]
        
        for field in required_fields:
            if not program.get(field):
                return False
        
        # Must have at least one ID
        if not program.get('university_id') and not program.get('major_id'):
            return False
        
        return True
    
    def add_derived_features(self, programs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Add derived features to program records"""
        for program in programs:
            # Location matching flags
            program['is_phnom_penh'] = program.get('city', '').lower() in ['ភ្នំពេញ', 'phnom penh']
            program['is_siem_reap'] = program.get('city', '').lower() in ['សៀមរាប', 'siem reap']
            program['is_battambang'] = program.get('city', '').lower() in ['បាត់ដំបង', 'battambang']
            
            # Field categorization
            program['is_stem'] = self._is_stem_field(program.get('major_name_en', ''))
            program['is_business'] = self._is_business_field(program.get('major_name_en', ''))
            program['is_arts'] = self._is_arts_field(program.get('major_name_en', ''))
            
            # Financial flags
            tuition_usd = program.get('tuition_fees_usd')
            if tuition_usd:
                program['is_low_cost'] = tuition_usd < 500
                program['is_medium_cost'] = 500 <= tuition_usd <= 1000
                program['is_high_cost'] = tuition_usd > 1000
            else:
                program['is_low_cost'] = False
                program['is_medium_cost'] = False
                program['is_high_cost'] = False
            
            # Employment rate category
            emp_rate = program.get('employment_rate')
            if emp_rate:
                program['high_employment'] = emp_rate >= 80
            else:
                program['high_employment'] = False
        
        return programs
    
    def _is_stem_field(self, major_name: str) -> bool:
        """Check if major is STEM field"""
        stem_keywords = ['engineering', 'computer', 'mathematics', 'science', 'technology', 'it']
        return any(keyword in major_name.lower() for keyword in stem_keywords)
    
    def _is_business_field(self, major_name: str) -> bool:
        """Check if major is business field"""
        business_keywords = ['business', 'management', 'accounting', 'finance', 'economics', 'marketing']
        return any(keyword in major_name.lower() for keyword in business_keywords)
    
    def _is_arts_field(self, major_name: str) -> bool:
        """Check if major is arts field"""
        arts_keywords = ['art', 'literature', 'language', 'history', 'philosophy', 'music']
        return any(keyword in major_name.lower() for keyword in arts_keywords)
    
    def convert_to_parquet(self, programs: List[Dict[str, Any]], lang: str = "kh") -> Path:
        """Convert programs to Parquet format"""
        df = pd.DataFrame(programs)
        
        # Language-specific field mapping for English
        if lang == "en":
            field_mapping = {
                'major_name_kh': 'major_name_en',
                'university_name_kh': 'university_name_en',
                'degree_level_kh': 'degree_level_en',
                'faculty_name_kh': 'faculty_name_en'
            }
            
            # Use English fields where available
            for kh_field, en_field in field_mapping.items():
                if en_field in df.columns and kh_field in df.columns:
                    df[kh_field] = df[en_field].fillna(df[kh_field])
        
        # Clean and optimize data types
        df = self._optimize_dataframe(df)
        
        # Save to Parquet
        output_path = self.build_dir / f"programmes_{lang}.parquet"
        df.to_parquet(output_path, compression='snappy', index=False)
        
        logger.info(f"Saved {len(programs)} programs to {output_path}")
        logger.info(f"File size: {output_path.stat().st_size / 1024:.1f} KB")
        
        return output_path
    
    def _optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame data types and clean data"""
        # Convert numeric columns
        numeric_columns = ['tuition_fees_usd', 'tuition_fees_khr', 'employment_rate', 
                          'average_starting_salary', 'total_credits', 'minimum_gpa']
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Convert boolean columns
        bool_columns = ['scholarship_availability', 'is_phnom_penh', 'is_siem_reap', 
                       'is_battambang', 'is_stem', 'is_business', 'is_arts',
                       'is_low_cost', 'is_medium_cost', 'is_high_cost', 'high_employment']
        
        for col in bool_columns:
            if col in df.columns:
                df[col] = df[col].astype(bool)
        
        # Clean string columns
        string_columns = df.select_dtypes(include=['object']).columns
        for col in string_columns:
            if col not in ['language_of_instruction', 'potential_careers_kh', 'potential_careers_en']:
                df[col] = df[col].astype(str).replace('nan', '').replace('', None)
        
        return df
    
    def should_rebuild(self, lang: str = "kh") -> bool:
        """Check if rebuild is needed based on source hash"""
        hash_file = self.build_dir / f"source_hash_{lang}.txt"
        parquet_file = self.build_dir / f"programmes_{lang}.parquet"
        
        if not hash_file.exists() or not parquet_file.exists():
            return True
        
        current_hash = self.calculate_source_hash()
        
        try:
            with open(hash_file, 'r') as f:
                stored_hash = f.read().strip()
            return current_hash != stored_hash
        except FileNotFoundError:
            return True
    
    def save_hash(self, lang: str = "kh") -> None:
        """Save current source hash"""
        hash_file = self.build_dir / f"source_hash_{lang}.txt"
        current_hash = self.calculate_source_hash()
        
        with open(hash_file, 'w') as f:
            f.write(current_hash)
        
        logger.info(f"Saved source hash for {lang}: {current_hash[:8]}...")
    
    def build(self, lang: str = "kh", force: bool = False) -> bool:
        """Main build pipeline"""
        if not force and not self.should_rebuild(lang):
            logger.info(f"Assets are up to date for language '{lang}', no rebuild needed")
            return True
        
        logger.info(f"Building assets for language '{lang}'...")
        
        try:
            # Load and process data
            programs = self.load_json_files()
            programs = self.add_derived_features(programs)
            
            # Convert to Parquet
            self.convert_to_parquet(programs, lang)
            
            # Save hash
            self.save_hash(lang)
            
            logger.info("Data pipeline completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            return False


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='EduGuideBot Data Pipeline')
    parser.add_argument('--force', action='store_true', help='Force rebuild')
    parser.add_argument('--data-dir', default='data/raw', help='Source data directory')
    parser.add_argument('--build-dir', default='build', help='Build output directory')
    parser.add_argument('--lang', default='kh', choices=['kh', 'en'], help='Language for output')
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    build_dir = Path(args.build_dir)
    
    if not data_dir.exists():
        logger.error(f"Data directory {data_dir} does not exist")
        return 1
    
    pipeline = DataPipeline(data_dir, build_dir)
    success = pipeline.build(args.lang, args.force)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
