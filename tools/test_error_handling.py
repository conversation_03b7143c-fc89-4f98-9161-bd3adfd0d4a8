#!/usr/bin/env python3
"""
Test script for EduGuideBot Telegram Error Handling System
Demonstrates the error handling capabilities and validates the implementation
"""

import asyncio
import logging
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock
from telegram.error import BadRequest, TimedOut

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.bot.telegram_safe import (
    safe_answer_callback,
    safe_edit_message,
    log_telegram_errors,
    offload_heavy_task,
    validate_callback_pattern,
    CALLBACK_PATTERNS
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_safe_answer_callback():
    """Test safe callback answering with various error scenarios"""
    print("\n=== Testing safe_answer_callback ===")
    
    # Test successful answer
    mock_query = AsyncMock()
    result = await safe_answer_callback(mock_query, "Success!")
    print(f"✅ Successful answer: {result}")
    
    # Test ignorable error
    mock_query_error = AsyncMock()
    mock_query_error.answer.side_effect = BadRequest("Query is too old")
    result = await safe_answer_callback(mock_query_error, "Should be ignored")
    print(f"✅ Ignorable error handled: {result}")
    
    # Test timeout error
    mock_query_timeout = AsyncMock()
    mock_query_timeout.answer.side_effect = TimedOut("Request timed out")
    result = await safe_answer_callback(mock_query_timeout, "Timeout test")
    print(f"✅ Timeout error handled: {result}")


async def test_safe_edit_message():
    """Test safe message editing with content comparison"""
    print("\n=== Testing safe_edit_message ===")
    
    # Test successful edit
    mock_query = AsyncMock()
    mock_query.message.text = "Old text"
    mock_query.message.reply_markup = None
    
    result = await safe_edit_message(mock_query, "New text")
    print(f"✅ Successful edit: {result}")
    
    # Test skipped edit (same content)
    mock_query_same = AsyncMock()
    mock_query_same.message.text = "Same text"
    mock_query_same.message.reply_markup = None
    
    result = await safe_edit_message(mock_query_same, "Same text")
    print(f"✅ Skipped unchanged edit: {result}")
    
    # Test "message is not modified" error
    mock_query_not_modified = AsyncMock()
    mock_query_not_modified.message.text = "Old text"
    mock_query_not_modified.message.reply_markup = None
    mock_query_not_modified.edit_message_text.side_effect = BadRequest("Message is not modified")
    
    result = await safe_edit_message(mock_query_not_modified, "New text")
    print(f"✅ 'Message not modified' error handled: {result}")


async def test_error_decorator():
    """Test the error handling decorator"""
    print("\n=== Testing log_telegram_errors decorator ===")
    
    @log_telegram_errors(logger)
    async def test_function_success():
        return "success"
    
    @log_telegram_errors(logger)
    async def test_function_ignorable_error():
        raise BadRequest("Query is too old")
    
    @log_telegram_errors(logger)
    async def test_function_serious_error():
        raise BadRequest("Forbidden: bot was blocked")
    
    # Test successful execution
    result = await test_function_success()
    print(f"✅ Successful function execution: {result}")
    
    # Test ignorable error
    result = await test_function_ignorable_error()
    print(f"✅ Ignorable error handled: {result}")
    
    # Test serious error
    mock_update = MagicMock()
    mock_update.callback_query = AsyncMock()
    result = await test_function_serious_error()
    print(f"✅ Serious error handled: {result}")


def test_callback_pattern_validation():
    """Test callback pattern validation"""
    print("\n=== Testing callback pattern validation ===")
    
    test_cases = [
        ("answer_location_preference_pp", True),
        ("answer_budget_range_low", True),
        ("FILTER_city", True),
        ("FILTERS_HOME", True),
        ("BACK", True),
        ("DET_123", True),
        ("SEC_456_2", True),
        ("QS_SURPRISE", True),
        ("FB_UP", True),
        ("MORE_123", True),
        ("lang_kh", True),
        ("WIZARD_START", True),
        ("invalid_pattern", False),
        ("UNKNOWN_CALLBACK", False),
        ("", False)
    ]
    
    passed = 0
    total = len(test_cases)
    
    for callback_data, expected in test_cases:
        result = validate_callback_pattern(callback_data)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{callback_data}' -> {result} (expected {expected})")
        if result == expected:
            passed += 1
    
    print(f"\n📊 Pattern validation: {passed}/{total} tests passed")


def test_pattern_compilation():
    """Test that all regex patterns compile correctly"""
    print("\n=== Testing pattern compilation ===")
    
    import re
    
    for pattern_name, pattern in CALLBACK_PATTERNS.items():
        try:
            re.compile(pattern)
            print(f"✅ Pattern '{pattern_name}' compiles successfully")
        except re.error as e:
            print(f"❌ Pattern '{pattern_name}' failed to compile: {e}")


async def test_background_task_offloading():
    """Test background task offloading"""
    print("\n=== Testing background task offloading ===")
    
    async def dummy_heavy_task():
        await asyncio.sleep(0.1)  # Simulate heavy work
        print("✅ Background task completed")
        return "task_result"
    
    # Test successful task offloading
    offload_heavy_task(dummy_heavy_task())
    print("✅ Background task offloaded successfully")
    
    # Give the task time to complete
    await asyncio.sleep(0.2)


async def integration_test():
    """Run a complete integration test of the error handling system"""
    print("\n🚀 Starting EduGuideBot Error Handling Integration Test")
    print("=" * 60)
    
    try:
        await test_safe_answer_callback()
        await test_safe_edit_message()
        await test_error_decorator()
        test_callback_pattern_validation()
        test_pattern_compilation()
        await test_background_task_offloading()
        
        print("\n" + "=" * 60)
        print("🎉 All tests completed successfully!")
        print("✅ EduGuideBot error handling system is working correctly")
        print("✅ Ready for production deployment")
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        raise


def main():
    """Main entry point"""
    print("EduGuideBot Telegram Error Handling Test Suite")
    print("Testing comprehensive error handling and recovery mechanisms")
    
    # Run the integration test
    asyncio.run(integration_test())


if __name__ == "__main__":
    main()
