#!/usr/bin/env python3
"""
<PERSON>ript to replace all query.edit_message_text() calls with safe_edit_message() calls
"""

import re
import sys
from pathlib import Path

def main():
    """Replace all edit_message_text calls with safe_edit_message"""
    
    handlers_file = Path(__file__).parents[1] / "src" / "bot" / "handlers.py"
    
    # Read the current file
    with open(handlers_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern 1: Simple single-line calls
    # await query.edit_message_text(text="...")
    pattern1 = r'await query\.edit_message_text\(text=([^)]+)\)'
    replacement1 = r'await safe_edit_message(query, text=\1)'
    content = re.sub(pattern1, replacement1, content)
    
    # Pattern 2: Multi-line calls with text, reply_markup, parse_mode
    # await query.edit_message_text(
    #     text=message_text,
    #     reply_markup=reply_markup,
    #     parse_mode="Markdown"
    # )
    pattern2 = r'await query\.edit_message_text\(\s*\n\s*text=([^,\n]+),?\s*\n\s*reply_markup=([^,\n]+),?\s*\n\s*parse_mode=([^,\n)]+)\s*\n\s*\)'
    replacement2 = r'await safe_edit_message(\n        query,\n        text=\1,\n        reply_markup=\2,\n        parse_mode=\3\n    )'
    content = re.sub(pattern2, replacement2, content, flags=re.MULTILINE)
    
    # Pattern 3: Multi-line calls with just text and reply_markup
    pattern3 = r'await query\.edit_message_text\(\s*\n\s*text=([^,\n]+),?\s*\n\s*reply_markup=([^,\n)]+)\s*\n\s*\)'
    replacement3 = r'await safe_edit_message(\n        query,\n        text=\1,\n        reply_markup=\2\n    )'
    content = re.sub(pattern3, replacement3, content, flags=re.MULTILINE)
    
    # Pattern 4: Simple calls with text and reply_markup on same line
    pattern4 = r'await query\.edit_message_text\(\s*text=([^,]+),\s*reply_markup=([^)]+)\s*\)'
    replacement4 = r'await safe_edit_message(query, text=\1, reply_markup=\2)'
    content = re.sub(pattern4, replacement4, content)
    
    # Manual replacements for complex cases
    manual_replacements = [
        # Show recommendations case
        (
            'if query:\n            await query.edit_message_text(\n                text=message_text,\n                reply_markup=reply_markup,\n                parse_mode="Markdown"\n            )',
            'if query:\n            await safe_edit_message(\n                query,\n                text=message_text,\n                reply_markup=reply_markup,\n                parse_mode="Markdown"\n            )'
        ),
        # Simple title case
        (
            'if query:\n        await query.edit_message_text(text=title_text)',
            'if query:\n        await safe_edit_message(query, text=title_text)'
        ),
        # Browse majors case with inline keyboard
        (
            'await query.edit_message_text(\n        text=f"🗂 {tr(\'browse_majors_coming_soon\', lang)}\\n\\n{tr(\'use_quiz_instead\', lang)}",\n        reply_markup=InlineKeyboardMarkup([[\n            InlineKeyboardButton(f"🧮 {tr(\'take_quiz\', lang)}", callback_data="START_QUIZ"),\n            InlineKeyboardButton(f"⬅️ {tr(\'back\', lang)}", callback_data="HOME")\n        ]])\n    )',
            'await safe_edit_message(\n        query,\n        text=f"🗂 {tr(\'browse_majors_coming_soon\', lang)}\\n\\n{tr(\'use_quiz_instead\', lang)}",\n        reply_markup=InlineKeyboardMarkup([[\n            InlineKeyboardButton(f"🧮 {tr(\'take_quiz\', lang)}", callback_data="START_QUIZ"),\n            InlineKeyboardButton(f"⬅️ {tr(\'back\', lang)}", callback_data="HOME")\n        ]])\n    )'
        ),
        # Shortlist empty case
        (
            'await query.edit_message_text(\n            text=f"⭐ {tr(\'shortlist_empty\', lang)}\\n\\n{tr(\'save_majors_hint\', lang)}",\n            reply_markup=InlineKeyboardMarkup([[\n                InlineKeyboardButton(f"🧮 {tr(\'take_quiz\', lang)}", callback_data="START_QUIZ"),\n                InlineKeyboardButton(f"⬅️ {tr(\'back\', lang)}", callback_data="HOME")\n            ]])\n        )',
            'await safe_edit_message(\n            query,\n            text=f"⭐ {tr(\'shortlist_empty\', lang)}\\n\\n{tr(\'save_majors_hint\', lang)}",\n            reply_markup=InlineKeyboardMarkup([[\n                InlineKeyboardButton(f"🧮 {tr(\'take_quiz\', lang)}", callback_data="START_QUIZ"),\n                InlineKeyboardButton(f"⬅️ {tr(\'back\', lang)}", callback_data="HOME")\n            ]])\n        )'
        ),
        # Help callback case
        (
            'await query.edit_message_text(\n        text=help_text,\n        reply_markup=InlineKeyboardMarkup([[\n            InlineKeyboardButton(f"⬅️ {tr(\'back\', lang)}", callback_data="HOME")\n        ]]),\n        parse_mode="Markdown"\n    )',
            'await safe_edit_message(\n        query,\n        text=help_text,\n        reply_markup=InlineKeyboardMarkup([[\n            InlineKeyboardButton(f"⬅️ {tr(\'back\', lang)}", callback_data="HOME")\n        ]]),\n        parse_mode="Markdown"\n    )'
        ),
        # Unknown callback case
        (
            'await query.edit_message_text(\n        text=f"❌ {tr(\'unknown_action\', lang)}\\n\\n{tr(\'please_restart\', lang)}",\n        reply_markup=InlineKeyboardMarkup([[\n            InlineKeyboardButton(f"↩️ {tr(\'restart\', lang)}", callback_data="RESTART")\n        ]])\n    )',
            'await safe_edit_message(\n        query,\n        text=f"❌ {tr(\'unknown_action\', lang)}\\n\\n{tr(\'please_restart\', lang)}",\n        reply_markup=InlineKeyboardMarkup([[\n            InlineKeyboardButton(f"↩️ {tr(\'restart\', lang)}", callback_data="RESTART")\n        ]])\n    )'
        )
    ]
    
    # Apply manual replacements
    for old_text, new_text in manual_replacements:
        if old_text in content:
            content = content.replace(old_text, new_text)
            print(f"✅ Applied manual fix: {old_text[:50]}...")
        else:
            print(f"⚠️ Manual pattern not found: {old_text[:50]}...")
    
    # Final catch-all pattern for any remaining cases
    remaining_pattern = r'await query\.edit_message_text\('
    if re.search(remaining_pattern, content):
        print("⚠️ Some query.edit_message_text calls may still remain - manual review needed")
        # Find and show remaining cases
        matches = re.finditer(remaining_pattern, content)
        for match in matches:
            start = max(0, match.start() - 50)
            end = min(len(content), match.end() + 100)
            context = content[start:end].replace('\n', '\\n')
            print(f"   Remaining: ...{context}...")
    
    # Write the updated content back
    with open(handlers_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\n🎉 Updated {handlers_file}")
    print("All edit_message_text calls should now use safe_edit_message!")

if __name__ == "__main__":
    main()
