#!/usr/bin/env python3
"""
Auto-Patcher for Critical Data Gaps
Automatically fixes missing/invalid critical fields in university data
"""

import json
import csv
import sys
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import statistics


def calculate_dataset_medians(data_dir: Path) -> Dict[str, Any]:
    """
    Calculate median values from the full dataset for imputation
    
    Args:
        data_dir: Path to raw data directory
        
    Returns:
        Dictionary of field -> median value
    """
    tuition_values = []
    employment_values = []
    
    json_files = list(data_dir.glob('**/*.json'))
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'programmes' in data:
                for programme in data['programmes']:
                    # Collect tuition fees
                    if 'tuition_fees_usd' in programme:
                        tuition = programme['tuition_fees_usd']
                        if tuition and tuition not in [0, '0', '']:
                            try:
                                # Extract numeric value
                                tuition_str = str(tuition).replace('$', '').replace(',', '')
                                tuition_num = float(tuition_str)
                                if tuition_num > 0:
                                    tuition_values.append(tuition_num)
                            except (ValueError, TypeError):
                                pass
                    
                    # Collect employment rates
                    if 'employment_rate' in programme:
                        emp_rate = programme['employment_rate']
                        if emp_rate and emp_rate not in [0, '0', '0%', '']:
                            try:
                                # Extract percentage
                                emp_str = str(emp_rate).replace('%', '')
                                emp_num = float(emp_str)
                                if 0 < emp_num <= 100:
                                    employment_values.append(emp_num)
                            except (ValueError, TypeError):
                                pass
        
        except (json.JSONDecodeError, Exception):
            continue
    
    medians = {}
    
    if tuition_values:
        medians['tuition_fees_usd'] = round(statistics.median(tuition_values))
    else:
        medians['tuition_fees_usd'] = 1500  # Default fallback
    
    if employment_values:
        medians['employment_rate'] = round(statistics.median(employment_values))
    else:
        medians['employment_rate'] = 75  # Default fallback
    
    return medians


def patch_record(data: Dict[str, Any], medians: Dict[str, Any], file_path: str) -> List[Tuple[str, str, str, str]]:
    """
    Patch a single university record
    
    Args:
        data: University data dictionary
        medians: Median values for imputation
        file_path: Path to the file being patched
        
    Returns:
        List of (field_name, old_value, new_value, action) tuples
    """
    changes = []
    
    if 'programmes' not in data:
        return changes
    
    programmes = data['programmes']
    if not isinstance(programmes, list):
        return changes
    
    for i, programme in enumerate(programmes):
        prog_prefix = f'programmes[{i}]'
        
        # Patch tuition_fees_usd
        if 'tuition_fees_usd' in programme:
            tuition = programme['tuition_fees_usd']
            if tuition in [None, '', 0, '0']:
                old_value = str(tuition)
                new_value = f"${medians['tuition_fees_usd']}"
                programme['tuition_fees_usd'] = new_value
                changes.append((f'{prog_prefix}.tuition_fees_usd', old_value, new_value, 'imputed_median'))
        else:
            new_value = f"${medians['tuition_fees_usd']}"
            programme['tuition_fees_usd'] = new_value
            changes.append((f'{prog_prefix}.tuition_fees_usd', 'missing', new_value, 'added_median'))
        
        # Patch employment_rate
        if 'employment_rate' in programme:
            emp_rate = programme['employment_rate']
            if emp_rate in [None, '', 0, '0', '0%']:
                old_value = str(emp_rate)
                new_value = f"{medians['employment_rate']}%"
                programme['employment_rate'] = new_value
                changes.append((f'{prog_prefix}.employment_rate', old_value, new_value, 'imputed_median'))
        else:
            new_value = f"{medians['employment_rate']}%"
            programme['employment_rate'] = new_value
            changes.append((f'{prog_prefix}.employment_rate', 'missing', new_value, 'added_median'))
        
        # Patch student_faculty_ratio
        if 'faculty_information' not in programme:
            programme['faculty_information'] = {}
        
        faculty_info = programme['faculty_information']
        if 'student_faculty_ratio' not in faculty_info or not faculty_info['student_faculty_ratio']:
            old_value = faculty_info.get('student_faculty_ratio', 'missing')
            new_value = "25:1"
            faculty_info['student_faculty_ratio'] = new_value
            changes.append((f'{prog_prefix}.faculty_information.student_faculty_ratio', 
                          str(old_value), new_value, 'imputed_default'))
    
    return changes


def patch_data_files(raw_dir: Path, clean_dir: Path, log_path: str) -> Tuple[int, int]:
    """
    Patch all JSON files and save to clean directory
    
    Args:
        raw_dir: Path to raw data directory
        clean_dir: Path to clean data directory
        log_path: Path to save change log
        
    Returns:
        Tuple of (files_processed, total_changes)
    """
    # Calculate medians first
    print("Calculating dataset medians for imputation...")
    medians = calculate_dataset_medians(raw_dir)
    print(f"Medians: tuition=${medians['tuition_fees_usd']}, employment={medians['employment_rate']}%")
    
    # Create clean directory
    clean_dir.mkdir(parents=True, exist_ok=True)
    
    # Prepare change log
    log_path = Path(log_path)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    all_changes = []
    files_processed = 0
    total_changes = 0
    
    # Find all JSON files
    json_files = list(raw_dir.glob('**/*.json'))
    print(f"Processing {len(json_files)} JSON files...")
    
    for json_file in json_files:
        try:
            # Read original file
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Patch the data
            changes = patch_record(data, medians, str(json_file))
            
            # Create clean directory structure
            relative_path = json_file.relative_to(raw_dir)
            clean_file = clean_dir / relative_path
            clean_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Save patched file
            with open(clean_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Log changes
            for field, old_val, new_val, action in changes:
                all_changes.append({
                    'file': str(relative_path),
                    'field': field,
                    'old_value': old_val,
                    'new_value': new_val,
                    'action': action
                })
            
            files_processed += 1
            total_changes += len(changes)
            
            if changes:
                print(f"  {relative_path}: {len(changes)} changes")
        
        except (json.JSONDecodeError, Exception) as e:
            print(f"  Error processing {json_file}: {e}")
            continue
    
    # Save change log
    if all_changes:
        with open(log_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['file', 'field', 'old_value', 'new_value', 'action'])
            writer.writeheader()
            writer.writerows(all_changes)
        
        print(f"Change log saved to {log_path}")
    
    return files_processed, total_changes


def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Auto-patch critical data gaps')
    parser.add_argument('--raw-dir', default='data/raw', help='Raw data directory')
    parser.add_argument('--clean-dir', default='data/clean', help='Clean data output directory')
    parser.add_argument('--log', default='build/auto_patch_log.csv', help='Change log file')
    
    args = parser.parse_args()
    
    raw_dir = Path(args.raw_dir)
    clean_dir = Path(args.clean_dir)
    
    if not raw_dir.exists():
        print(f"Error: Raw data directory not found: {raw_dir}")
        sys.exit(1)
    
    print("Starting auto-patch process...")
    
    # Run patching
    files_processed, total_changes = patch_data_files(raw_dir, clean_dir, args.log)
    
    print(f"\n✅ Auto-patch completed:")
    print(f"  Files processed: {files_processed}")
    print(f"  Total changes: {total_changes}")
    print(f"  Clean data saved to: {clean_dir}")
    
    if total_changes > 0:
        print(f"  Change log: {args.log}")


if __name__ == '__main__':
    main()
