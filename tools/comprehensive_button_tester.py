#!/usr/bin/env python3
"""
EduGuideBot Comprehensive Button Function Tester - Phase 2
Tests all interactive buttons and callback functions systematically
"""

import asyncio
import logging
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, List, Any

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.bot.handlers import (
    detail_callback, compare_callback, filters_callback, save_callback,
    remove_callback, section_callback, trending_callback, feedback_callback,
    more_info_callback, back_callback, refresh_callback, restart_callback,
    surprise_me_callback, browse_majors_callback, shortlist_callback,
    help_callback, language_toggle_callback, quick_start_callback,
    wizard_start_callback, share_start_handler, unknown_callback
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ComprehensiveButtonTester:
    """Comprehensive tester for all button functions"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.test_results = {}
        
    def log_error(self, test_name: str, error: str):
        """Log an error during testing"""
        self.errors.append(f"{test_name}: {error}")
        logger.error(f"❌ {test_name}: {error}")
        
    def log_warning(self, test_name: str, warning: str):
        """Log a warning during testing"""
        self.warnings.append(f"{test_name}: {warning}")
        logger.warning(f"⚠️ {test_name}: {warning}")
        
    def log_success(self, test_name: str, message: str = ""):
        """Log a successful test"""
        self.test_results[test_name] = "PASS"
        logger.info(f"✅ {test_name}: {message}")

    async def test_detail_callbacks(self):
        """Test detail view callbacks"""
        test_name = "Detail View Callbacks"
        
        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {
                'last_recs': {
                    '123': {
                        'major_id': '123',
                        'major_name_en': 'Computer Science',
                        'hybrid_score': 0.95
                    }
                }
            }
            mock_update.callback_query = AsyncMock()
            mock_update.callback_query.data = "DET_123"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.ui.create_detail_view') as mock_detail:
                
                mock_lang.return_value = 'en'
                mock_detail.return_value = ("Detail View", MagicMock())
                
                await detail_callback(mock_update, mock_context)
                
            self.log_success(test_name, "Detail callbacks work correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_comparison_callbacks(self):
        """Test comparison callbacks"""
        test_name = "Comparison Callbacks"
        
        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {
                'last_recs': {
                    '123': {'major_id': '123', 'major_name_en': 'Computer Science', 'hybrid_score': 0.95},
                    '456': {'major_id': '456', 'major_name_en': 'Software Engineering', 'hybrid_score': 0.90}
                }
            }
            mock_update.callback_query = AsyncMock()
            mock_update.callback_query.data = "CMP_123"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.ui.create_comparison_view') as mock_compare, \
                 patch('src.bot.ui.tr') as mock_tr:
                
                mock_lang.return_value = 'en'
                mock_compare.return_value = "Comparison View"
                mock_tr.return_value = "Back to list"
                
                await compare_callback(mock_update, mock_context)
                
            self.log_success(test_name, "Comparison callbacks work correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_filter_callbacks(self):
        """Test filter callbacks"""
        test_name = "Filter Callbacks"
        
        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {}
            mock_update.callback_query = AsyncMock()
            mock_update.callback_query.data = "FILTERS_HOME"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.ui.create_filters_menu') as mock_filters:
                
                mock_lang.return_value = 'en'
                mock_filters.return_value = ("Filters Menu", MagicMock())
                
                await filters_callback(mock_update, mock_context)
                
            self.log_success(test_name, "Filter callbacks work correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_save_remove_callbacks(self):
        """Test save and remove callbacks"""
        test_name = "Save/Remove Callbacks"
        
        try:
            # Test save callback
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {'shortlist': []}
            mock_update.callback_query = AsyncMock()
            mock_update.callback_query.data = "SAVE_123"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang:
                
                mock_lang.return_value = 'en'
                
                await save_callback(mock_update, mock_context)
                
                # Check that item was added to shortlist
                if '123' not in mock_context.user_data['shortlist']:
                    self.log_error(test_name, "Save callback didn't add item to shortlist")
                    return
            
            # Test remove callback
            mock_context.user_data = {
                'shortlist': ['123'],
                'last_recs': {'123': {'major_id': '123'}}
            }
            mock_update.callback_query.data = "REMOVE_123"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang:
                
                mock_lang.return_value = 'en'
                
                await remove_callback(mock_update, mock_context)
                
                # Check that item was removed
                if '123' in mock_context.user_data.get('shortlist', []):
                    self.log_error(test_name, "Remove callback didn't remove item from shortlist")
                    return
                
            self.log_success(test_name, "Save/Remove callbacks work correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_navigation_callbacks(self):
        """Test navigation callbacks (back, refresh, restart)"""
        test_name = "Navigation Callbacks"
        
        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {
                'last_recs': {'123': {'major_id': '123', 'hybrid_score': 0.95}},
                'last_answers': {'location_preference': 'pp'}
            }
            mock_update.callback_query = AsyncMock()
            
            # Test back callback
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.ui.create_recommendations_view') as mock_view:
                
                mock_lang.return_value = 'en'
                mock_view.return_value = ("Recommendations", MagicMock())
                
                await back_callback(mock_update, mock_context)
            
            # Test refresh callback
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.core.hybrid_recommender.get_recommendations') as mock_rec, \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.utils.cache_recommendations'), \
                 patch('src.bot.utils.cache_user_answers'), \
                 patch('src.bot.ui.create_recommendations_view') as mock_view:
                
                mock_rec.return_value = [{'major_id': '123', 'hybrid_score': 0.95}]
                mock_lang.return_value = 'en'
                mock_view.return_value = ("Refreshed Recommendations", MagicMock())
                
                await refresh_callback(mock_update, mock_context)
            
            # Test restart callback
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.start_assessment', new_callable=AsyncMock) as mock_start:
                
                await restart_callback(mock_update, mock_context)
                mock_start.assert_called_once()
                
            self.log_success(test_name, "Navigation callbacks work correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_special_feature_callbacks(self):
        """Test special feature callbacks (surprise, browse, shortlist, help)"""
        test_name = "Special Feature Callbacks"
        
        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {}
            mock_update.callback_query = AsyncMock()
            
            # Test surprise_me_callback
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.core.hybrid_recommender.get_recommendations') as mock_rec, \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.utils.cache_recommendations'), \
                 patch('src.bot.utils.cache_user_answers'), \
                 patch('src.bot.ui.create_recommendations_view') as mock_view, \
                 patch('src.bot.ui.tr') as mock_tr:
                
                mock_rec.return_value = [{'major_id': '123', 'hybrid_score': 0.95}]
                mock_lang.return_value = 'en'
                mock_view.return_value = ("Surprise Recommendations", MagicMock())
                mock_tr.return_value = "Surprise recommendations"
                
                await surprise_me_callback(mock_update, mock_context)
            
            # Test browse_majors_callback
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.ui.tr') as mock_tr:
                
                mock_lang.return_value = 'en'
                mock_tr.side_effect = lambda key, lang: f"Translated: {key}"
                
                await browse_majors_callback(mock_update, mock_context)
            
            # Test help_callback
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.ui.tr') as mock_tr:
                
                mock_lang.return_value = 'en'
                mock_tr.side_effect = lambda key, lang: f"Translated: {key}"
                
                await help_callback(mock_update, mock_context)
                
            self.log_success(test_name, "Special feature callbacks work correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_language_and_settings(self):
        """Test language toggle and settings"""
        test_name = "Language and Settings"
        
        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {'language': 'kh'}
            mock_update.callback_query = AsyncMock()
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.ui.create_home_screen') as mock_home:
                
                mock_lang.return_value = 'kh'
                mock_home.return_value = ("Home Screen", MagicMock())
                
                await language_toggle_callback(mock_update, mock_context)
                
                # Check that language was toggled
                if mock_context.user_data.get('language') != 'en':
                    self.log_error(test_name, "Language toggle didn't change language")
                    return
                    
            self.log_success(test_name, "Language and settings work correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def test_error_handling(self):
        """Test error handling and unknown callbacks"""
        test_name = "Error Handling"
        
        try:
            mock_update = MagicMock()
            mock_context = MagicMock()
            mock_context.user_data = {}
            mock_update.callback_query = AsyncMock()
            mock_update.callback_query.data = "UNKNOWN_CALLBACK"
            
            with patch('src.bot.handlers.safe_answer_callback', new_callable=AsyncMock), \
                 patch('src.bot.handlers.safe_edit_message', new_callable=AsyncMock), \
                 patch('src.bot.handlers.get_lang') as mock_lang, \
                 patch('src.bot.ui.tr') as mock_tr:
                
                mock_lang.return_value = 'en'
                mock_tr.side_effect = lambda key, lang: f"Translated: {key}"
                
                await unknown_callback(mock_update, mock_context)
                
            self.log_success(test_name, "Error handling works correctly")
            
        except Exception as e:
            self.log_error(test_name, f"Exception during test: {e}")

    async def run_comprehensive_test(self):
        """Run all button function tests"""
        print("🚀 Starting EduGuideBot Comprehensive Button Function Testing - Phase 2")
        print("=" * 80)
        
        # Run all tests
        await self.test_detail_callbacks()
        await self.test_comparison_callbacks()
        await self.test_filter_callbacks()
        await self.test_save_remove_callbacks()
        await self.test_navigation_callbacks()
        await self.test_special_feature_callbacks()
        await self.test_language_and_settings()
        await self.test_error_handling()
        
        # Generate report
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE BUTTON TESTING REPORT")
        print("=" * 80)
        
        total_tests = len(self.test_results) + len(self.errors)
        passed_tests = len(self.test_results)
        
        print(f"✅ Tests Passed: {passed_tests}")
        print(f"❌ Tests Failed: {len(self.errors)}")
        print(f"⚠️ Warnings: {len(self.warnings)}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%" if total_tests > 0 else "📈 Success Rate: 0%")
        
        if self.errors:
            print("\n🔥 CRITICAL ISSUES FOUND:")
            for error in self.errors:
                print(f"   ❌ {error}")
        
        if self.warnings:
            print("\n⚠️ WARNINGS:")
            for warning in self.warnings:
                print(f"   ⚠️ {warning}")
        
        if not self.errors:
            print("\n🎉 ALL BUTTON FUNCTION TESTS PASSED!")
            print("✅ All interactive buttons work correctly")
            print("✅ All callback handlers are functional")
            print("✅ Error handling is robust")
            print("✅ Navigation flows work properly")
            print("✅ EduGuideBot is ready for production!")
        else:
            print("\n🚨 BUTTON FUNCTION ISSUES FOUND")
            print("❌ Some interactive features have problems")
            print("🔧 Fix required before deployment")
        
        return len(self.errors) == 0


async def main():
    """Main entry point"""
    tester = ComprehensiveButtonTester()
    success = await tester.run_comprehensive_test()
    
    if success:
        print("\n🎉 EduGuideBot Assessment Flow and Button Functions: FULLY FUNCTIONAL!")
        print("✅ Ready for production deployment")
        return 0
    else:
        print("\n🛑 Issues found that need to be addressed")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
