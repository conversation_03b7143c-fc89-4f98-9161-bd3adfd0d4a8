# EduGuideBot Makefile
# Data processing, quality assurance, and deployment targets

.PHONY: data-clean audit patch docker-build docker-test metrics-clean assets help

# Default target
help:
	@echo "Available targets:"
	@echo "  data-clean    - Run data audit and auto-patch process"
	@echo "  audit         - Run data quality audit only"
	@echo "  patch         - Run auto-patch only (requires audit first)"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-test   - Build and test Docker image (dry-run)"
	@echo "  metrics-clean - Clean metrics log file"
	@echo "  assets        - Build assets (use LANG=en for English, LANG=kh for Khmer)"
	@echo "  help          - Show this help message"

# Main data cleaning pipeline
data-clean:
	@echo "🔍 Running data quality audit..."
	python scripts/data_audit.py
	@echo "🔧 Running auto-patch..."
	python tools/auto_patch.py
	@echo "✅ Data cleaning completed - No blocking issues"

# Individual targets
audit:
	python scripts/data_audit.py

patch:
	python tools/auto_patch.py

# Docker targets
docker-build:
	docker build . -t eduguidebot:local

docker-test:
	docker run --rm eduguidebot:local

# Metrics targets
metrics-clean:
	rm -rf logs/metrics.ndjson

# Asset targets
LANG ?= kh
assets:
	python tools/data_pipeline.py --lang $(LANG)
