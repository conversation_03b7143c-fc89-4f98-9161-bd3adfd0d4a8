"""
Global pytest configuration and fixtures
"""

import warnings
import pytest

# Suppress Telegram PTBUserWarning in tests
try:
    from telegram.ext import PTBUserWarning
    
    @pytest.fixture(autouse=True)
    def _suppress_ptb_warnings():
        warnings.filterwarnings("ignore", category=PTBUserWarning)
except ImportError:
    # If telegram is not available, skip the warning suppression
    pass

# Suppress other noisy warnings in tests
@pytest.fixture(autouse=True)
def _suppress_deprecation_warnings():
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
