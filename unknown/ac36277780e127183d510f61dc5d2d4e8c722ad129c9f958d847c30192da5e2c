"""
Additional Coverage Tests
Tests to cover remaining missing lines to reach 95% coverage
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open
import os

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))


class TestAdditionalCoverage:
    """Test cases to cover remaining missing lines"""
    
    def test_mcda_score_with_roi_bonus(self):
        """Test MCDA score calculation with ROI bonus"""
        from src.core.mcda import score
        
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'low',
            'field_of_interest': 'stem',
            'career_goal': 'high_salary'
        }
        
        # Programme with high employment rate and low tuition (good ROI)
        programme = {
            'major_name_en': 'Computer Science',
            'city': 'ភ្នំពេញ',
            'field_tag': 'STEM',
            'tuition_bracket': 'Low',
            'employment_rate': '95%',
            'tuition_fees_usd': '500'
        }
        
        result = score(user_answers, programme)
        assert isinstance(result, float)
        assert 0.0 <= result <= 1.0
    
    def test_mcda_score_with_scholarship_bonus(self):
        """Test MCDA score calculation with scholarship bonus"""
        from src.core.mcda import score
        
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'low',
            'field_of_interest': 'stem'
        }
        
        # Programme with scholarship available
        programme = {
            'major_name_en': 'Engineering',
            'city': 'ភ្នំពេញ',
            'field_tag': 'STEM',
            'tuition_bracket': 'High',
            'has_scholarship': True,
            'scholarship_availability': True
        }
        
        result = score(user_answers, programme)
        assert isinstance(result, float)
        assert 0.0 <= result <= 1.0
    
    def test_mcda_score_with_language_bonus(self):
        """Test MCDA score calculation with language bonus"""
        from src.core.mcda import score
        
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'mid',
            'field_of_interest': 'business',
            'language_preference': 'khmer'
        }
        
        # Programme with Khmer language instruction
        programme = {
            'major_name_en': 'Business Administration',
            'city': 'ភ្នំពេញ',
            'field_tag': 'Business',
            'tuition_bracket': 'Medium',
            'language_of_instruction': ['Khmer', 'English']
        }
        
        result = score(user_answers, programme)
        assert isinstance(result, float)
        assert 0.0 <= result <= 1.0
    
    def test_mcda_score_with_all_bonuses(self):
        """Test MCDA score calculation with all possible bonuses"""
        from src.core.mcda import score
        
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'low',
            'field_of_interest': 'stem',
            'career_goal': 'high_salary',
            'language_preference': 'khmer'
        }
        
        # Programme with all bonuses
        programme = {
            'major_name_en': 'Computer Science',
            'city': 'ភ្នំពេញ',
            'field_tag': 'STEM',
            'tuition_bracket': 'Low',
            'employment_rate': '98%',
            'tuition_fees_usd': '400',
            'has_scholarship': True,
            'scholarship_availability': True,
            'language_of_instruction': ['Khmer', 'English']
        }
        
        result = score(user_answers, programme)
        assert isinstance(result, float)
        assert 0.0 <= result <= 1.0
    
    def test_hybrid_recommender_ml_version_2(self):
        """Test hybrid recommender with ML_VERSION=2"""
        from src.core.hybrid_recommender import _get_ml_scores
        
        programmes = [
            {'major_id': 'test1', 'major_name_en': 'Test 1'},
            {'major_id': 'test2', 'major_name_en': 'Test 2'}
        ]
        
        user_answers = {'location_preference': 'pp'}
        
        # Test with ML_VERSION=2
        with patch.dict('os.environ', {'ML_VERSION': '2'}):
            with patch('pathlib.Path.exists', return_value=False):  # No models exist
                scores = _get_ml_scores(user_answers, programmes)
                assert len(scores) == 2
                assert all(score == 0.5 for score in scores)
    
    def test_hybrid_recommender_ml_version_1(self):
        """Test hybrid recommender with ML_VERSION=1"""
        from src.core.hybrid_recommender import _get_ml_scores
        
        programmes = [{'major_id': 'test1', 'major_name_en': 'Test 1'}]
        user_answers = {'location_preference': 'pp'}
        
        # Test with ML_VERSION=1
        with patch.dict('os.environ', {'ML_VERSION': '1'}):
            with patch('pathlib.Path.exists', return_value=False):  # No models exist
                scores = _get_ml_scores(user_answers, programmes)
                assert len(scores) == 1
                assert scores[0] == 0.5
    
    def test_ml_reranker_with_valid_model(self):
        """Test MLReranker with a valid model"""
        from src.core.ml_reranker import MLReranker
        
        # Mock a valid model and scaler
        mock_model = MagicMock()
        mock_model.predict.return_value = [0.8, 0.6]
        mock_scaler = MagicMock()
        mock_scaler.transform.return_value = [[0.5, 0.5], [0.3, 0.7]]
        
        with patch('pathlib.Path.exists', return_value=True):
            with patch('joblib.load', side_effect=[mock_model, mock_scaler]):
                reranker = MLReranker("fake_model.joblib")
                
                assert reranker.model is not None
                assert reranker.scaler is not None
                
                user_answers = {'location_preference': 'pp'}
                programmes = [
                    {'major_id': 'test1', 'major_name_en': 'Test 1', 'mcda_score': 0.7},
                    {'major_id': 'test2', 'major_name_en': 'Test 2', 'mcda_score': 0.5}
                ]
                
                result = reranker.rank(user_answers, programmes, top_k=2)
                
                assert len(result) == 2
                for prog in result:
                    assert 'ml_score' in prog
                    assert isinstance(prog['ml_score'], float)
                    assert 0.0 <= prog['ml_score'] <= 1.0
    
    def test_metrics_file_operations(self):
        """Test metrics file operations for coverage"""
        from src.infra.metrics import log_event, read_metrics, clear_metrics, METRICS_FILE

        # Clear any existing metrics
        clear_metrics()

        # Test logging multiple events
        log_event("test_event_1", {"data": "value1"})
        log_event("test_event_2", {"data": "value2"})

        # Read metrics
        metrics = read_metrics()
        assert len(metrics) >= 2

        # Test that file exists
        assert METRICS_FILE.exists()

        # Clear metrics again
        clear_metrics()
        assert not METRICS_FILE.exists()
    
    def test_data_loader_validate_program_edge_cases(self):
        """Test data loader validation with edge cases"""
        from src.core.data_loader import validate_program_data

        # Test with empty dict (avoid None which causes AttributeError)
        assert validate_program_data({}) == False

        # Test with only university_id
        assert validate_program_data({'university_id': 'test'}) == False

        # Test with only major_id
        assert validate_program_data({'major_id': 'test'}) == False

        # Test with both IDs but missing other required fields
        program_with_ids = {
            'university_id': 'test_uni',
            'major_id': 'test_major'
            # Missing other required fields
        }
        assert validate_program_data(program_with_ids) == False

        # Test with partial required fields
        program_partial = {
            'university_name_kh': 'សាកលវិទ្យាល័យ',
            'university_name_en': 'University',
            # Missing major names and city
        }
        assert validate_program_data(program_partial) == False


if __name__ == '__main__':
    pytest.main([__file__])
