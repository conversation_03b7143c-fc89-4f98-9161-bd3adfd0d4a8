# EduGuideBot Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM python:3.11-slim as builder

# Set working directory
WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --user -r requirements.txt

# Production stage
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r bot && useradd -r -g bot bot

# Copy Python packages from builder stage
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY . .

# Set ownership
RUN chown -R bot:bot /app

# Switch to non-root user
USER bot

# Add local Python packages to PATH
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python src/bot/app.py --dry-run || exit 1

# Default command (dry-run mode)
CMD ["python", "src/bot/app.py", "--dry-run"]
