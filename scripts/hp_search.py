#!/usr/bin/env python3
"""
Multi-Model Hyperparameter Search for ML Re-ranker
Searches across RandomForest, ExtraTrees, and GradientBoosting
"""

import json
import pandas as pd
import numpy as np
from pathlib import Path
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.model_selection import RandomizedSearchCV, KFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.feature_schema import FEATURES


def hyperparameter_search(data_path: str, output_path: str) -> dict:
    """
    Perform multi-model hyperparameter search

    Args:
        data_path: Path to training CSV file
        output_path: Path to save results JSON

    Returns:
        dict: Best model and parameters
    """
    print("Loading training data...")
    df = pd.read_csv(data_path)

    # Extract features and labels
    X = df[FEATURES].values
    y = df['label'].values

    print(f"Dataset: {len(df)} examples, {len(FEATURES)} features")
    print(f"Label distribution: {np.bincount(y.astype(int))}")

    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # Define models and their parameter distributions
    models = {
        'RandomForest': {
            'estimator': RandomForestRegressor(random_state=42, n_jobs=-1),
            'params': {
                'n_estimators': [200, 400, 600],
                'max_depth': [8, 12, 16, None],
                'min_samples_split': [2, 5],
                'min_samples_leaf': [1, 2]
            }
        },
        'ExtraTrees': {
            'estimator': ExtraTreesRegressor(random_state=42, n_jobs=-1),
            'params': {
                'n_estimators': [200, 400, 600],
                'max_depth': [8, 12, 16, None],
                'min_samples_split': [2, 5],
                'min_samples_leaf': [1, 2]
            }
        },
        'GradientBoosting': {
            'estimator': GradientBoostingRegressor(random_state=42),
            'params': {
                'n_estimators': [300, 500],
                'learning_rate': [0.05, 0.1, 0.2],
                'max_depth': [3, 4],
                'subsample': [0.8, 1.0]
            }
        }
    }

    print("\nStarting multi-model hyperparameter search...")
    print("Models:", list(models.keys()))
    
    # Setup cross-validation
    cv = KFold(n_splits=5, shuffle=True, random_state=42)

    best_overall_score = -np.inf
    best_overall_model = None
    best_overall_params = None
    all_model_results = {}

    # Search each model
    for model_name, model_config in models.items():
        print(f"\n{'='*60}")
        print(f"Searching {model_name}...")
        print(f"{'='*60}")

        estimator = model_config['estimator']
        param_distributions = model_config['params']

        # Adjust n_iter based on model (GBR is slower)
        n_iter = 20 if model_name == 'GradientBoosting' else 25

        random_search = RandomizedSearchCV(
            estimator=estimator,
            param_distributions=param_distributions,
            n_iter=n_iter,
            cv=cv,
            scoring='r2',
            random_state=42,
            n_jobs=-1 if model_name != 'GradientBoosting' else 1,  # GBR doesn't parallelize well
            verbose=1
        )

        print(f"Running RandomizedSearchCV with n_iter={n_iter}, 5-fold CV...")

        # Fit the search
        random_search.fit(X_scaled, y)

        # Store results
        model_best_score = random_search.best_score_
        model_best_params = random_search.best_params_

        print(f"\n{model_name} Results:")
        print(f"  Best CV R²: {model_best_score:.4f}")
        print(f"  Best params: {model_best_params}")

        all_model_results[model_name] = {
            'best_score': float(model_best_score),
            'best_params': model_best_params,
            'model_type': model_name
        }

        # Track overall best
        if model_best_score > best_overall_score:
            best_overall_score = model_best_score
            best_overall_model = model_name
            best_overall_params = model_best_params

    print(f"\n{'='*80}")
    print(f"OVERALL BEST MODEL: {best_overall_model}")
    print(f"BEST CV R²: {best_overall_score:.4f}")
    print(f"BEST PARAMS: {best_overall_params}")
    print(f"{'='*80}")
    
    # Create results summary
    results_summary = {
        'best_model': best_overall_model,
        'best_params': best_overall_params,
        'best_cv_r2': float(best_overall_score),
        'all_model_results': all_model_results,
        'feature_names': FEATURES,
        'search_method': 'Multi-Model RandomizedSearchCV'
    }

    # Print comparison table
    print(f"\n{'='*60}")
    print("MODEL COMPARISON RESULTS")
    print(f"{'='*60}")
    print(f"{'Model':<20} {'Best R²':<12} {'Best Params (truncated)':<30}")
    print("-"*60)

    for model_name, results in all_model_results.items():
        params_str = str(results['best_params'])[:27] + "..." if len(str(results['best_params'])) > 30 else str(results['best_params'])
        marker = " ★" if model_name == best_overall_model else ""
        print(f"{model_name + marker:<20} {results['best_score']:<12.4f} {params_str:<30}")

    print("-"*60)
    print(f"WINNER: {best_overall_model} (R² = {best_overall_score:.4f})")
    
    # Save results
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f"\nResults saved to {output_path}")
    
    return results_summary


def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Hyperparameter search for RandomForest')
    parser.add_argument('--data', default='build/synth_train.csv', help='Training data CSV')
    parser.add_argument('--output', default='build/hp_results.json', help='Output JSON file')
    
    args = parser.parse_args()
    
    # Check input file exists
    if not Path(args.data).exists():
        print(f"Error: Training data not found: {args.data}")
        print("Run: python scripts/gen_synth_users.py --num 10000 --seed 42")
        sys.exit(1)
    
    # Run hyperparameter search
    results = hyperparameter_search(args.data, args.output)
    
    print(f"\nHyperparameter search completed!")
    print(f"Best parameters: {results['best_params']}")
    print(f"Best CV R²: {results['best_cv_r2']:.4f}")


if __name__ == '__main__':
    main()
