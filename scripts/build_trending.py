#!/usr/bin/env python3
"""
Build trending majors data from callback events log

This script analyzes user interaction logs to identify trending majors
and generates a JSON file for the trending carousel feature.

Usage:
    python scripts/build_trending.py

Output:
    data/cache/trending.json - Array of trending major objects

Requirements:
    - logs/callback_events.csv (optional - graceful fallback if missing)
    - src/core/data_loader.py for major name lookup
"""

import json
import csv
import logging
from pathlib import Path
from datetime import datetime, timedelta
from collections import Counter
from typing import List, Dict, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_callback_events(days_back: int = 30) -> List[Dict[str, Any]]:
    """
    Load callback events from CSV log file
    
    Args:
        days_back: Number of days to look back for events
        
    Returns:
        List of event dictionaries
    """
    events = []
    log_file = Path("logs/callback_events.csv")
    
    if not log_file.exists():
        logger.warning(f"Log file {log_file} not found - using empty events")
        return events
    
    cutoff_date = datetime.now() - timedelta(days=days_back)
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    # Parse timestamp
                    event_time = datetime.fromisoformat(row.get('timestamp', ''))
                    if event_time >= cutoff_date:
                        events.append(row)
                except (ValueError, KeyError):
                    # Skip malformed rows
                    continue
                    
    except Exception as e:
        logger.error(f"Error reading log file: {e}")
    
    logger.info(f"Loaded {len(events)} events from last {days_back} days")
    return events


def extract_major_ids_from_events(events: List[Dict[str, Any]]) -> List[str]:
    """
    Extract major IDs from detail_opened events
    
    Args:
        events: List of event dictionaries
        
    Returns:
        List of major IDs sorted by frequency
    """
    major_ids = []
    
    for event in events:
        event_type = event.get('event_type', '')
        callback_data = event.get('callback_data', '')
        
        # Look for detail view events
        if event_type == 'detail_opened' and callback_data.startswith('DET_'):
            major_id = callback_data[4:]  # Remove 'DET_' prefix
            major_ids.append(major_id)
        
        # Also look for section navigation events
        elif event_type == 'section_viewed' and callback_data.startswith('SEC_'):
            parts = callback_data[4:].rsplit('_', 1)
            if len(parts) == 2:
                major_id = parts[0]
                major_ids.append(major_id)
    
    # Count frequencies and return top 10
    counter = Counter(major_ids)
    top_majors = [major_id for major_id, count in counter.most_common(10)]
    
    logger.info(f"Found {len(top_majors)} trending majors from {len(major_ids)} events")
    return top_majors


def get_major_names(major_ids: List[str]) -> List[Dict[str, str]]:
    """
    Get major names for trending major IDs
    
    Args:
        major_ids: List of major IDs
        
    Returns:
        List of major objects with id, name_en, name_kh
    """
    trending_majors = []
    
    try:
        # Import data loader
        import sys
        sys.path.append(str(Path(__file__).parent.parent))
        from src.core.data_loader import load_raw
        
        # Load all programmes
        programmes = load_raw()
        programme_dict = {prog.get('major_id'): prog for prog in programmes}
        
        # Build trending list with names
        for major_id in major_ids:
            programme = programme_dict.get(major_id)
            if programme:
                trending_majors.append({
                    'major_id': major_id,
                    'name_en': programme.get('major_name_en', major_id),
                    'name_kh': programme.get('major_name_kh', major_id)
                })
            else:
                logger.warning(f"Major ID {major_id} not found in programme data")
                
    except Exception as e:
        logger.error(f"Error loading programme data: {e}")
        # Fallback: create entries with just IDs
        for major_id in major_ids:
            trending_majors.append({
                'major_id': major_id,
                'name_en': major_id.replace('-', ' ').title(),
                'name_kh': major_id
            })
    
    logger.info(f"Built {len(trending_majors)} trending major entries")
    return trending_majors


def save_trending_data(trending_majors: List[Dict[str, str]], output_file: Path) -> None:
    """
    Save trending majors data to JSON file
    
    Args:
        trending_majors: List of major objects
        output_file: Path to output JSON file
    """
    try:
        # Ensure output directory exists
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Save to JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(trending_majors, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Saved {len(trending_majors)} trending majors to {output_file}")
        
    except Exception as e:
        logger.error(f"Error saving trending data: {e}")
        raise


def generate_fallback_trending() -> List[Dict[str, str]]:
    """
    Generate fallback trending data when no events are available
    
    Returns:
        List of popular major objects
    """
    # Common popular majors in Cambodia
    fallback_majors = [
        {'major_id': 'computer-science', 'name_en': 'Computer Science', 'name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ'},
        {'major_id': 'business-management', 'name_en': 'Business Management', 'name_kh': 'គ្រប់គ្រងអាជីវកម្ម'},
        {'major_id': 'accounting', 'name_en': 'Accounting', 'name_kh': 'គណនេយ្យ'},
        {'major_id': 'english-literature', 'name_en': 'English Literature', 'name_kh': 'អក្សរសាស្ត្រអង់គ្លេស'},
        {'major_id': 'civil-engineering', 'name_en': 'Civil Engineering', 'name_kh': 'វិស្វកម្មស៊ីវិល'}
    ]
    
    logger.info(f"Using fallback trending data with {len(fallback_majors)} majors")
    return fallback_majors


def main():
    """Main function to build trending majors data"""
    logger.info("Starting trending majors data build")
    
    try:
        # Load events from log file
        events = load_callback_events(days_back=30)
        
        if events:
            # Extract trending major IDs from events
            major_ids = extract_major_ids_from_events(events)
            
            if major_ids:
                # Get major names
                trending_majors = get_major_names(major_ids)
            else:
                logger.warning("No trending majors found in events - using fallback")
                trending_majors = generate_fallback_trending()
        else:
            logger.warning("No events found - using fallback trending data")
            trending_majors = generate_fallback_trending()
        
        # Save to output file
        output_file = Path("data/cache/trending.json")
        save_trending_data(trending_majors, output_file)
        
        logger.info("Trending majors data build completed successfully")
        
    except Exception as e:
        logger.error(f"Error building trending data: {e}")
        
        # Create empty trending file as fallback
        try:
            output_file = Path("data/cache/trending.json")
            output_file.parent.mkdir(parents=True, exist_ok=True)
            with open(output_file, 'w') as f:
                json.dump([], f)
            logger.info("Created empty trending.json as fallback")
        except Exception as fallback_error:
            logger.error(f"Failed to create fallback file: {fallback_error}")


if __name__ == "__main__":
    main()
