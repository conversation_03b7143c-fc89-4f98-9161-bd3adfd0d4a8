#!/usr/bin/env python3
"""
Synthetic User Generator
Generates synthetic user answer sets with MCDA scores for ML training
"""

import argparse
import csv
import json
import random
import sys
from pathlib import Path
from typing import Dict, List, Any

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.data_loader import load_raw
from src.core.feature_engineering import add_derived_features
from src.core.mcda import score
from src.core.feature_schema import extract_features, FEATURES


def generate_random_answers(seed: int = None) -> Dict[str, str]:
    """Generate a random set of user answers with realistic distributions"""
    if seed is not None:
        random.seed(seed)

    # Define answer options with realistic distributions
    answer_options = {
        'location_preference': ['pp', 'sr', 'btb', 'any'],
        'budget_range': ['low', 'mid', 'high', 'flex'],
        'field_of_interest': ['stem', 'business', 'health', 'arts', 'social', 'education'],
        'career_goal': ['tech', 'finance', 'health', 'gov', 'entre', 'unsure'],
        'academic_strength': ['math', 'language', 'social', 'arts', 'hands_on', 'all'],
        'learning_style': ['theory', 'practical', 'group', 'self', 'mixed'],
        'study_mode': ['full', 'part', 'evening', 'weekend', 'flex'],
        'language_pref': ['kh', 'en', 'both'],
        'scholarship_need': ['yes', 'partial', 'no'],
        'campus_life': ['very', 'some', 'little', 'none'],
        'extracurricular': ['sports', 'arts', 'volunteer', 'clubs', 'none'],
        'employment_priority': ['salary', 'stability', 'passion', 'growth', 'balance'],
        'mental_health_support': ['high', 'medium', 'low'],
        'international_exposure': ['yes', 'maybe', 'no'],
        'study_plan_assistance': ['yes', 'no'],
        'campus_map_need': ['yes', 'maybe', 'no']
    }

    answers = {}
    for question, options in answer_options.items():
        answers[question] = random.choice(options)

    return answers


def generate_synthetic_dataset(num_users: int = 10000, seed: int = 42) -> List[Dict[str, Any]]:
    """
    Generate synthetic dataset with user answers and MCDA scores

    Args:
        num_users: Number of synthetic users to generate
        seed: Random seed for reproducibility

    Returns:
        List[Dict]: Training examples with features and labels
    """
    random.seed(seed)

    print("Loading program data...")
    raw_data = load_raw()
    enhanced_data = add_derived_features(raw_data)
    print(f"Loaded {len(enhanced_data)} programs")

    print(f"Generating {num_users} synthetic users...")

    training_examples = []

    for i in range(num_users):
        if i % 1000 == 0:
            print(f"Generated {i}/{num_users} users...")

        # Generate random user answers
        user_answers = generate_random_answers(seed + i)

        # Score all programs with MCDA
        program_scores = []
        for prog in enhanced_data:
            mcda_score = score(user_answers, prog)
            prog_with_score = prog.copy()
            prog_with_score['mcda_score'] = mcda_score
            program_scores.append((prog_with_score, mcda_score))

        # Sort by MCDA score and label top-1 as best (label=1)
        program_scores.sort(key=lambda x: x[1], reverse=True)

        # Create training examples for top programs
        for j, (prog, mcda_score) in enumerate(program_scores[:10]):  # Top 10 for variety
            # Label: 1 for best program, 0 for others
            label = 1.0 if j == 0 else 0.0

            # Extract features
            features = extract_features(user_answers, prog)

            # Create training example
            example = {
                'answer_json': json.dumps(user_answers),
                'programme_id': prog.get('major_id', f'unknown_{j}'),
                'label': label
            }

            # Add feature values
            for feat_name, feat_value in zip(FEATURES, features):
                example[feat_name] = feat_value

            training_examples.append(example)

    print(f"Generated {len(training_examples)} training examples")
    return training_examples


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Generate synthetic user dataset')
    parser.add_argument('--num', type=int, default=10000, help='Number of synthetic users')
    parser.add_argument('--seed', type=int, default=42, help='Random seed for reproducibility')
    parser.add_argument('--output', default='build/synth_train.csv', help='Output CSV file')

    args = parser.parse_args()

    # Generate dataset
    dataset = generate_synthetic_dataset(args.num, args.seed)

    # Save to CSV
    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    with open(output_path, 'w', newline='', encoding='utf-8') as f:
        if dataset:
            writer = csv.DictWriter(f, fieldnames=dataset[0].keys())
            writer.writeheader()
            writer.writerows(dataset)

    print(f"Saved {len(dataset)} examples to {output_path}")
    print(f"File size: {output_path.stat().st_size / 1024 / 1024:.1f} MB")


if __name__ == '__main__':
    main()
