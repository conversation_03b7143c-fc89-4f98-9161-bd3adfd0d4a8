#!/usr/bin/env python3
"""
Raw Data Validator
Walks data/raw directory and validates JSON files, printing errors
"""

import json
import sys
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


class RawDataValidator:
    """Validator for raw JSON university data files"""
    
    def __init__(self, data_dir: Path):
        self.data_dir = data_dir
        self.errors = []
        self.warnings = []
        self.files_processed = 0
        self.programs_found = 0
    
    def validate_all(self) -> bool:
        """Validate all JSON files in data directory"""
        if not self.data_dir.exists():
            logger.error(f"Data directory {self.data_dir} does not exist")
            return False
        
        json_files = list(self.data_dir.rglob("*.json"))
        logger.info(f"Found {len(json_files)} JSON files to validate")
        
        for json_file in json_files:
            self._validate_file(json_file)
            self.files_processed += 1
        
        self._print_summary()
        return len(self.errors) == 0
    
    def _validate_file(self, json_file: Path) -> None:
        """Validate a single JSON file"""
        try:
            # Test JSON parsing
            with open(json_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            # Validate structure
            self._validate_structure(json_file, file_data)
            
        except json.JSONDecodeError as e:
            self.errors.append(f"JSON Parse Error in {json_file}: {e}")
        except UnicodeDecodeError as e:
            self.errors.append(f"Encoding Error in {json_file}: {e}")
        except Exception as e:
            self.errors.append(f"Unexpected Error in {json_file}: {e}")
    
    def _validate_structure(self, json_file: Path, file_data: Dict[str, Any]) -> None:
        """Validate the structure of a JSON file"""
        # Check top-level structure
        if not isinstance(file_data, dict):
            self.errors.append(f"Invalid root structure in {json_file}: expected dict, got {type(file_data)}")
            return
        
        # Check for required top-level keys
        if 'university' not in file_data:
            self.errors.append(f"Missing 'university' key in {json_file}")
            return
        
        if 'majors' not in file_data:
            self.errors.append(f"Missing 'majors' key in {json_file}")
            return
        
        # Validate university section
        self._validate_university(json_file, file_data['university'])
        
        # Validate majors section
        self._validate_majors(json_file, file_data['majors'])
    
    def _validate_university(self, json_file: Path, university: Dict[str, Any]) -> None:
        """Validate university section"""
        if not isinstance(university, dict):
            self.errors.append(f"Invalid university structure in {json_file}: expected dict")
            return
        
        # Required university fields
        required_fields = ['id', 'name_kh', 'name_en', 'location']
        for field in required_fields:
            if field not in university:
                self.errors.append(f"Missing university.{field} in {json_file}")
            elif not university[field]:
                self.warnings.append(f"Empty university.{field} in {json_file}")
        
        # Validate location
        if 'location' in university and isinstance(university['location'], dict):
            if 'city' not in university['location']:
                self.errors.append(f"Missing university.location.city in {json_file}")
            elif not university['location']['city']:
                self.warnings.append(f"Empty university.location.city in {json_file}")
    
    def _validate_majors(self, json_file: Path, majors: List[Dict[str, Any]]) -> None:
        """Validate majors section"""
        if not isinstance(majors, list):
            self.errors.append(f"Invalid majors structure in {json_file}: expected list")
            return
        
        if not majors:
            self.warnings.append(f"Empty majors list in {json_file}")
            return
        
        for i, major in enumerate(majors):
            self._validate_major(json_file, major, i)
            self.programs_found += 1
    
    def _validate_major(self, json_file: Path, major: Dict[str, Any], index: int) -> None:
        """Validate a single major"""
        if not isinstance(major, dict):
            self.errors.append(f"Invalid major[{index}] structure in {json_file}: expected dict")
            return
        
        # Check major_info section
        if 'major_info' not in major:
            self.errors.append(f"Missing major_info in major[{index}] of {json_file}")
            return
        
        major_info = major['major_info']
        if not isinstance(major_info, dict):
            self.errors.append(f"Invalid major_info structure in major[{index}] of {json_file}")
            return
        
        # Required major_info fields
        required_fields = ['id', 'name_kh', 'name_en']
        for field in required_fields:
            if field not in major_info:
                self.errors.append(f"Missing major_info.{field} in major[{index}] of {json_file}")
            elif not major_info[field]:
                self.warnings.append(f"Empty major_info.{field} in major[{index}] of {json_file}")
        
        # Validate optional sections
        self._validate_optional_sections(json_file, major, index)
    
    def _validate_optional_sections(self, json_file: Path, major: Dict[str, Any], index: int) -> None:
        """Validate optional sections in major"""
        # Check practical_information
        if 'practical_information' in major:
            practical = major['practical_information']
            if isinstance(practical, dict):
                # Check tuition fees
                if 'tuition_fees_usd' in practical:
                    usd_fee = practical['tuition_fees_usd']
                    if usd_fee and not self._is_valid_number(usd_fee):
                        self.warnings.append(f"Invalid tuition_fees_usd '{usd_fee}' in major[{index}] of {json_file}")
                
                if 'tuition_fees_khr' in practical:
                    khr_fee = practical['tuition_fees_khr']
                    if khr_fee and not self._is_valid_number(khr_fee):
                        self.warnings.append(f"Invalid tuition_fees_khr '{khr_fee}' in major[{index}] of {json_file}")
        
        # Check career_prospects
        if 'career_prospects' in major:
            career = major['career_prospects']
            if isinstance(career, dict) and 'employment_statistics' in career:
                emp_stats = career['employment_statistics']
                if isinstance(emp_stats, dict):
                    # Check employment rate
                    if 'employment_rate' in emp_stats:
                        emp_rate = emp_stats['employment_rate']
                        if emp_rate and not self._is_valid_percentage(emp_rate):
                            self.warnings.append(f"Invalid employment_rate '{emp_rate}' in major[{index}] of {json_file}")
        
        # Check program_details
        if 'program_details' in major:
            program = major['program_details']
            if isinstance(program, dict):
                if 'total_credits' in program:
                    credits = program['total_credits']
                    if credits and not self._is_valid_integer(credits):
                        self.warnings.append(f"Invalid total_credits '{credits}' in major[{index}] of {json_file}")
        
        # Check academic_requirements
        if 'academic_requirements' in major:
            academic = major['academic_requirements']
            if isinstance(academic, dict):
                if 'minimum_gpa' in academic:
                    gpa = academic['minimum_gpa']
                    if gpa and not self._is_valid_gpa(gpa):
                        self.warnings.append(f"Invalid minimum_gpa '{gpa}' in major[{index}] of {json_file}")
    
    def _is_valid_number(self, value: Any) -> bool:
        """Check if value is a valid number"""
        try:
            if isinstance(value, str):
                # Remove common formatting
                cleaned = value.replace(',', '').replace('$', '').strip()
                float(cleaned)
            else:
                float(value)
            return True
        except (ValueError, TypeError):
            return False
    
    def _is_valid_percentage(self, value: Any) -> bool:
        """Check if value is a valid percentage"""
        try:
            if isinstance(value, str):
                cleaned = value.replace('%', '').strip()
                num = float(cleaned)
            else:
                num = float(value)
            return 0 <= num <= 100
        except (ValueError, TypeError):
            return False
    
    def _is_valid_integer(self, value: Any) -> bool:
        """Check if value is a valid integer"""
        try:
            int(value)
            return True
        except (ValueError, TypeError):
            return False
    
    def _is_valid_gpa(self, value: Any) -> bool:
        """Check if value is a valid GPA"""
        try:
            gpa = float(value)
            return 0.0 <= gpa <= 4.0
        except (ValueError, TypeError):
            return False
    
    def _print_summary(self) -> None:
        """Print validation summary"""
        print("\n" + "="*60)
        print("RAW DATA VALIDATION SUMMARY")
        print("="*60)
        print(f"Files processed: {self.files_processed}")
        print(f"Programs found: {self.programs_found}")
        print(f"Errors: {len(self.errors)}")
        print(f"Warnings: {len(self.warnings)}")
        
        if self.errors:
            print("\n🚨 ERRORS:")
            for error in self.errors:
                print(f"  ❌ {error}")
        
        if self.warnings:
            print("\n⚠️  WARNINGS:")
            for warning in self.warnings:
                print(f"  ⚠️  {warning}")
        
        if not self.errors and not self.warnings:
            print("\n✅ All files are valid!")
        elif not self.errors:
            print(f"\n✅ No blocking errors found (but {len(self.warnings)} warnings)")
        else:
            print(f"\n❌ Found {len(self.errors)} blocking errors")
        
        print("="*60)


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate raw JSON university data')
    parser.add_argument('--data-dir', default='data/raw', help='Data directory to validate')
    
    args = parser.parse_args()
    
    data_dir = Path(args.data_dir)
    validator = RawDataValidator(data_dir)
    
    success = validator.validate_all()
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
