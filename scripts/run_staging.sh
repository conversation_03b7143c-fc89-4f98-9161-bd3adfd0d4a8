#!/usr/bin/env bash
# EduGuideBot Staging Runner
# One-liner for human testers to run the bot in staging mode

set -e

echo "🚀 Starting EduGuideBot in staging mode..."

# Set default BOT_TOKEN if not provided
export BOT_TOKEN=${BOT_TOKEN:-"dry"}

echo "📋 Configuration:"
echo "  BOT_TOKEN: ${BOT_TOKEN}"
echo "  ML_VERSION: ${ML_VERSION:-1}"

# Run docker compose with staging profile
echo "🐳 Building and starting containers..."
docker compose --profile staging up --build

echo "✅ EduGuideBot staging completed"
