#!/usr/bin/env python3
"""
ML Training Script
Trains RandomForest model on synthetic dataset
"""

import argparse
import json
import sys
from pathlib import Path
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.ml_reranker import MLReranker


def train(data_path: str, model_path: str, params: dict = None) -> None:
    """
    Train ML model on synthetic dataset

    Args:
        data_path: Path to training CSV file
        model_path: Path to save trained model
        params: Optional hyperparameters dict
    """
    print(f"Loading training data from {data_path}...")
    
    # Load data
    df = pd.read_csv(data_path)
    print(f"Loaded {len(df)} training examples")
    
    # Check data quality
    print(f"Features: {df.columns.tolist()}")
    print(f"Label distribution: {df['label'].value_counts().to_dict()}")
    
    # Split into train/validation
    train_df, val_df = train_test_split(df, test_size=0.2, random_state=42, stratify=df['label'])
    print(f"Train: {len(train_df)}, Validation: {len(val_df)}")
    
    # Train model
    print("Training RandomForest model...")
    if params:
        print(f"Using custom parameters: {params}")

    reranker = MLReranker()

    # Save full dataset temporarily for training
    temp_train_path = Path(data_path).parent / "temp_train.csv"
    train_df.to_csv(temp_train_path, index=False)

    try:
        reranker.train(str(temp_train_path), model_path, params)
        
        # Evaluate on validation set
        print("Evaluating model...")
        
        # Load trained model
        trained_reranker = MLReranker(model_path)
        
        # Extract validation features and labels
        from src.core.feature_schema import FEATURES
        X_val = val_df[FEATURES].values
        y_val = val_df['label'].values
        
        # Scale validation features
        X_val_scaled = trained_reranker.scaler.transform(X_val)
        
        # Predict
        y_pred = trained_reranker.model.predict(X_val_scaled)
        
        # Calculate metrics
        r2 = r2_score(y_val, y_pred)
        mse = mean_squared_error(y_val, y_pred)
        rmse = np.sqrt(mse)
        
        print(f"\nValidation Results:")
        print(f"R² Score: {r2:.4f}")
        print(f"RMSE: {rmse:.4f}")
        print(f"MSE: {mse:.4f}")
        
        # Feature importance
        if hasattr(trained_reranker.model, 'feature_importances_'):
            print(f"\nFeature Importance:")
            importances = []
            for feat, importance in zip(FEATURES, trained_reranker.model.feature_importances_):
                print(f"  {feat}: {importance:.4f}")
                importances.append({'feature': feat, 'importance': importance})

            # Save feature importance to CSV
            importance_path = Path(model_path).parent / "feature_importance.csv"
            importance_df = pd.DataFrame(importances)
            importance_df.to_csv(importance_path, index=False)
            print(f"Feature importance saved to {importance_path}")

        print(f"\nModel successfully trained and saved to {model_path}")
        
    finally:
        # Clean up temporary file
        if temp_train_path.exists():
            temp_train_path.unlink()


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Train ML re-ranker model')
    parser.add_argument('--data', required=True, help='Path to training CSV file')
    parser.add_argument('--model', required=True, help='Path to save trained model')
    parser.add_argument('--params-json', help='Path to hyperparameters JSON file')

    args = parser.parse_args()

    # Load parameters if provided
    params = None
    if args.params_json:
        with open(args.params_json, 'r') as f:
            hp_results = json.load(f)
            params = hp_results.get('best_params', {}).copy()

            # Add model type if available
            if 'best_model' in hp_results:
                params['model_type'] = hp_results['best_model']

            print(f"Loaded best model: {hp_results.get('best_model', 'RandomForest')}")
            print(f"Loaded best parameters: {params}")
    
    # Validate inputs
    data_path = Path(args.data)
    if not data_path.exists():
        print(f"Error: Training data file not found: {data_path}")
        sys.exit(1)
    
    model_path = Path(args.model)
    model_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Train model
    train(str(data_path), str(model_path), params)


if __name__ == '__main__':
    main()
