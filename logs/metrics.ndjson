{"timestamp": "2025-06-16T15:43:49.222281+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.222545+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.222597+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.224916+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.224991+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.225067+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.228804+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.228958+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.229009+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.232306+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.232379+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.232420+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.235646+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.235820+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.235867+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.237197+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.237254+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.237293+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.252012+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.252164+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.252212+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.299428+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.299554+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.299603+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.341336+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.341476+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.341523+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:43:49.405103+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.0015}}
{"timestamp": "2025-06-16T15:43:49.405290+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.405548+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.005}}
{"timestamp": "2025-06-16T15:43:49.406652+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.15}}
{"timestamp": "2025-06-16T15:43:49.406722+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0}}
{"timestamp": "2025-06-16T15:43:49.406774+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.5}}
{"timestamp": "2025-06-16T15:46:48.901281+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:47:07.004859+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-16T15:47:07.007132+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-16T15:47:07.007190+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-16T15:47:20.177613+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-16T15:47:20.177915+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-16T15:47:20.178250+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-16T15:47:35.947010+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.3511642857142857}}
{"timestamp": "2025-06-16T15:47:35.948071+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4016}}
{"timestamp": "2025-06-16T15:47:35.948142+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.23348095238095237}}
{"timestamp": "2025-06-16T15:47:39.631864+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:47:46.017669+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:47:55.318211+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:48:03.186795+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:48:36.475851+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.3511642857142857}}
{"timestamp": "2025-06-16T15:48:36.476023+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4016}}
{"timestamp": "2025-06-16T15:48:36.476083+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.23348095238095237}}
{"timestamp": "2025-06-16T15:48:50.912440+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T15:49:11.280675+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-16T16:02:29.642071+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-16T16:02:29.759981+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-16T16:02:29.760155+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-17T06:44:17.145462+00:00", "event": "recommendation_sent", "data": {"count": 2}}
{"timestamp": "2025-06-17T06:44:17.147096+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 2.4958750000223517, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T06:46:21.365893+00:00", "event": "recommendation_sent", "data": {"count": 2}}
{"timestamp": "2025-06-17T06:46:21.367175+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 2.0688749791588634, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T06:53:44.209928+00:00", "event": "recommendation_sent", "data": {"count": 2}}
{"timestamp": "2025-06-17T06:53:44.211111+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 2.4987499928101897, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T07:03:15.480764+00:00", "event": "recommendation_sent", "data": {"count": 2}}
{"timestamp": "2025-06-17T07:03:15.481964+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 2.3216249828692526, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T07:04:32.207603+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 3.3384590060450137, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T07:07:15.737878+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-17T07:07:15.738666+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-17T07:07:15.738743+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-17T07:09:01.352341+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-17T07:09:01.353124+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-17T07:09:01.353195+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-17T07:49:20.723861+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.7668750185985118, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T07:49:57.736686+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.6862920126877725, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T08:01:14.429100+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.7051249849610031, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T08:26:09.299116+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.4628970864661654}}
{"timestamp": "2025-06-17T08:26:09.299827+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.5169473684210526}}
{"timestamp": "2025-06-17T08:26:09.299908+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-17T08:26:22.270654+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-17T08:26:22.271825+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-17T08:26:22.271912+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-17T08:26:36.211760+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-17T08:26:36.212719+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-17T08:26:36.212805+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-17T08:26:50.257937+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-17T08:26:50.258939+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-17T08:26:50.259025+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-17T08:27:02.452956+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11377392857142858}}
{"timestamp": "2025-06-17T08:27:02.453192+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-17T08:27:02.453512+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-17T08:27:02.456269+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-17T08:33:25.030106+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-17T08:33:32.394071+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.22143947368421046}}
{"timestamp": "2025-06-17T08:33:32.394310+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.2764421052631579}}
{"timestamp": "2025-06-17T08:33:32.394674+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.09309999999999999}}
{"timestamp": "2025-06-17T08:33:34.139869+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.27449210526315787}}
{"timestamp": "2025-06-17T08:33:34.140085+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.35223157894736845}}
{"timestamp": "2025-06-17T08:33:34.140330+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.09309999999999999}}
{"timestamp": "2025-06-17T08:33:57.406466+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.2611239473684211}}
{"timestamp": "2025-06-17T08:33:57.406891+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.3530842105263158}}
{"timestamp": "2025-06-17T08:33:57.406983+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.046549999999999994}}
{"timestamp": "2025-06-17T08:34:12.869471+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-17T08:35:05.809117+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.45405498120300747}}
{"timestamp": "2025-06-17T08:35:05.809734+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.5043157894736842}}
{"timestamp": "2025-06-17T08:35:05.809811+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-17T08:35:07.037998+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-17T08:35:07.537981+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 13573.969666002085, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T08:35:44.940569+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.45405498120300747}}
{"timestamp": "2025-06-17T08:35:44.941837+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.5043157894736842}}
{"timestamp": "2025-06-17T08:35:44.941914+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-17T09:59:12.785918+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.7703329902142286, "function": "show_recommendations"}}
{"timestamp": "2025-06-17T09:59:55.873686+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.29236065789473675}}
{"timestamp": "2025-06-17T09:59:55.874313+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.3257473684210526}}
{"timestamp": "2025-06-17T09:59:55.874394+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.21445833333333325}}
{"timestamp": "2025-06-17T10:00:08.065602+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-17T10:00:08.066628+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-17T10:00:08.066692+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-17T10:00:27.947629+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-17T10:00:27.949321+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-17T10:00:27.949386+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-17T10:00:56.805584+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-17T10:00:56.816677+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-17T10:00:56.818707+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-17T10:01:22.429376+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11377392857142858}}
{"timestamp": "2025-06-17T10:01:22.470481+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-17T10:01:22.470585+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-17T10:01:22.627036+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-19T13:59:24.224873+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T13:59:41.431790+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.263267591918805}}
{"timestamp": "2025-06-19T13:59:41.432027+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.22471849865951743}}
{"timestamp": "2025-06-19T13:59:41.432316+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T13:59:41.433961+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T13:59:41.434038+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 17209.10874999754, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T13:59:41.435174+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 5}}
{"timestamp": "2025-06-19T13:59:53.835720+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-19T13:59:53.836052+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-19T13:59:53.836350+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T13:59:53.838469+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T13:59:53.838546+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 12403.400832998159, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:00:06.059999+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.7291838214285713}}
{"timestamp": "2025-06-19T14:00:06.061561+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.9361200000000001}}
{"timestamp": "2025-06-19T14:00:06.061623+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.24633273809523817}}
{"timestamp": "2025-06-19T14:00:18.416248+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.7565139285714284}}
{"timestamp": "2025-06-19T14:00:18.416721+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.9364000000000001}}
{"timestamp": "2025-06-19T14:00:18.416799+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T14:00:18.420183+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-19T14:00:18.432973+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:00:18.433668+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.853459001518786, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:01:06.830159+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.3265178405572752}}
{"timestamp": "2025-06-19T14:01:06.831926+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.30983157894736846}}
{"timestamp": "2025-06-19T14:01:06.831987+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.36545245098039086}}
{"timestamp": "2025-06-19T14:01:19.313636+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-19T14:01:19.314051+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-19T14:01:19.314149+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:01:33.630525+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:01:33.631555+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:01:33.631639+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:01:47.822547+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:01:47.823547+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:01:47.823612+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:01:47.873309+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:02:01.159842+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.1355046428571428}}
{"timestamp": "2025-06-19T14:02:01.160191+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0422}}
{"timestamp": "2025-06-19T14:02:01.160472+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:02:01.164600+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:02:01.164718+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 13291.387540997675, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:02:01.165939+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 5}}
{"timestamp": "2025-06-19T14:02:13.214825+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11870464285714281}}
{"timestamp": "2025-06-19T14:02:13.215130+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:02:13.215430+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:02:13.218574+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:02:13.218666+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 12052.748999998585, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:02:25.311221+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.08654182142857145}}
{"timestamp": "2025-06-19T14:02:25.311541+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.01806}}
{"timestamp": "2025-06-19T14:02:25.311846+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.24633273809523817}}
{"timestamp": "2025-06-19T14:02:37.509027+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11377392857142858}}
{"timestamp": "2025-06-19T14:02:37.509303+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:02:37.509602+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T14:02:37.512371+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-19T14:02:37.524787+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:02:37.525718+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 1.0337909989175387, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:04:17.051899+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.37616599729517475, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:04:30.728506+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.4628970864661654}}
{"timestamp": "2025-06-19T14:04:30.728785+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.5169473684210526}}
{"timestamp": "2025-06-19T14:04:30.729086+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T14:04:42.535919+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-19T14:04:42.536213+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-19T14:04:42.536519+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:04:56.237465+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:04:56.237689+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:04:56.238018+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:05:09.976505+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:05:09.977481+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:05:09.977561+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:05:10.024855+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:05:21.908831+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.1355046428571428}}
{"timestamp": "2025-06-19T14:05:21.909130+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0422}}
{"timestamp": "2025-06-19T14:05:21.909413+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:05:21.913724+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:05:21.913841+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11888.966708000225, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:05:21.915211+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 5}}
{"timestamp": "2025-06-19T14:05:33.700520+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11870464285714281}}
{"timestamp": "2025-06-19T14:05:33.700722+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:05:33.700794+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:05:33.702751+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:05:33.702837+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11787.664082999981, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:05:45.608571+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.08654182142857145}}
{"timestamp": "2025-06-19T14:05:45.609556+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.01806}}
{"timestamp": "2025-06-19T14:05:45.609642+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.24633273809523817}}
{"timestamp": "2025-06-19T14:05:57.368003+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11377392857142858}}
{"timestamp": "2025-06-19T14:05:57.368202+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:05:57.368562+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T14:05:57.371004+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-19T14:05:57.382879+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:05:57.383711+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.9184160007862374, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:06:46.572354+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:06:46.573070+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.7179999993240926, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:06:48.813227+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.23423314028826098}}
{"timestamp": "2025-06-19T14:06:48.813493+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.26063157894736844}}
{"timestamp": "2025-06-19T14:06:48.813808+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.17263678341701033}}
{"timestamp": "2025-06-19T14:07:00.507146+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-19T14:07:00.507352+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-19T14:07:00.507434+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:07:14.254708+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:07:14.254923+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:07:14.255227+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:07:28.119482+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:07:28.119790+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:07:28.119857+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:07:28.169254+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:07:39.927801+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.1355046428571428}}
{"timestamp": "2025-06-19T14:07:39.927937+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0422}}
{"timestamp": "2025-06-19T14:07:39.928010+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:07:39.929485+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:07:39.929605+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11760.25891700192, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:07:39.930804+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 5}}
{"timestamp": "2025-06-19T14:07:51.712469+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11870464285714281}}
{"timestamp": "2025-06-19T14:07:51.712660+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:07:51.712985+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:07:51.714960+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:07:51.715039+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11784.203875002277, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:08:03.565754+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.08654182142857145}}
{"timestamp": "2025-06-19T14:08:03.566824+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.01806}}
{"timestamp": "2025-06-19T14:08:03.566908+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.24633273809523817}}
{"timestamp": "2025-06-19T14:08:15.344507+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11377392857142858}}
{"timestamp": "2025-06-19T14:08:15.345442+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:08:15.345515+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T14:08:15.348428+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-19T14:08:15.360057+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:08:15.360902+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.9330829998361878, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:08:47.156123+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:08:47.156759+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.6457909985329024, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:09:00.380174+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.45405498120300747}}
{"timestamp": "2025-06-19T14:09:00.380411+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.5043157894736842}}
{"timestamp": "2025-06-19T14:09:00.380699+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T14:09:12.109218+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-19T14:09:12.109421+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-19T14:09:12.109763+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:09:25.912077+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:09:25.912349+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:09:25.912622+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:09:39.655909+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:09:39.656769+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:09:39.656828+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:09:39.703555+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:09:51.521497+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.1355046428571428}}
{"timestamp": "2025-06-19T14:09:51.521700+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0422}}
{"timestamp": "2025-06-19T14:09:51.521757+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:09:51.523075+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:09:51.523146+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11819.54395899811, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:09:51.524301+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 5}}
{"timestamp": "2025-06-19T14:10:03.308611+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11870464285714281}}
{"timestamp": "2025-06-19T14:10:03.309529+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:10:03.309601+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:10:03.311614+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:10:03.311681+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11787.377667002147, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:10:15.074943+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.08654182142857145}}
{"timestamp": "2025-06-19T14:10:15.075887+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.01806}}
{"timestamp": "2025-06-19T14:10:15.075964+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.24633273809523817}}
{"timestamp": "2025-06-19T14:10:26.925074+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11377392857142858}}
{"timestamp": "2025-06-19T14:10:26.925360+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:10:26.925429+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T14:10:26.929006+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-19T14:10:26.947487+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:10:26.948397+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 1.0068749979836866, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:54:11.783082+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:54:25.178002+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.263267591918805}}
{"timestamp": "2025-06-19T14:54:25.178215+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.22471849865951743}}
{"timestamp": "2025-06-19T14:54:25.178521+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:54:25.180731+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 13397.562707999896, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:54:26.430999+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.2911261654135338}}
{"timestamp": "2025-06-19T14:54:26.431208+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.3540210526315789}}
{"timestamp": "2025-06-19T14:54:26.431290+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.14437142857142846}}
{"timestamp": "2025-06-19T14:54:38.376909+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-19T14:54:38.378378+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-19T14:54:38.378440+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:54:52.340794+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:54:52.341082+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:54:52.341147+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:55:06.039193+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:55:06.039408+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:55:06.039763+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:55:06.114195+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:55:17.805572+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.1355046428571428}}
{"timestamp": "2025-06-19T14:55:17.805887+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0422}}
{"timestamp": "2025-06-19T14:55:17.806216+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:55:17.811402+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11697.101500001736, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:55:17.812849+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 5}}
{"timestamp": "2025-06-19T14:55:29.575247+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11870464285714281}}
{"timestamp": "2025-06-19T14:55:29.575404+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:55:29.575465+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:55:29.577230+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11764.354375001858, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:55:29.581955+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:55:29.582922+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 1.0742090016719885, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:56:47.050637+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:57:01.212351+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.263267591918805}}
{"timestamp": "2025-06-19T14:57:01.212712+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.22471849865951743}}
{"timestamp": "2025-06-19T14:57:01.212987+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:57:01.217299+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:57:01.217408+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 14166.676666998683, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:57:02.793354+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.2346151879699248}}
{"timestamp": "2025-06-19T14:57:02.793941+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.2624421052631579}}
{"timestamp": "2025-06-19T14:57:02.794154+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.16968571428571427}}
{"timestamp": "2025-06-19T14:57:16.379454+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-19T14:57:16.380121+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-19T14:57:16.380446+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:57:31.471474+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:57:31.471965+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:57:31.472025+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:57:46.466160+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:57:46.466920+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:57:46.466997+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:57:46.574626+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:57:58.800038+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.1355046428571428}}
{"timestamp": "2025-06-19T14:57:58.801023+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0422}}
{"timestamp": "2025-06-19T14:57:58.801119+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:57:58.802433+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:57:58.802506+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 12227.795292001247, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:57:58.803706+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 5}}
{"timestamp": "2025-06-19T14:58:11.229739+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11870464285714281}}
{"timestamp": "2025-06-19T14:58:11.229986+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:58:11.230350+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:58:11.233012+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:58:11.233190+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 12429.440167001303, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:58:24.025305+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.08654182142857145}}
{"timestamp": "2025-06-19T14:58:24.025650+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.01806}}
{"timestamp": "2025-06-19T14:58:24.025966+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.24633273809523817}}
{"timestamp": "2025-06-19T14:58:35.846445+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11377392857142858}}
{"timestamp": "2025-06-19T14:58:35.846663+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T14:58:35.847018+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T14:58:35.849549+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-19T14:58:35.857050+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T14:58:35.857829+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 0.8752089997869916, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:59:02.687720+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T14:59:16.381451+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.263267591918805}}
{"timestamp": "2025-06-19T14:59:16.382499+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.22471849865951743}}
{"timestamp": "2025-06-19T14:59:16.382588+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:59:16.387932+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T14:59:16.388144+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 13700.340167000832, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T14:59:17.100704+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.24528577186720835}}
{"timestamp": "2025-06-19T14:59:17.100964+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.27642105263157896}}
{"timestamp": "2025-06-19T14:59:17.101264+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.17263678341701033}}
{"timestamp": "2025-06-19T14:59:28.976134+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.40593306390977435}}
{"timestamp": "2025-06-19T14:59:28.976511+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.4285263157894737}}
{"timestamp": "2025-06-19T14:59:28.976575+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T14:59:42.744646+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:59:42.745554+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:59:42.745616+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:59:56.566005+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.09813835714285714}}
{"timestamp": "2025-06-19T14:59:56.566892+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.018039999999999997}}
{"timestamp": "2025-06-19T14:59:56.566961+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.28503452380952377}}
{"timestamp": "2025-06-19T14:59:56.649348+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 15}}
{"timestamp": "2025-06-19T15:00:08.479999+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.1355046428571428}}
{"timestamp": "2025-06-19T15:00:08.480196+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0422}}
{"timestamp": "2025-06-19T15:00:08.480504+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T15:00:08.482047+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T15:00:08.482144+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 11832.738832999894, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T15:00:08.483389+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 5}}
{"timestamp": "2025-06-19T15:00:20.657116+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11870464285714281}}
{"timestamp": "2025-06-19T15:00:20.657479+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T15:00:20.657774+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.3532154761904761}}
{"timestamp": "2025-06-19T15:00:20.660945+00:00", "event": "recommendation_sent", "data": {"count": 5}}
{"timestamp": "2025-06-19T15:00:20.661032+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 12177.610332997574, "function": "show_recommendations"}}
{"timestamp": "2025-06-19T15:00:32.418089+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.08654182142857145}}
{"timestamp": "2025-06-19T15:00:32.418296+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.01806}}
{"timestamp": "2025-06-19T15:00:32.418593+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.24633273809523817}}
{"timestamp": "2025-06-19T15:00:44.297915+00:00", "event": "histogram", "data": {"name": "hybrid_score", "value": 0.11377392857142858}}
{"timestamp": "2025-06-19T15:00:44.298210+00:00", "event": "histogram", "data": {"name": "mcda_component", "value": 0.0182}}
{"timestamp": "2025-06-19T15:00:44.298515+00:00", "event": "histogram", "data": {"name": "ml_component", "value": 0.336779761904762}}
{"timestamp": "2025-06-19T15:00:44.302617+00:00", "event": "call_count", "data": {"event_name": "start", "function": "start_assessment"}}
{"timestamp": "2025-06-19T15:00:44.308485+00:00", "event": "recommendation_request", "data": {"user_id": 12345, "answers_count": 0}}
{"timestamp": "2025-06-19T15:00:44.309548+00:00", "event": "latency", "data": {"event_name": "recommendation_latency", "latency_ms": 1.1972920001426246, "function": "show_recommendations"}}
