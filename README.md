# EduGuideBot v2.0

A Telegram bot that provides personalized university recommendations for Cambodian students using a hybrid scoring system combining Multi-Criteria Decision Analysis (MCDA) with Machine Learning.

## 🎯 Features

- **16-question assessment** to understand student preferences
- **Hybrid scoring system** (70% MCDA + 30% ML) for accurate recommendations
- **Interactive recommendation cards** with inline buttons for detailed explanations
- **Advanced filtering system** (low-cost only, Phnom Penh only)
- **Bilingual support** (Khmer and English) with seamless language switching
- **Comprehensive database** of 47 universities and 500+ study programs in Cambodia
- **Detailed program information** including tuition fees, employment rates, and scholarship availability

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Telegram <PERSON><PERSON> (from @BotFather)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd EduGuideBot_Project
```

2. Install dependencies:
```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt  # For development
```

3. Set up environment variables:
```bash
export BOT_TOKEN="your_telegram_bot_token"
export ML_VERSION="3"  # Optional: ML model version (1, 2, or 3)
```

4. Run the bot:
```bash
python -m src.bot.app
```

For dry-run mode (testing without network calls):
```bash
python -m src.bot.app --dry-run
```

## 🏗️ Architecture v2.0

```
┌─────────────────────────────────────────────────────────────┐
│                    EduGuideBot v2.0                         │
├─────────────────────────────────────────────────────────────┤
│  Bot Layer (src/bot/)                                       │
│  ├── commands.py      # Command handlers (/start, /details) │
│  ├── handlers.py      # Conversation flow & callbacks       │
│  ├── utils.py         # Shared utilities & formatting       │
│  └── i18n.py          # Bilingual support (kh/en)          │
├─────────────────────────────────────────────────────────────┤
│  Core Layer (src/core/)                                     │
│  ├── hybrid_recommender.py  # 70% MCDA + 30% ML blend      │
│  ├── mcda.py               # Vectorized MCDA scoring        │
│  ├── ml_reranker.py        # ML model (RandomForest)        │
│  └── feature_engineering.py # Data preprocessing            │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (data/)                                         │
│  ├── raw/              # University & program data (JSON)   │
│  ├── processed/        # Enhanced data with derived features │
│  └── models/           # Trained ML models (v1, v2, v3)     │
├─────────────────────────────────────────────────────────────┤
│  Infrastructure (src/infra/)                                │
│  ├── metrics.py        # Performance monitoring             │
│  └── logging.py        # Structured logging                 │
└─────────────────────────────────────────────────────────────┘
```

### Hybrid Scoring Pipeline

The bot uses a sophisticated hybrid scoring system:

1. **MCDA Component (70%)**: Rule-based scoring using Multi-Criteria Decision Analysis
   - Location preference matching
   - Budget range compatibility
   - Field of interest alignment
   - Career goal matching
   - Scholarship and employment bonuses

2. **ML Component (30%)**: Machine Learning predictions using RandomForest
   - Learns from successful student outcomes
   - Considers program popularity and success rates
   - Adapts to changing market conditions

3. **Hybrid Score**: `Final Score = 0.7 × MCDA + 0.3 × ML`

## 🧪 Testing

Run the complete test suite:
```bash
pytest
```

Run specific test categories:
```bash
pytest tests/mcda/          # MCDA scoring tests
pytest tests/recommender/   # Hybrid recommender tests
pytest tests/commands/      # Command layer tests
pytest tests/app/           # Application integration tests
```

Generate coverage report:
```bash
pytest --cov=src --cov-report=html
```

Target: ≥95% test coverage

## 🛠️ Development

### Code Quality Tools

```bash
# Format code
black .

# Lint code
flake8

# Run all quality checks
make lint  # If Makefile exists, or run individually
```

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `BOT_TOKEN` | Telegram bot token | Required |
| `ML_VERSION` | ML model version (1, 2, 3) | `3` |
| `LOG_LEVEL` | Logging level | `INFO` |

### Adding New Features

1. Create feature branch from `main`
2. Implement changes with comprehensive tests
3. Ensure all tests pass: `pytest`
4. Check code quality: `black . && flake8`
5. Update documentation if needed
6. Submit pull request

### ML Model Versioning

The bot supports multiple ML model versions:
- **v1**: Basic RandomForest model
- **v2**: Enhanced feature set
- **v3**: Latest model with improved accuracy

Models are automatically selected with fallback chain: v3 → v2 → v1

## 📊 Performance Metrics

The bot tracks key performance indicators:
- Recommendation accuracy
- Response time
- User engagement
- ML model performance
- Error rates

Metrics are logged for monitoring and optimization.

## 🌐 Internationalization

Full bilingual support with:
- Dynamic language switching via `/settings`
- Localized recommendation explanations
- Cultural context awareness
- Proper Khmer typography

## 🔧 Configuration

### Filters

Users can set preferences via `/settings`:
- **Low-cost only**: Show programs under $500
- **Phnom Penh only**: Limit to capital city programs

### Recommendation Tuning

Scoring weights can be adjusted in `config/weights.yaml`:
```yaml
question_weights:
  location_preference: 4.5
  budget_range: 5.0
  field_of_interest: 4.8
  career_goal: 4.2
```

## 📈 Data Sources

University and program data sourced from:
- Ministry of Education, Youth and Sports of Cambodia
- Individual university websites and catalogs
- Academic program databases
- Employment outcome surveys

Data is regularly updated and validated for accuracy.

## 🚀 Deployment

### Production Deployment

```bash
# Build Docker image
docker build -t eduguidebot:v2.0 .

# Run with environment variables
docker run -e BOT_TOKEN=$BOT_TOKEN eduguidebot:v2.0
```

### CI/CD Pipeline

GitHub Actions automatically:
- Runs test suite on pull requests
- Checks code quality (black, flake8)
- Validates documentation
- Builds and tests Docker images

## 📝 Changelog

### v2.0.0 (Current)
- ✨ Hybrid scoring system (70% MCDA + 30% ML)
- ✨ Interactive recommendation cards with inline buttons
- ✨ Advanced filtering system
- ✨ Vectorized MCDA scoring for better performance
- ✨ Enhanced bilingual support
- 🔧 Improved test coverage (95%+)
- 🔧 Better error handling and fallbacks

### v1.0.0
- Basic MCDA-only recommendations
- Simple command interface
- Basic bilingual support

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For technical support or questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation wiki

---

**EduGuideBot v2.0** - Empowering Cambodian students with intelligent university recommendations.
