"""
Hybrid Recommender Coverage Tests
Additional tests to cover missing lines in hybrid_recommender.py and ml_reranker.py
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.hybrid_recommender import get_recommendations, _get_ml_scores
from src.core.ml_reranker import MLReranker
import numpy as np


def test_get_recommendations_import_error():
    with patch("src.core.hybrid_recommender.load_raw", side_effect=ImportError):
        assert get_recommendations({"location_preference": "pp"}) == []


def test_get_ml_scores_invalid_env():
    progs = [{"major_id": "a"}, {"major_id": "b"}]
    with patch.dict("os.environ", {"ML_VERSION": "🤡"}):
        scores = _get_ml_scores({}, progs)
    assert np.all(scores == 0.5)


class TestHybridRecommenderCoverage:
    """Test cases to cover missing lines in hybrid_recommender.py"""

    def test_get_ml_scores_no_model_found(self):
        """Test _get_ml_scores when no model files exist"""
        programmes = [{'major_id': 'test1', 'major_name_en': 'Test 1'}]
        user_answers = {'location_preference': 'pp'}

        # Mock all model files as non-existent
        with patch('pathlib.Path.exists', return_value=False):
            scores = _get_ml_scores(user_answers, programmes)
            assert len(scores) == 1
            assert scores[0] == 0.5  # Neutral score


class TestMLRerankerCoverage:
    """Test cases to cover missing lines in ml_reranker.py"""
    
    def test_ml_reranker_model_not_found(self):
        """Test MLReranker when model file doesn't exist"""
        reranker = MLReranker("nonexistent_model.joblib")
        
        # Model and scaler should be None
        assert reranker.model is None
        assert reranker.scaler is None
    
    def test_ml_reranker_model_load_error(self):
        """Test MLReranker when model loading fails"""
        with patch('pathlib.Path.exists', return_value=True):
            with patch('joblib.load', side_effect=Exception("Load error")):
                reranker = MLReranker("fake_model.joblib")
                
                # Model and scaler should be None
                assert reranker.model is None
                assert reranker.scaler is None
    
    def test_rank_with_no_model(self):
        """Test rank method when no model is loaded"""
        reranker = MLReranker("nonexistent_model.joblib")
        
        user_answers = {'location_preference': 'pp'}
        programmes = [
            {'major_id': 'test1', 'major_name_en': 'Test 1'},
            {'major_id': 'test2', 'major_name_en': 'Test 2'}
        ]
        
        # Should return programmes with dummy ML scores
        result = reranker.rank(user_answers, programmes, top_k=2)
        
        assert len(result) == 2
        for prog in result:
            assert 'ml_score' in prog
            assert prog['ml_score'] == 0.5  # Dummy score
    
    def test_rank_with_missing_mcda_score(self):
        """Test rank method when programmes don't have mcda_score"""
        reranker = MLReranker("nonexistent_model.joblib")

        user_answers = {'location_preference': 'pp'}
        programmes = [
            {'major_id': 'test1', 'major_name_en': 'Test 1'},  # No mcda_score
            {'major_id': 'test2', 'major_name_en': 'Test 2'}   # No mcda_score
        ]

        # Should add mcda_score and return with dummy ML scores
        result = reranker.rank(user_answers, programmes, top_k=2)

        assert len(result) == 2
        for prog in result:
            assert 'ml_score' in prog
            assert prog['ml_score'] == 0.5  # Dummy score when no model
    
    def test_rank_empty_programmes(self):
        """Test rank method with empty programmes list"""
        reranker = MLReranker("fake_model.joblib")
        
        user_answers = {'location_preference': 'pp'}
        programmes = []
        
        result = reranker.rank(user_answers, programmes, top_k=5)
        
        assert isinstance(result, list)
        assert len(result) == 0


def test_add_derived_features_coverage():
    """Test add_derived_features function"""
    from src.core.feature_engineering import add_derived_features

    programmes = [
        {"major_id": "test1", "major_name_en": "Computer Science", "tuition_fees_usd": "800"},
        {"major_id": "test2", "major_name_en": "Business Administration", "tuition_fees_usd": "1200"}
    ]

    enhanced = add_derived_features(programmes)
    assert len(enhanced) == 2
    assert all("field_tag" in prog for prog in enhanced)
    assert all("tuition_bracket" in prog for prog in enhanced)
    assert all("has_scholarship" in prog for prog in enhanced)
    assert enhanced[0]["field_tag"] == "STEM"
    assert enhanced[1]["field_tag"] == "Business"


if __name__ == '__main__':
    pytest.main([__file__])
