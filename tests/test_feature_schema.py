"""
Feature Schema Tests
Tests for feature extraction functionality
"""

import pytest
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.feature_schema import extract_features, FEATURES, DATASET_MEDIANS


class TestFeatureSchema:
    """Test cases for feature schema module"""
    
    def test_extract_features_basic(self):
        """Test basic feature extraction"""
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'mid',
            'field_of_interest': 'stem'
        }
        
        programme = {
            'mcda_score': 0.8,
            'city': 'ភ្នំពេញ',
            'tuition_bracket': 'Medium',
            'field_tag': 'STEM',
            'has_scholarship': True,
            'employment_rate': '85%',
            'tuition_fees_usd': '1200',
            'language_of_instruction': ['Khmer', 'English']
        }
        
        features = extract_features(user_answers, programme)
        
        assert len(features) == len(FEATURES)
        assert features[0] == 0.8  # mcda_score
        assert features[1] == 1.0  # location match
        assert features[2] == 0.0  # budget diff
        assert features[3] == 1.0  # field match
        assert features[4] == 1.0  # has_scholarship
    
    def test_extract_features_location_matching(self):
        """Test location preference matching"""
        user_answers = {'location_preference': 'sr'}
        
        # Test Siem Reap match
        programme_sr = {'city': 'សៀមរាប', 'mcda_score': 0.5}
        features_sr = extract_features(user_answers, programme_sr)
        assert features_sr[1] == 1.0  # Should match
        
        # Test Battambang match
        user_answers['location_preference'] = 'btb'
        programme_btb = {'city': 'បាត់ដំបង', 'mcda_score': 0.5}
        features_btb = extract_features(user_answers, programme_btb)
        assert features_btb[1] == 1.0  # Should match
        
        # Test 'any' preference
        user_answers['location_preference'] = 'any'
        programme_any = {'city': 'កំពត', 'mcda_score': 0.5}
        features_any = extract_features(user_answers, programme_any)
        assert features_any[1] == 0.5  # Should be 0.5 for 'any'
    
    def test_extract_features_budget_differences(self):
        """Test budget bracket difference calculation"""
        user_answers = {'budget_range': 'low'}
        
        # Test exact match
        programme_low = {'tuition_bracket': 'Low', 'mcda_score': 0.5}
        features_low = extract_features(user_answers, programme_low)
        assert features_low[2] == 0.0  # No difference
        
        # Test one bracket difference
        programme_med = {'tuition_bracket': 'Medium', 'mcda_score': 0.5}
        features_med = extract_features(user_answers, programme_med)
        assert features_med[2] == 1.0  # One bracket difference
        
        # Test two bracket difference
        programme_high = {'tuition_bracket': 'High', 'mcda_score': 0.5}
        features_high = extract_features(user_answers, programme_high)
        assert features_high[2] == 2.0  # Two bracket difference
        
        # Test unknown bracket
        programme_unknown = {'tuition_bracket': 'Unknown', 'mcda_score': 0.5}
        features_unknown = extract_features(user_answers, programme_unknown)
        assert features_unknown[2] == 1.0  # Unknown maps to 1, low maps to 0
    
    def test_extract_features_field_matching(self):
        """Test field of interest matching"""
        test_cases = [
            ('stem', 'STEM', 1.0),
            ('business', 'Business', 1.0),
            ('health', 'Health', 1.0),
            ('arts', 'Arts & Humanities', 1.0),
            ('social', 'Social Sciences', 1.0),
            ('education', 'Education', 1.0),
            ('stem', 'Business', 0.0),  # No match
        ]
        
        for user_field, program_field, expected in test_cases:
            user_answers = {'field_of_interest': user_field}
            programme = {'field_tag': program_field, 'mcda_score': 0.5}
            features = extract_features(user_answers, programme)
            assert features[3] == expected, f"Field match failed for {user_field} -> {program_field}"
    
    def test_extract_features_employment_rate_parsing(self):
        """Test employment rate parsing with various formats"""
        user_answers = {}
        
        # Test valid percentage
        programme_valid = {'employment_rate': '85%', 'mcda_score': 0.5}
        features_valid = extract_features(user_answers, programme_valid)
        assert features_valid[5] == 0.85
        
        # Test invalid percentage (should use median)
        programme_invalid = {'employment_rate': 'invalid%', 'mcda_score': 0.5}
        features_invalid = extract_features(user_answers, programme_invalid)
        assert features_invalid[5] == DATASET_MEDIANS['employment_rate']
        
        # Test out of range percentage (should use median)
        programme_out_of_range = {'employment_rate': '150%', 'mcda_score': 0.5}
        features_out_of_range = extract_features(user_answers, programme_out_of_range)
        assert features_out_of_range[5] == DATASET_MEDIANS['employment_rate']
        
        # Test missing employment rate (should use median)
        programme_missing = {'mcda_score': 0.5}
        features_missing = extract_features(user_answers, programme_missing)
        assert features_missing[5] == DATASET_MEDIANS['employment_rate']
    
    def test_extract_features_tuition_parsing(self):
        """Test tuition fee parsing with various formats"""
        user_answers = {}
        
        # Test valid tuition
        programme_valid = {'tuition_fees_usd': '$1,200', 'mcda_score': 0.5}
        features_valid = extract_features(user_answers, programme_valid)
        assert features_valid[6] == 1200.0
        
        # Test invalid tuition (should use median)
        programme_invalid = {'tuition_fees_usd': 'invalid', 'mcda_score': 0.5}
        features_invalid = extract_features(user_answers, programme_invalid)
        assert features_invalid[6] == DATASET_MEDIANS['tuition_usd']
        
        # Test negative tuition (should use median)
        programme_negative = {'tuition_fees_usd': '-500', 'mcda_score': 0.5}
        features_negative = extract_features(user_answers, programme_negative)
        assert features_negative[6] == DATASET_MEDIANS['tuition_usd']
        
        # Test missing tuition (should use median)
        programme_missing = {'mcda_score': 0.5}
        features_missing = extract_features(user_answers, programme_missing)
        assert features_missing[6] == DATASET_MEDIANS['tuition_usd']
    
    def test_extract_features_language_instruction(self):
        """Test language of instruction parsing"""
        user_answers = {}

        # Test with Khmer in list
        programme_kh_list = {
            'language_of_instruction': ['Khmer', 'English'],
            'mcda_score': 0.5
        }
        features_kh_list = extract_features(user_answers, programme_kh_list)
        assert features_kh_list[13] == 1.0  # Language Khmer available (index 13)
        
        # Test with Khmer script
        programme_kh_script = {
            'language_of_instruction': ['ខ្មែរ', 'English'],
            'mcda_score': 0.5
        }
        features_kh_script = extract_features(user_answers, programme_kh_script)
        assert features_kh_script[13] == 1.0

        # Test with string instead of list
        programme_string = {
            'language_of_instruction': 'Khmer',
            'mcda_score': 0.5
        }
        features_string = extract_features(user_answers, programme_string)
        assert features_string[13] == 1.0

        # Test with None
        programme_none = {
            'language_of_instruction': None,
            'mcda_score': 0.5
        }
        features_none = extract_features(user_answers, programme_none)
        assert features_none[13] == 0.0

        # Test without Khmer
        programme_no_kh = {
            'language_of_instruction': ['English'],
            'mcda_score': 0.5
        }
        features_no_kh = extract_features(user_answers, programme_no_kh)
        assert features_no_kh[13] == 0.0
    
    def test_extract_features_one_hot_encoding(self):
        """Test field tag one-hot encoding"""
        user_answers = {}
        
        # Test STEM field
        programme_stem = {'field_tag': 'STEM', 'mcda_score': 0.5}
        features_stem = extract_features(user_answers, programme_stem)
        # Features 7-12 are one-hot encoded field tags
        assert features_stem[7] == 1.0  # STEM
        assert features_stem[8] == 0.0  # Business
        assert features_stem[9] == 0.0  # Health
        assert features_stem[10] == 0.0  # Arts
        assert features_stem[11] == 0.0  # Social
        assert features_stem[12] == 0.0  # Education
        
        # Test Business field
        programme_business = {'field_tag': 'Business', 'mcda_score': 0.5}
        features_business = extract_features(user_answers, programme_business)
        assert features_business[7] == 0.0  # STEM
        assert features_business[8] == 1.0  # Business

    def test_extract_features_log_transform(self):
        """Test tuition USD log transform"""
        import numpy as np
        user_answers = {}

        programme = {'tuition_fees_usd': '1000', 'mcda_score': 0.5}
        features = extract_features(user_answers, programme)

        # Feature 15 is log transform of tuition (0-based indexing)
        expected_log = np.log1p(1000.0)
        assert abs(features[15] - expected_log) < 0.001


if __name__ == '__main__':
    pytest.main([__file__])
