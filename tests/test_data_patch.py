"""
Data Patch Tests
Tests for data audit and auto-patch functionality
"""

import pytest
import json
import tempfile
import subprocess
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))


def create_corrupt_sample_data(temp_dir: Path) -> Path:
    """Create purposely corrupt sample data for testing"""
    sample_data = {
        "university": {
            "id": "test_uni_001",
            "name_kh": "សាកលវិទ្យាល័យសាកល្បង",
            "location": {
                "city": "ភ្នំពេញ"
            }
        },
        "programmes": [
            {
                "major_info": {
                    "id": "prog_001"
                },
                # Missing tuition_fees_usd
                "employment_rate": "",  # Empty employment rate
                "faculty_information": {
                    # Missing student_faculty_ratio
                }
            },
            {
                "major_info": {
                    "id": "prog_002"
                },
                "tuition_fees_usd": 0,  # Zero tuition
                "employment_rate": None,  # Null employment rate
                "faculty_information": {
                    "student_faculty_ratio": ""  # Empty ratio
                }
            }
        ]
    }
    
    # Create sample file
    sample_file = temp_dir / "corrupt_sample.json"
    with open(sample_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    return sample_file


def test_data_audit_detects_issues():
    """Test that data audit detects issues in corrupt data"""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create corrupt sample data
        sample_file = create_corrupt_sample_data(temp_path)
        
        # Run audit script
        result = subprocess.run([
            sys.executable, "scripts/data_audit.py",
            "--data-dir", str(temp_path),
            "--output", str(temp_path / "audit_report.md")
        ], capture_output=True, text=True)
        
        # Should exit with code 1 (issues found)
        assert result.returncode == 1, "Audit should detect issues and return exit code 1"
        
        # Check that report was created
        report_file = temp_path / "audit_report.md"
        assert report_file.exists(), "Audit report should be created"
        
        # Check report content
        with open(report_file, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        assert "tuition_fees_usd" in report_content, "Report should mention tuition issues"
        assert "employment_rate" in report_content, "Report should mention employment rate issues"
        assert "student_faculty_ratio" in report_content, "Report should mention faculty ratio issues"


def test_auto_patch_fixes_issues():
    """Test that auto-patch fixes critical issues"""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create corrupt sample data
        sample_file = create_corrupt_sample_data(temp_path)
        
        # Create clean directory
        clean_dir = temp_path / "clean"
        log_file = temp_path / "patch_log.csv"
        
        # Run auto-patch
        result = subprocess.run([
            sys.executable, "tools/auto_patch.py",
            "--raw-dir", str(temp_path),
            "--clean-dir", str(clean_dir),
            "--log", str(log_file)
        ], capture_output=True, text=True)
        
        # Should succeed
        assert result.returncode == 0, f"Auto-patch should succeed, got: {result.stderr}"
        
        # Check that clean file was created
        clean_file = clean_dir / "corrupt_sample.json"
        assert clean_file.exists(), "Clean file should be created"
        
        # Check that log was created
        assert log_file.exists(), "Patch log should be created"
        
        # Verify patches were applied
        with open(clean_file, 'r', encoding='utf-8') as f:
            clean_data = json.load(f)
        
        programmes = clean_data['programmes']
        
        # Check first programme
        prog1 = programmes[0]
        assert 'tuition_fees_usd' in prog1, "Tuition should be added"
        assert prog1['tuition_fees_usd'] != '', "Tuition should not be empty"
        assert prog1['employment_rate'] != '', "Employment rate should be patched"
        assert prog1['faculty_information']['student_faculty_ratio'] == '25:1', "Faculty ratio should be patched"
        
        # Check second programme
        prog2 = programmes[1]
        assert prog2['tuition_fees_usd'] != 0, "Zero tuition should be patched"
        assert prog2['employment_rate'] is not None, "Null employment rate should be patched"
        assert prog2['faculty_information']['student_faculty_ratio'] == '25:1', "Empty ratio should be patched"


def test_makefile_data_clean_target():
    """Test that Makefile data-clean target works"""
    # This is a basic test that the Makefile target exists and can be parsed
    makefile_path = Path("Makefile")
    
    if not makefile_path.exists():
        pytest.skip("Makefile not found")
    
    with open(makefile_path, 'r') as f:
        makefile_content = f.read()
    
    assert "data-clean:" in makefile_content, "Makefile should have data-clean target"
    assert "scripts/data_audit.py" in makefile_content, "Should reference audit script"
    assert "tools/auto_patch.py" in makefile_content, "Should reference patch script"


def test_audit_script_help():
    """Test that audit script shows help without error"""
    result = subprocess.run([
        sys.executable, "scripts/data_audit.py", "--help"
    ], capture_output=True, text=True)
    
    assert result.returncode == 0, "Audit script help should work"
    assert "audit" in result.stdout.lower(), "Help should mention audit"


def test_patch_script_help():
    """Test that patch script shows help without error"""
    result = subprocess.run([
        sys.executable, "tools/auto_patch.py", "--help"
    ], capture_output=True, text=True)
    
    assert result.returncode == 0, "Patch script help should work"
    assert "patch" in result.stdout.lower(), "Help should mention patch"


def test_patch_log_format():
    """Test that patch log has correct CSV format"""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create minimal corrupt data
        sample_data = {
            "university": {"id": "test", "name_kh": "test", "location": {"city": "test"}},
            "programmes": [{"major_info": {"id": "test"}, "tuition_fees_usd": 0}]
        }
        
        sample_file = temp_path / "test.json"
        with open(sample_file, 'w') as f:
            json.dump(sample_data, f)
        
        # Run patch
        clean_dir = temp_path / "clean"
        log_file = temp_path / "log.csv"
        
        subprocess.run([
            sys.executable, "tools/auto_patch.py",
            "--raw-dir", str(temp_path),
            "--clean-dir", str(clean_dir),
            "--log", str(log_file)
        ], capture_output=True)
        
        if log_file.exists():
            with open(log_file, 'r') as f:
                log_content = f.read()
            
            # Check CSV header
            assert "file,field,old_value,new_value,action" in log_content, "Log should have correct CSV header"
