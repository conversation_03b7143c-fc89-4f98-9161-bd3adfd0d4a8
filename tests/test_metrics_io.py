"""
Metrics I/O Tests
Tests for metrics file operations and Flask import
"""

import sys
from pathlib import Path
from unittest.mock import patch

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.infra.metrics import read_metrics, _lazy_import_flask


def test_read_metrics_missing_file(tmp_path, monkeypatch):
    # point the module at a non-existent file
    monkeypatch.setattr("src.infra.metrics.METRICS_FILE", tmp_path/"ghost.ndjson")
    assert read_metrics() == []


def test_lazy_flask_import_no_dep():
    with patch.dict("sys.modules", {"flask": None}):
        with patch("builtins.__import__", side_effect=ImportError("No module named 'flask'")):
            Flask, Response = _lazy_import_flask()
            assert Flask is None and Response is None
