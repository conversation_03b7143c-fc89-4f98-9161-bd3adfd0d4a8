# EduGuideBot Test Logs

This directory contains comprehensive test logs for the EduGuideBot system.

## Log Files

### `ux_simulation.log`
Complete UX simulation results with detailed test execution data:
- Test names and execution times
- Pass/fail status for each test
- Error details and stack traces
- User journey validation results

### `failures.log`
Detailed failure information for debugging:
- Full error messages and tracebacks
- Test context and input data
- Expected vs actual behavior
- Reproduction steps

### `callback_coverage.log`
Button and callback coverage analysis:
- All registered callback patterns
- Coverage percentage by handler type
- Orphaned buttons (buttons without handlers)
- Missing handler warnings

### `performance.log`
Performance metrics and timing data:
- Response times for each handler
- Memory usage during tests
- Database query performance
- Recommendation generation timing

## Log Format

All logs use structured JSON format for easy parsing:

```json
{
  "timestamp": 1640995200.123,
  "test_name": "Home Screen Flow",
  "status": "PASS|FAIL",
  "details": "Detailed description",
  "context": {
    "user_action": "/start",
    "bot_response": "Welcome message",
    "callback_data": "START_QUIZ",
    "error": "Optional error details"
  }
}
```

## Usage

Logs are automatically generated during:
- `python tools/ux_simulator.py` - UX simulation runs
- `python tools/comprehensive_bot_tester.py` - Full bot testing
- `pytest tests/` - Unit test execution
- CI/CD pipeline runs

## Retention

- Logs are rotated daily
- Keep last 30 days of logs
- Critical failures are archived separately
