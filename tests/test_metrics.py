"""
Metrics Tests
Tests for the lightweight metrics module and exporter
"""

import pytest
import json
import tempfile
import time
import os
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.infra.metrics import log_event, histogram, track_latency, count_calls, clear_metrics, read_metrics


def test_log_event_writes_correct_format():
    """Test that log_event writes a line with correct keys"""
    # Clear any existing metrics
    clear_metrics()
    
    # Log a test event
    test_data = {"user_id": 123, "action": "test"}
    log_event("test_event", test_data)
    
    # Read back the metrics
    metrics = read_metrics()
    
    assert len(metrics) == 1, "Should have exactly one metric"
    
    metric = metrics[0]
    assert "timestamp" in metric, "Should have timestamp"
    assert "event" in metric, "Should have event"
    assert "data" in metric, "Should have data"
    
    assert metric["event"] == "test_event", "Event name should match"
    assert metric["data"] == test_data, "Data should match"
    
    # Verify timestamp format (ISO 8601)
    timestamp = metric["timestamp"]
    assert "T" in timestamp, "Timestamp should be ISO format"
    assert timestamp.endswith("Z") or "+" in timestamp, "Timestamp should have timezone"


def test_histogram_logs_correctly():
    """Test that histogram function logs values correctly"""
    clear_metrics()
    
    # Log histogram values
    histogram("test_metric", 42.5)
    histogram("test_metric", 100.0)
    
    # Read metrics
    metrics = read_metrics()
    
    assert len(metrics) == 2, "Should have two histogram entries"
    
    for metric in metrics:
        assert metric["event"] == "histogram", "Should be histogram event"
        assert "name" in metric["data"], "Should have name in data"
        assert "value" in metric["data"], "Should have value in data"
        assert metric["data"]["name"] == "test_metric", "Name should match"


def test_track_latency_decorator_sync():
    """Test that track_latency decorator records latencies > 0 for sync functions"""
    clear_metrics()
    
    @track_latency("test_sync_latency")
    def slow_function():
        time.sleep(0.01)  # 10ms delay
        return "done"
    
    # Call the function
    result = slow_function()
    
    assert result == "done", "Function should return correct value"
    
    # Check metrics
    metrics = read_metrics()
    
    assert len(metrics) == 1, "Should have one latency metric"
    
    metric = metrics[0]
    assert metric["event"] == "latency", "Should be latency event"
    assert metric["data"]["event_name"] == "test_sync_latency", "Event name should match"
    assert metric["data"]["latency_ms"] > 0, "Latency should be > 0"
    assert metric["data"]["latency_ms"] >= 10, "Latency should be at least 10ms"
    assert metric["data"]["function"] == "slow_function", "Function name should be recorded"


def test_track_latency_decorator_async():
    """Test that track_latency decorator works with async functions"""
    clear_metrics()
    
    @track_latency("test_async_latency")
    async def async_slow_function():
        await asyncio.sleep(0.01)  # 10ms delay
        return "async_done"
    
    # This test requires asyncio, skip if not available
    try:
        import asyncio
    except ImportError:
        pytest.skip("asyncio not available")
    
    # Run the async function
    result = asyncio.run(async_slow_function())
    
    assert result == "async_done", "Async function should return correct value"
    
    # Check metrics
    metrics = read_metrics()
    
    assert len(metrics) == 1, "Should have one latency metric"
    
    metric = metrics[0]
    assert metric["event"] == "latency", "Should be latency event"
    assert metric["data"]["event_name"] == "test_async_latency", "Event name should match"
    assert metric["data"]["latency_ms"] > 0, "Latency should be > 0"


def test_count_calls_decorator():
    """Test that count_calls decorator increments counter"""
    clear_metrics()
    
    @count_calls("test_call_count")
    def counted_function(x):
        return x * 2
    
    # Call function multiple times
    counted_function(1)
    counted_function(2)
    counted_function(3)
    
    # Check metrics
    metrics = read_metrics()
    
    assert len(metrics) == 3, "Should have three call count metrics"
    
    for metric in metrics:
        assert metric["event"] == "call_count", "Should be call_count event"
        assert metric["data"]["event_name"] == "test_call_count", "Event name should match"
        assert metric["data"]["function"] == "counted_function", "Function name should be recorded"


def test_metrics_file_creation():
    """Test that metrics file and directory are created automatically"""
    clear_metrics()
    
    # Ensure logs directory doesn't exist
    logs_dir = Path("logs")
    if logs_dir.exists():
        import shutil
        shutil.rmtree(logs_dir)
    
    # Log an event
    log_event("test_creation", {"test": True})
    
    # Check that directory and file were created
    assert logs_dir.exists(), "Logs directory should be created"
    assert (logs_dir / "metrics.ndjson").exists(), "Metrics file should be created"


def test_read_metrics_empty_file():
    """Test reading metrics when file doesn't exist"""
    clear_metrics()
    
    metrics = read_metrics()
    assert metrics == [], "Should return empty list when no metrics file"


def test_exporter_import():
    """Test that exporter can be imported when METRICS_ENABLED is set"""
    # Skip if METRICS_ENABLED not set
    if os.environ.get("METRICS_ENABLED") != "1":
        pytest.skip("Exporter test skipped (METRICS_ENABLED not set)")
    
    try:
        from src.infra.exporter import parse_metrics, format_prometheus_metrics
        
        # Test with empty metrics
        clear_metrics()
        aggregated = parse_metrics()
        
        assert isinstance(aggregated, dict), "parse_metrics should return dict"
        assert "call_counts" in aggregated, "Should have call_counts"
        assert "latency_buckets" in aggregated, "Should have latency_buckets"
        assert "histogram_values" in aggregated, "Should have histogram_values"
        
        # Test formatting
        prometheus_text = format_prometheus_metrics(aggregated)
        assert isinstance(prometheus_text, str), "Should return string"
        assert "eduguidebot" in prometheus_text, "Should contain metric prefix"
        
    except ImportError as e:
        pytest.skip(f"Exporter import failed: {e}")


def test_metrics_with_none_data():
    """Test that metrics work with None data"""
    clear_metrics()
    
    log_event("test_none", None)
    
    metrics = read_metrics()
    assert len(metrics) == 1, "Should have one metric"
    assert metrics[0]["data"] == {}, "None data should become empty dict"


def test_multiple_histogram_values():
    """Test histogram with multiple values for aggregation"""
    clear_metrics()

    # Log multiple values for same histogram
    values = [0.1, 0.5, 0.8, 0.3, 0.9]
    for value in values:
        histogram("test_histogram", value)

    metrics = read_metrics()
    assert len(metrics) == len(values), f"Should have {len(values)} histogram entries"

    # All should be histogram events
    for metric in metrics:
        assert metric["event"] == "histogram", "Should be histogram event"
        assert metric["data"]["name"] == "test_histogram", "Name should match"


def test_parse_metrics_empty_file():
    """Test parse_metrics with empty/missing file"""
    clear_metrics()

    try:
        from src.infra.exporter import parse_metrics

        # Should not crash with empty file
        aggregated = parse_metrics()

        assert isinstance(aggregated, dict), "Should return dict"
        assert "call_counts" in aggregated, "Should have call_counts"
        assert "latency_buckets" in aggregated, "Should have latency_buckets"
        assert "histogram_values" in aggregated, "Should have histogram_values"

        # All should be empty
        assert len(aggregated["call_counts"]) == 0, "call_counts should be empty"
        assert len(aggregated["latency_buckets"]) == 0, "latency_buckets should be empty"
        assert len(aggregated["histogram_values"]) == 0, "histogram_values should be empty"

    except ImportError:
        pytest.skip("Exporter not available")


def test_format_prometheus_metrics_empty():
    """Test format_prometheus_metrics with empty data"""
    try:
        from src.infra.exporter import format_prometheus_metrics

        # Test with empty aggregated data
        empty_aggregated = {
            "call_counts": {},
            "latency_buckets": {},
            "histogram_values": {}
        }

        prometheus_text = format_prometheus_metrics(empty_aggregated)

        assert isinstance(prometheus_text, str), "Should return string"
        assert len(prometheus_text) > 0, "Should not be empty"
        assert "# HELP" in prometheus_text, "Should contain help text"
        assert "eduguidebot" in prometheus_text, "Should contain metric prefix"
        assert 'event="none"' in prometheus_text, "Should contain default metrics"

    except ImportError:
        pytest.skip("Exporter not available")


def test_exporter_startup_with_flask():
    """Test exporter startup when Flask is available"""
    # Skip if METRICS_ENABLED not set
    if os.environ.get("METRICS_ENABLED") != "1":
        pytest.skip("Exporter test skipped (METRICS_ENABLED not set)")

    try:
        from src.infra.exporter import _lazy_import_flask, create_app

        Flask, Response = _lazy_import_flask()
        if Flask is None:
            pytest.skip("Flask unavailable")

        # Should be able to create app
        app = create_app(Flask, Response)
        assert app is not None, "Should create Flask app"

        # Test client for basic endpoint check
        with app.test_client() as client:
            response = client.get('/health')
            assert response.status_code == 200, "Health endpoint should work"

            response = client.get('/metrics')
            assert response.status_code == 200, "Metrics endpoint should work"
            assert 'text/plain' in response.content_type, "Should return plain text"

    except ImportError:
        pytest.skip("Flask unavailable")


def test_exporter_main_function():
    """Test main function behavior"""
    try:
        from src.infra.exporter import main

        # Test with METRICS_ENABLED=0 (should return early)
        original_env = os.environ.get("METRICS_ENABLED")
        os.environ["METRICS_ENABLED"] = "0"

        # Should return without error
        main()  # This should print "disabled" and return

        # Restore environment
        if original_env is not None:
            os.environ["METRICS_ENABLED"] = original_env
        else:
            os.environ.pop("METRICS_ENABLED", None)

    except ImportError:
        pytest.skip("Exporter not available")


def test_async_function_detection_coverage():
    """Test async function detection code paths for coverage"""
    from src.infra.metrics import track_latency, count_calls

    # Test the hasattr checks for async function detection
    # This covers the lines that check for __call__ and __await__

    @track_latency("test_coverage")
    def sync_function():
        return "sync_result"

    @count_calls("test_coverage")
    def another_sync_function():
        return "sync_result"

    # Call the functions to execute the decorator logic
    result1 = sync_function()
    result2 = another_sync_function()

    assert result1 == "sync_result"
    assert result2 == "sync_result"


def test_read_metrics_with_invalid_json():
    """Test read_metrics handles invalid JSON gracefully"""
    from src.infra.metrics import read_metrics, clear_metrics, METRICS_FILE

    clear_metrics()

    # Write some valid and invalid JSON lines
    with open(METRICS_FILE, "w", encoding="utf-8") as f:
        f.write('{"valid": "json"}\n')
        f.write('invalid json line\n')
        f.write('{"another": "valid"}\n')

    metrics = read_metrics()

    # Should only return valid JSON entries
    assert len(metrics) == 2
    assert metrics[0]["valid"] == "json"
    assert metrics[1]["another"] == "valid"


def test_clear_metrics():
    """Test clear_metrics function"""
    from src.infra.metrics import clear_metrics, log_event, read_metrics, METRICS_FILE

    # Create some metrics
    log_event("test", {"data": "test"})
    assert METRICS_FILE.exists()
    assert len(read_metrics()) > 0

    # Clear metrics
    clear_metrics()
    assert not METRICS_FILE.exists()
    assert len(read_metrics()) == 0


def test_clear_metrics_nonexistent_file():
    """Test clear_metrics when file doesn't exist"""
    from src.infra.metrics import clear_metrics, METRICS_FILE

    # Ensure file doesn't exist
    if METRICS_FILE.exists():
        METRICS_FILE.unlink()

    # Should not crash
    clear_metrics()
    assert not METRICS_FILE.exists()
