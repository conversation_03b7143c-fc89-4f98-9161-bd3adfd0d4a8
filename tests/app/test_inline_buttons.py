"""
Inline Buttons Tests
Tests for inline keyboard buttons in recommendation cards
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.bot.handlers import display_recommendations_list, back_to_list_callback, explain_rec_callback


@pytest.fixture
def mock_update():
    """Mock Telegram Update object"""
    update = MagicMock()
    update.message.reply_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Mock Telegram Context object"""
    context = MagicMock()
    context.user_data = {
        "last_recs": {
            "cs-001": {
                "major_id": "cs-001",
                "major_name_kh": "វិទ្យាសាស្ត្រកុំព្យូទ័រ",
                "university_name_kh": "សាកលវិទ្យាល័យភូមិន្ទ",
                "city": "ភ្នំពេញ",
                "tuition_fees_usd": 400,
                "mcda_score": 0.85,
                "ml_score": 0.72,
                "hybrid_score": 0.81,
                "mcda_reason": "ស្ថិតនៅភ្នំពេញតាមការចង់បាន"
            }
        },
        "last_answers": {
            "location_preference": "pp",
            "budget_range": "low",
            "field_of_interest": "stem"
        }
    }
    return context


@pytest.fixture
def sample_recommendations():
    """Sample recommendations data"""
    return [
        {
            "major_id": "cs-001",
            "major_name_kh": "វិទ្យាសាស្ត្រកុំព្យូទ័រ",
            "university_name_kh": "សាកលវិទ្យាល័យភូមិន្ទ",
            "city": "ភ្នំពេញ",
            "tuition_fees_usd": 400,
            "mcda_score": 0.85,
            "ml_score": 0.72,
            "hybrid_score": 0.81,
            "mcda_reason": "ស្ថិតនៅភ្នំពេញតាមការចង់បាន"
        }
    ]


class TestInlineButtons:
    """Test inline keyboard buttons in recommendations"""

    @pytest.mark.asyncio
    async def test_recommendation_cards_have_buttons(self, mock_update, mock_context, sample_recommendations):
        """Test that recommendation cards include inline buttons"""
        with patch('src.bot.handlers.format_recommendation_card') as mock_format, \
             patch('src.bot.handlers.t') as mock_t, \
             patch('src.bot.handlers.get_lang') as mock_get_lang:
            
            mock_format.return_value = "Test recommendation card"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            mock_get_lang.return_value = "en"
            
            await display_recommendations_list(mock_update, mock_context, sample_recommendations)

            # Should have called reply_text twice (title + recommendation card)
            assert mock_update.message.reply_text.call_count == 2

            # Get the second call (recommendation card with buttons)
            second_call_kwargs = mock_update.message.reply_text.call_args_list[1][1]

            # Check that reply_markup was provided in the recommendation card
            assert 'reply_markup' in second_call_kwargs
            reply_markup = second_call_kwargs['reply_markup']

            # Check that keyboard has buttons
            assert hasattr(reply_markup, 'inline_keyboard')
            assert len(reply_markup.inline_keyboard) > 0
            assert len(reply_markup.inline_keyboard[0]) == 2  # Back and Why buttons

    @pytest.mark.asyncio
    async def test_back_to_list_callback_with_cache(self, mock_context):
        """Test back to list callback when cache exists"""
        # Mock callback query
        query = MagicMock()
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        
        update = MagicMock()
        update.callback_query = query
        
        with patch('src.bot.handlers.display_recommendations_list') as mock_display_recs, \
             patch('src.bot.handlers.get_lang') as mock_get_lang:

            mock_get_lang.return_value = "en"
            mock_display_recs.return_value = None  # Make it awaitable

            await back_to_list_callback(update, mock_context)

            # Should answer the callback
            query.answer.assert_called_once()

            # Should call display_recommendations_list with cached data
            mock_display_recs.assert_called_once()
            call_args = mock_display_recs.call_args
            assert call_args[0][2]  # recommendations list should not be empty
            assert call_args[1]['query'] == query  # should pass query

    @pytest.mark.asyncio
    async def test_back_to_list_callback_no_cache(self, mock_context):
        """Test back to list callback when no cache exists"""
        # Remove cache
        mock_context.user_data["last_recs"] = {}
        
        # Mock callback query
        query = MagicMock()
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        
        update = MagicMock()
        update.callback_query = query
        
        with patch('src.bot.handlers.get_lang') as mock_get_lang:
            mock_get_lang.return_value = "en"
            
            await back_to_list_callback(update, mock_context)
            
            # Should answer the callback
            query.answer.assert_called_once()
            
            # Should show error message
            query.edit_message_text.assert_called_once()
            call_kwargs = query.edit_message_text.call_args[1]
            assert "No previous recommendations found" in call_kwargs['text']

    @pytest.mark.asyncio
    async def test_explain_rec_callback_valid_program(self, mock_context):
        """Test explain recommendation callback with valid program"""
        # Mock callback query
        query = MagicMock()
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        query.data = "explain_rec:cs-001"
        
        update = MagicMock()
        update.callback_query = query
        
        with patch('src.bot.handlers.get_lang') as mock_get_lang, \
             patch('src.bot.handlers.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await explain_rec_callback(update, mock_context)
            
            # Should answer the callback
            query.answer.assert_called_once()
            
            # Should edit message with explanation
            query.edit_message_text.assert_called_once()
            call_kwargs = query.edit_message_text.call_args[1]
            
            # Check that explanation text contains scores
            explanation_text = call_kwargs['text']
            assert "translated_hybrid_score_explanation" in explanation_text
            
            # Check that reply markup has back button
            assert 'reply_markup' in call_kwargs

    @pytest.mark.asyncio
    async def test_explain_rec_callback_invalid_program(self, mock_context):
        """Test explain recommendation callback with invalid program ID"""
        # Mock callback query
        query = MagicMock()
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        query.data = "explain_rec:invalid-id"
        
        update = MagicMock()
        update.callback_query = query
        
        with patch('src.bot.handlers.get_lang') as mock_get_lang:
            mock_get_lang.return_value = "en"
            
            await explain_rec_callback(update, mock_context)
            
            # Should answer the callback
            query.answer.assert_called_once()
            
            # Should show error message
            query.edit_message_text.assert_called_once()
            call_kwargs = query.edit_message_text.call_args[1]
            assert "not found in cache" in call_kwargs['text']

    @pytest.mark.asyncio
    async def test_explain_rec_callback_invalid_data(self, mock_context):
        """Test explain recommendation callback with invalid callback data"""
        # Mock callback query
        query = MagicMock()
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        query.data = "invalid_callback_data"
        
        update = MagicMock()
        update.callback_query = query
        
        await explain_rec_callback(update, mock_context)
        
        # Should answer the callback
        query.answer.assert_called_once()
        
        # Should show error message
        query.edit_message_text.assert_called_once()
        call_kwargs = query.edit_message_text.call_args[1]
        assert "Invalid callback data" in call_kwargs['text']


class TestButtonIntegration:
    """Test button integration with recommendation flow"""

    @pytest.mark.asyncio
    async def test_recommendation_to_explanation_flow(self, mock_context, sample_recommendations):
        """Test full flow from recommendation to explanation"""
        # Step 1: Show recommendations (should have buttons)
        update = MagicMock()
        update.message.reply_text = AsyncMock()
        
        with patch('src.bot.handlers.format_recommendation_card') as mock_format, \
             patch('src.bot.handlers.t') as mock_t, \
             patch('src.bot.handlers.get_lang') as mock_get_lang:
            
            mock_format.return_value = "Test recommendation card"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            mock_get_lang.return_value = "en"
            
            await display_recommendations_list(update, mock_context, sample_recommendations)
            
            # Verify buttons were added
            call_kwargs = update.message.reply_text.call_args[1]
            assert 'reply_markup' in call_kwargs
        
        # Step 2: Click "Why this rec?" button
        query = MagicMock()
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        query.data = "explain_rec:cs-001"
        
        explain_update = MagicMock()
        explain_update.callback_query = query
        
        with patch('src.bot.handlers.get_lang') as mock_get_lang, \
             patch('src.bot.handlers.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await explain_rec_callback(explain_update, mock_context)
            
            # Verify explanation was shown
            query.edit_message_text.assert_called_once()
            call_kwargs = query.edit_message_text.call_args[1]
            assert 'text' in call_kwargs
            assert 'reply_markup' in call_kwargs

    @pytest.mark.asyncio
    async def test_button_callback_data_format(self, sample_recommendations):
        """Test that button callback data is formatted correctly"""
        update = MagicMock()
        update.message.reply_text = AsyncMock()
        context = MagicMock()
        context.user_data = {}
        
        with patch('src.bot.handlers.format_recommendation_card') as mock_format, \
             patch('src.bot.handlers.t') as mock_t, \
             patch('src.bot.handlers.get_lang') as mock_get_lang:
            
            mock_format.return_value = "Test recommendation card"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            mock_get_lang.return_value = "en"
            
            await display_recommendations_list(update, context, sample_recommendations)
            
            # Get the reply markup
            call_kwargs = update.message.reply_text.call_args[1]
            reply_markup = call_kwargs['reply_markup']
            
            # Check button callback data
            buttons = reply_markup.inline_keyboard[0]
            back_button = buttons[0]
            explain_button = buttons[1]
            
            assert back_button.callback_data == "back_to_list"
            assert explain_button.callback_data == "explain_rec:cs-001"
