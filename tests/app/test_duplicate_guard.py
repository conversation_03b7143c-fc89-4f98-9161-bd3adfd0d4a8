"""
Duplicate Guard Tests
Tests for idempotent handler registration in app.py
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import MagicMock, patch

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.bot.app import main, _add_command_handlers


@pytest.fixture
def mock_application():
    """Mock Telegram Application object"""
    app = MagicMock()
    app.add_handler = MagicMock()
    app.add_error_handler = MagicMock()
    app.run_polling = MagicMock()
    return app


@pytest.fixture
def mock_env_vars():
    """Mock environment variables"""
    return {
        'BOT_TOKEN': 'test_token_123'
    }


class TestDuplicateGuard:
    """Test duplicate handler registration guard"""

    @patch('src.bot.app.Application')
    @patch.dict('os.environ', {'BOT_TOKEN': 'test_token_123'})
    def test_handlers_registered_once(self, mock_app_builder):
        """Test that handlers are only registered once"""
        # Reset global flag for this test
        import src.bot.app
        src.bot.app._handlers_registered = False

        mock_app = MagicMock()
        mock_builder = MagicMock()
        mock_token_builder = MagicMock()
        mock_post_init_builder = MagicMock()

        # Setup the builder chain
        mock_app_builder.builder.return_value = mock_builder
        mock_builder.token.return_value = mock_token_builder
        mock_token_builder.post_init.return_value = mock_post_init_builder
        mock_post_init_builder.build.return_value = mock_app

        # First call - should register handlers
        with patch('src.bot.app.create_conversation_handler') as mock_conv_handler, \
             patch('src.bot.app.logger') as mock_logger:

            mock_conv_handler.return_value = MagicMock()

            main()

            # Should have registered handlers
            assert mock_app.add_handler.call_count > 0
            # Check global flag was set
            assert src.bot.app._handlers_registered is True

    @patch('src.bot.app.Application')
    @patch.dict('os.environ', {'BOT_TOKEN': 'test_token_123'})
    def test_duplicate_registration_prevented(self, mock_app_builder):
        """Test that duplicate registration is prevented"""
        # Set global flag to already registered
        import src.bot.app
        src.bot.app._handlers_registered = True

        mock_app = MagicMock()
        mock_builder = MagicMock()
        mock_token_builder = MagicMock()
        mock_post_init_builder = MagicMock()

        # Setup the builder chain
        mock_app_builder.builder.return_value = mock_builder
        mock_builder.token.return_value = mock_token_builder
        mock_token_builder.post_init.return_value = mock_post_init_builder
        mock_post_init_builder.build.return_value = mock_app

        with patch('src.bot.app.logger') as mock_logger:
            main()

            # Should have skipped registration due to global flag
            # The add_handler call count should be 0 since handlers were skipped
            assert mock_app.add_handler.call_count == 0

    def test_add_command_handlers_function(self, mock_application):
        """Test _add_command_handlers function"""
        # Reset global flag for this test
        import src.bot.app
        src.bot.app._handlers_registered = False

        _add_command_handlers(mock_application)

        # Should add all expected handlers
        assert mock_application.add_handler.call_count >= 9  # At least 9 command handlers
        
        # Verify specific handlers were added
        call_args_list = [call[0][0] for call in mock_application.add_handler.call_args_list]

        # Check for CommandHandler instances (we can't easily check the exact commands)
        command_handlers = [handler for handler in call_args_list
                          if 'CommandHandler' in str(type(handler))]
        message_handlers = [handler for handler in call_args_list
                          if 'MessageHandler' in str(type(handler))]

        # Should have command handlers and at least one message handler (for details)
        assert len(command_handlers) >= 8  # start, help, cancel, filterlanguage, etc.
        assert len(message_handlers) >= 1   # details command pattern

    @patch.dict('os.environ', {'BOT_TOKEN': 'test_token_123'})
    def test_dry_run_mode_skips_registration(self):
        """Test that dry-run mode skips handler registration"""
        with patch('builtins.print') as mock_print:
            main(dry_run=True)
            
            # Should print dry-run message and return early
            mock_print.assert_called_with("🛈 Bot initialised (dry-run mode) – no network calls")

    @patch.dict('os.environ', {}, clear=True)  # No BOT_TOKEN
    def test_missing_token_triggers_dry_run(self):
        """Test that missing token triggers dry-run mode"""
        with patch('builtins.print') as mock_print:
            main()

            # Should print dry-run message and return early
            mock_print.assert_called_with("🛈 Bot initialised (dry-run mode) – no network calls")

    @patch.dict('os.environ', {'BOT_TOKEN': 'dry'})
    def test_dry_token_triggers_dry_run(self):
        """Test that 'dry' token triggers dry-run mode"""
        with patch('builtins.print') as mock_print:
            main()
            
            # Should print dry-run message and return early
            mock_print.assert_called_with("🛈 Bot initialised (dry-run mode) – no network calls")


class TestHandlerRegistration:
    """Test handler registration details"""

    def test_command_handlers_registration(self, mock_application):
        """Test that all expected command handlers are registered"""
        # Reset global flag for this test
        import src.bot.app
        src.bot.app._handlers_registered = False

        _add_command_handlers(mock_application)

        # Get all handler registration calls
        calls = mock_application.add_handler.call_args_list

        # Should have registered multiple handlers
        assert len(calls) >= 9

        # Verify that handlers were actually added
        mock_application.add_handler.assert_called()

    def test_handler_types_registered(self, mock_application):
        """Test that correct handler types are registered"""
        # Reset global flag for this test
        import src.bot.app
        src.bot.app._handlers_registered = False

        _add_command_handlers(mock_application)

        # Should have registered multiple handlers
        assert mock_application.add_handler.call_count >= 10  # 8 commands + 1 message + 1 conversation + 1 callback

        # Verify handlers were added
        mock_application.add_handler.assert_called()

    def test_idempotent_registration(self, mock_application):
        """Test that handler registration is idempotent"""
        # Reset global flag for this test
        import src.bot.app
        src.bot.app._handlers_registered = False

        # Register handlers twice
        _add_command_handlers(mock_application)
        first_call_count = mock_application.add_handler.call_count

        _add_command_handlers(mock_application)
        second_call_count = mock_application.add_handler.call_count

        # Should NOT have doubled the calls - second call should be skipped due to global flag
        assert second_call_count == first_call_count


class TestApplicationLifecycle:
    """Test application lifecycle with guards"""

    @patch('src.bot.app.Application')
    @patch.dict('os.environ', {'BOT_TOKEN': 'test_token_123'})
    def test_full_application_setup(self, mock_app_builder):
        """Test full application setup with all components"""
        # Reset global flag for this test
        import src.bot.app
        src.bot.app._handlers_registered = False

        mock_app = MagicMock()
        mock_builder = MagicMock()
        mock_token_builder = MagicMock()
        mock_post_init_builder = MagicMock()

        # Setup the builder chain
        mock_app_builder.builder.return_value = mock_builder
        mock_builder.token.return_value = mock_token_builder
        mock_token_builder.post_init.return_value = mock_post_init_builder
        mock_post_init_builder.build.return_value = mock_app

        with patch('src.bot.app.create_conversation_handler') as mock_conv_handler, \
             patch('src.bot.app.logger') as mock_logger:

            mock_conv_handler.return_value = MagicMock()

            main()

            # Should have set up all components
            assert mock_app.add_handler.call_count > 0  # Command handlers
            mock_app.add_error_handler.assert_called_once()  # Error handler
            mock_app.run_polling.assert_called_once()   # Start polling

            # Should have marked as registered
            assert src.bot.app._handlers_registered is True

    @patch('src.bot.app.Application')
    @patch.dict('os.environ', {'BOT_TOKEN': 'test_token_123'})
    def test_application_setup_with_existing_registration(self, mock_app_builder):
        """Test application setup when handlers already registered"""
        # Set global flag to already registered
        import src.bot.app
        src.bot.app._handlers_registered = True

        mock_app = MagicMock()
        mock_builder = MagicMock()
        mock_token_builder = MagicMock()
        mock_post_init_builder = MagicMock()

        # Setup the builder chain
        mock_app_builder.builder.return_value = mock_builder
        mock_builder.token.return_value = mock_token_builder
        mock_token_builder.post_init.return_value = mock_post_init_builder
        mock_post_init_builder.build.return_value = mock_app

        with patch('src.bot.app.logger') as mock_logger:
            main()

            # Should have skipped handler registration
            assert mock_app.add_handler.call_count == 0

            # Should still call polling (main continues after handler setup)
            mock_app.run_polling.assert_called_once()

    @patch('src.bot.app.Application')
    @patch.dict('os.environ', {'BOT_TOKEN': 'test_token_123'})
    def test_error_handler_registration(self, mock_app_builder):
        """Test that error handler is properly registered"""
        # Reset global flag for this test
        import src.bot.app
        src.bot.app._handlers_registered = False

        mock_app = MagicMock()
        mock_builder = MagicMock()
        mock_token_builder = MagicMock()
        mock_post_init_builder = MagicMock()

        # Setup the builder chain
        mock_app_builder.builder.return_value = mock_builder
        mock_builder.token.return_value = mock_token_builder
        mock_token_builder.post_init.return_value = mock_post_init_builder
        mock_post_init_builder.build.return_value = mock_app

        with patch('src.bot.app.create_conversation_handler') as mock_conv_handler, \
             patch('src.bot.error_handler.error_handler') as mock_error_handler:

            mock_conv_handler.return_value = MagicMock()

            main()

            # Should have registered error handler
            mock_app.add_error_handler.assert_called_once_with(mock_error_handler)
