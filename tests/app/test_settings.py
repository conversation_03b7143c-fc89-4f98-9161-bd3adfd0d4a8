"""
Settings Tests
Tests for settings command and filters functionality
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Async<PERSON>ock, MagicMock, patch

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.bot.commands import settings_command, handle_filters_callback


@pytest.fixture
def mock_update():
    """Mock Telegram Update object"""
    update = MagicMock()
    update.message.reply_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Mock Telegram Context object"""
    context = MagicMock()
    context.user_data = {
        "user_settings": {
            "low_cost_only": False,
            "phnom_penh_only": False
        }
    }
    return context


@pytest.fixture
def mock_callback_query():
    """Mock callback query"""
    query = MagicMock()
    query.answer = AsyncMock()
    query.edit_message_text = AsyncMock()
    return query


class TestSettingsCommand:
    """Test settings command functionality"""

    @pytest.mark.asyncio
    async def test_settings_command_basic(self, mock_update, mock_context):
        """Test basic settings command"""
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await settings_command(mock_update, mock_context)
            
            # Should reply with settings menu
            mock_update.message.reply_text.assert_called_once()
            call_kwargs = mock_update.message.reply_text.call_args[1]
            
            # Should have inline keyboard
            assert 'reply_markup' in call_kwargs
            reply_markup = call_kwargs['reply_markup']
            assert hasattr(reply_markup, 'inline_keyboard')
            
            # Should have language and filters buttons
            assert len(reply_markup.inline_keyboard) >= 2

    @pytest.mark.asyncio
    async def test_settings_command_shows_filter_status(self, mock_update, mock_context):
        """Test that settings command shows current filter status"""
        # Set some filters
        mock_context.user_data["user_settings"]["low_cost_only"] = True
        mock_context.user_data["user_settings"]["phnom_penh_only"] = False
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await settings_command(mock_update, mock_context)
            
            # Check that filter status is shown in response
            call_args = mock_update.message.reply_text.call_args[0][0]
            assert "translated_filter_enabled" in call_args
            assert "translated_filter_disabled" in call_args

    @pytest.mark.asyncio
    async def test_settings_command_no_existing_settings(self, mock_update):
        """Test settings command when no user settings exist"""
        context = MagicMock()
        context.user_data = {}  # No existing settings
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await settings_command(mock_update, context)
            
            # Should not crash and should show default (disabled) filter status
            mock_update.message.reply_text.assert_called_once()


class TestFiltersCallback:
    """Test filters callback functionality"""

    @pytest.mark.asyncio
    async def test_show_filters_callback(self, mock_context, mock_callback_query):
        """Test show filters submenu callback"""
        update = MagicMock()
        update.callback_query = mock_callback_query
        mock_callback_query.data = "show_filters"
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await handle_filters_callback(update, mock_context)
            
            # Should answer callback and edit message
            mock_callback_query.answer.assert_called_once()
            mock_callback_query.edit_message_text.assert_called_once()
            
            # Should have filter toggle buttons
            call_kwargs = mock_callback_query.edit_message_text.call_args[1]
            assert 'reply_markup' in call_kwargs
            reply_markup = call_kwargs['reply_markup']
            assert len(reply_markup.inline_keyboard) >= 3  # 2 filters + back button

    @pytest.mark.asyncio
    async def test_toggle_low_cost_filter(self, mock_context, mock_callback_query):
        """Test toggling low cost filter"""
        update = MagicMock()
        update.callback_query = mock_callback_query
        mock_callback_query.data = "toggle_low_cost"
        
        # Initial state: low_cost_only = False
        initial_value = mock_context.user_data["user_settings"]["low_cost_only"]
        assert initial_value is False
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await handle_filters_callback(update, mock_context)
            
            # Should toggle the value
            new_value = mock_context.user_data["user_settings"]["low_cost_only"]
            assert new_value is True
            
            # Should show confirmation message
            mock_callback_query.edit_message_text.assert_called_once()
            call_args = mock_callback_query.edit_message_text.call_args[1]
            assert "translated_filters_updated" in call_args['text']

    @pytest.mark.asyncio
    async def test_toggle_phnom_penh_filter(self, mock_context, mock_callback_query):
        """Test toggling Phnom Penh filter"""
        update = MagicMock()
        update.callback_query = mock_callback_query
        mock_callback_query.data = "toggle_phnom_penh"
        
        # Initial state: phnom_penh_only = False
        initial_value = mock_context.user_data["user_settings"]["phnom_penh_only"]
        assert initial_value is False
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await handle_filters_callback(update, mock_context)
            
            # Should toggle the value
            new_value = mock_context.user_data["user_settings"]["phnom_penh_only"]
            assert new_value is True
            
            # Should show confirmation message
            mock_callback_query.edit_message_text.assert_called_once()

    @pytest.mark.asyncio
    async def test_back_to_settings_callback(self, mock_context, mock_callback_query):
        """Test back to settings callback"""
        update = MagicMock()
        update.callback_query = mock_callback_query
        mock_callback_query.data = "back_to_settings"
        
        with patch('src.bot.commands.settings_command') as mock_settings_cmd:
            await handle_filters_callback(update, mock_context)
            
            # Should call settings_command
            mock_settings_cmd.assert_called_once_with(update, mock_context)

    @pytest.mark.asyncio
    async def test_filter_persistence(self, mock_callback_query):
        """Test that filter settings are persisted in user_data"""
        context = MagicMock()
        context.user_data = {}  # Start with empty user_data
        
        update = MagicMock()
        update.callback_query = mock_callback_query
        mock_callback_query.data = "toggle_low_cost"
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await handle_filters_callback(update, context)
            
            # Should create user_settings if it doesn't exist
            assert "user_settings" in context.user_data
            assert "low_cost_only" in context.user_data["user_settings"]
            assert context.user_data["user_settings"]["low_cost_only"] is True


class TestSettingsIntegration:
    """Test settings integration with other components"""

    @pytest.mark.asyncio
    async def test_settings_to_filters_flow(self, mock_context):
        """Test full flow from settings to filters"""
        # Step 1: Show settings
        update = MagicMock()
        update.message.reply_text = AsyncMock()
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await settings_command(update, mock_context)
            
            # Verify settings menu was shown
            update.message.reply_text.assert_called_once()
        
        # Step 2: Click filters button
        query = MagicMock()
        query.answer = AsyncMock()
        query.edit_message_text = AsyncMock()
        query.data = "show_filters"
        
        filters_update = MagicMock()
        filters_update.callback_query = query
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await handle_filters_callback(filters_update, mock_context)
            
            # Verify filters menu was shown
            query.edit_message_text.assert_called_once()

    @pytest.mark.asyncio
    async def test_filter_button_states(self, mock_context, mock_callback_query):
        """Test that filter buttons show correct enabled/disabled states"""
        # Set mixed filter states
        mock_context.user_data["user_settings"]["low_cost_only"] = True
        mock_context.user_data["user_settings"]["phnom_penh_only"] = False
        
        update = MagicMock()
        update.callback_query = mock_callback_query
        mock_callback_query.data = "show_filters"
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            await handle_filters_callback(update, mock_context)
            
            # Check that buttons show correct states
            call_kwargs = mock_callback_query.edit_message_text.call_args[1]
            reply_markup = call_kwargs['reply_markup']
            
            # Get button texts
            buttons = reply_markup.inline_keyboard
            low_cost_button = buttons[0][0]
            phnom_penh_button = buttons[1][0]
            
            # Low cost should show enabled (✅), Phnom Penh should show disabled (❌)
            assert "✅" in low_cost_button.text
            assert "❌" in phnom_penh_button.text

    @pytest.mark.asyncio
    async def test_multiple_filter_toggles(self, mock_callback_query):
        """Test multiple filter toggles in sequence"""
        context = MagicMock()
        context.user_data = {"user_settings": {"low_cost_only": False, "phnom_penh_only": False}}
        
        update = MagicMock()
        update.callback_query = mock_callback_query
        
        with patch('src.bot.commands.get_lang') as mock_get_lang, \
             patch('src.bot.commands.t') as mock_t:
            
            mock_get_lang.return_value = "en"
            mock_t.side_effect = lambda key, lang: f"translated_{key}"
            
            # Toggle low cost filter
            mock_callback_query.data = "toggle_low_cost"
            await handle_filters_callback(update, context)
            assert context.user_data["user_settings"]["low_cost_only"] is True
            
            # Toggle Phnom Penh filter
            mock_callback_query.data = "toggle_phnom_penh"
            await handle_filters_callback(update, context)
            assert context.user_data["user_settings"]["phnom_penh_only"] is True
            
            # Toggle low cost filter again (should turn off)
            mock_callback_query.data = "toggle_low_cost"
            await handle_filters_callback(update, context)
            assert context.user_data["user_settings"]["low_cost_only"] is False
            assert context.user_data["user_settings"]["phnom_penh_only"] is True  # Should remain on
