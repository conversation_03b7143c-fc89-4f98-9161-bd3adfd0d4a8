"""
Data Loader Coverage Tests
Tests to improve coverage for data_loader.py
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.data_loader import validate_program_data


def test_validate_program_data_non_dict():
    """Test validate_program_data with non-dict input"""
    assert validate_program_data("not a dict") == False
    assert validate_program_data(None) == False
    assert validate_program_data(123) == False
    assert validate_program_data([]) == False
