"""
ML Model Routing Tests
Tests for correct ML model version selection and fallback behavior
"""

import pytest
import os
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.hybrid_recommender import get_recommendations


@pytest.fixture
def sample_user_answers():
    """Sample user answers for testing"""
    return {
        'location_preference': 'pp',
        'budget_range': 'mid',
        'field_of_interest': 'stem',
        'career_aspiration': 'engineer'
    }


@pytest.fixture
def sample_programmes():
    """Sample programmes for testing"""
    return [
        {
            'major_id': 'cs_001',
            'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
            'university_name_kh': 'សាកលវិទ្យាល័យភ្នំពេញ',
            'city': 'ភ្នំពេញ',
            'tuition_fees_usd': 1000,
            'field_tag': 'STEM',
            'tuition_bracket': 'Medium'
        },
        {
            'major_id': 'bus_001',
            'major_name_kh': 'អាជីវកម្ម',
            'university_name_kh': 'សាកលវិទ្យាល័យសៀមរាប',
            'city': 'សៀមរាប',
            'tuition_fees_usd': 800,
            'field_tag': 'Business',
            'tuition_bracket': 'Low'
        }
    ]


@patch('src.core.hybrid_recommender.Path')
@patch('src.core.hybrid_recommender.MLReranker')
def test_ml_version_1_routing(mock_ml_reranker, mock_path, sample_user_answers, sample_programmes):
    """Test ML_VERSION=1 routes to correct model"""
    # Mock file existence
    mock_path_instance = MagicMock()
    mock_path.return_value = mock_path_instance
    mock_path_instance.exists.return_value = True
    
    # Mock MLReranker
    mock_reranker_instance = MagicMock()
    mock_ml_reranker.return_value = mock_reranker_instance
    mock_reranker_instance.rank.return_value = sample_programmes
    
    with patch.dict(os.environ, {'ML_VERSION': '1'}):
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)
        
        # Verify correct model path was used
        mock_ml_reranker.assert_called_once()
        call_args = mock_ml_reranker.call_args[0]
        assert call_args[0] == "models/rf_ranker.joblib", "Should use v1 model for ML_VERSION=1"


@patch('src.core.hybrid_recommender.Path')
@patch('src.core.hybrid_recommender.MLReranker')
def test_ml_version_2_routing(mock_ml_reranker, mock_path, sample_user_answers, sample_programmes):
    """Test ML_VERSION=2 routes to correct model"""
    # Mock file existence
    mock_path_instance = MagicMock()
    mock_path.return_value = mock_path_instance
    mock_path_instance.exists.return_value = True
    
    # Mock MLReranker
    mock_reranker_instance = MagicMock()
    mock_ml_reranker.return_value = mock_reranker_instance
    mock_reranker_instance.rank.return_value = sample_programmes
    
    with patch.dict(os.environ, {'ML_VERSION': '2'}):
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)

        # Verify correct model path was used
        mock_ml_reranker.assert_called_once()
        call_args = mock_ml_reranker.call_args[0]
        assert call_args[0] == "models/rf_ranker_v2.joblib", "Should use v2 model for ML_VERSION=2"


@patch('src.core.hybrid_recommender.Path')
@patch('src.core.hybrid_recommender.MLReranker')
def test_ml_version_3_routing(mock_ml_reranker, mock_path, sample_user_answers, sample_programmes):
    """Test ML_VERSION=3 routes to correct model"""
    # Mock file existence for v3 model
    def mock_exists(path_str):
        return str(path_str) == "models/rf_ranker_v3.joblib"

    mock_path_instance = MagicMock()
    mock_path.return_value = mock_path_instance
    mock_path_instance.exists.side_effect = lambda: mock_exists(mock_path.call_args[0][0])

    # Mock MLReranker
    mock_reranker_instance = MagicMock()
    mock_ml_reranker.return_value = mock_reranker_instance
    mock_reranker_instance.rank.return_value = sample_programmes

    with patch.dict(os.environ, {'ML_VERSION': '3'}):
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)
        
        # Verify correct model path was used
        mock_ml_reranker.assert_called_once()
        call_args = mock_ml_reranker.call_args[0]
        assert call_args[0] == "models/rf_ranker_v3.joblib", "Should use v3 model for ML_VERSION=3"


@patch('src.core.hybrid_recommender.Path')
@patch('src.core.hybrid_recommender.MLReranker')
def test_fallback_chain_3_to_2(mock_ml_reranker, mock_path, sample_user_answers, sample_programmes):
    """Test fallback from v3 to v2 when v3 doesn't exist"""
    # Mock file existence: v3 doesn't exist, v2 exists
    def mock_exists(path_str):
        return str(path_str) == "models/rf_ranker_v2.joblib"
    
    mock_path_instance = MagicMock()
    mock_path.return_value = mock_path_instance
    mock_path_instance.exists.side_effect = lambda: mock_exists(mock_path.call_args[0][0])
    
    # Mock MLReranker
    mock_reranker_instance = MagicMock()
    mock_ml_reranker.return_value = mock_reranker_instance
    mock_reranker_instance.rank.return_value = sample_programmes
    
    with patch.dict(os.environ, {'ML_VERSION': '3'}):
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)

        # Verify fallback to v2 model
        mock_ml_reranker.assert_called_once()
        call_args = mock_ml_reranker.call_args[0]
        assert call_args[0] == "models/rf_ranker_v2.joblib", "Should fallback to v2 when v3 doesn't exist"


@patch('src.core.hybrid_recommender.Path')
@patch('src.core.hybrid_recommender.MLReranker')
def test_fallback_chain_3_to_1(mock_ml_reranker, mock_path, sample_user_answers, sample_programmes):
    """Test fallback from v3 to v1 when v3 and v2 don't exist"""
    # Mock file existence: only v1 exists
    def mock_exists(path_str):
        return str(path_str) == "models/rf_ranker.joblib"

    mock_path_instance = MagicMock()
    mock_path.return_value = mock_path_instance
    mock_path_instance.exists.side_effect = lambda: mock_exists(mock_path.call_args[0][0])

    # Mock MLReranker
    mock_reranker_instance = MagicMock()
    mock_ml_reranker.return_value = mock_reranker_instance
    mock_reranker_instance.rank.return_value = sample_programmes

    with patch.dict(os.environ, {'ML_VERSION': '3'}):
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)
        
        # Verify fallback to v1 model
        mock_ml_reranker.assert_called_once()
        call_args = mock_ml_reranker.call_args[0]
        assert call_args[0] == "models/rf_ranker.joblib", "Should fallback to v1 when v3 and v2 don't exist"


@patch('src.core.hybrid_recommender.Path')
def test_ultimate_fallback(mock_path, sample_user_answers, sample_programmes):
    """Test ultimate fallback when no models exist"""
    # Mock file existence: no models exist
    mock_path.return_value.exists.return_value = False

    with patch.dict(os.environ, {'ML_VERSION': '3'}):
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)

        # Should return results with neutral ML scores (0.5) when no models exist
        assert isinstance(result, list)
        assert len(result) > 0

        # All ML scores should be 0.5 (neutral fallback)
        for rec in result:
            assert rec['ml_score'] == 0.5, "Should use neutral ML score when no models exist"


def test_default_ml_version(sample_user_answers, sample_programmes):
    """Test default ML_VERSION when environment variable not set"""
    # Remove ML_VERSION from environment if it exists
    env_backup = os.environ.get('ML_VERSION')
    if 'ML_VERSION' in os.environ:
        del os.environ['ML_VERSION']

    try:
        # Test that default version (3) is used when no env var is set
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)

        # Should return valid results with both MCDA and ML scores
        assert isinstance(result, list)
        assert len(result) > 0

        for rec in result:
            assert 'mcda_score' in rec
            assert 'ml_score' in rec
            assert 'hybrid_score' in rec

    finally:
        # Restore environment
        if env_backup is not None:
            os.environ['ML_VERSION'] = env_backup


def test_invalid_ml_version():
    """Test handling of invalid ML_VERSION values"""
    with patch.dict(os.environ, {'ML_VERSION': '999'}):
        # Invalid ML version should fallback gracefully to default (3)
        result = get_recommendations({}, programmes=[])
        # Should return empty list for empty programmes, not raise exception
        assert result == []


@pytest.mark.parametrize("ml_version", ["1", "2", "3"])
def test_parametrized_ml_routing(ml_version, sample_user_answers, sample_programmes):
    """Parametrized test for ML version routing"""
    with patch.dict(os.environ, {'ML_VERSION': ml_version}):
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)

        # Should return valid results regardless of ML version
        assert isinstance(result, list)
        assert len(result) > 0

        # All results should have valid scores
        for rec in result:
            assert 'mcda_score' in rec
            assert 'ml_score' in rec
            assert 'hybrid_score' in rec
            assert 0.0 <= rec['mcda_score'] <= 1.0
            assert 0.0 <= rec['ml_score'] <= 1.0
            assert 0.0 <= rec['hybrid_score'] <= 1.0


def test_ml_reranker_failure_fallback(sample_user_answers, sample_programmes):
    """Test fallback to neutral ML scores when ML reranker fails"""
    with patch('src.core.hybrid_recommender.MLReranker') as mock_ml_reranker:
        # Make MLReranker raise an exception
        mock_ml_reranker.side_effect = Exception("Model loading failed")

        # Should not raise exception, should fallback gracefully
        result = get_recommendations(sample_user_answers, programmes=sample_programmes)

        # Should return some results with neutral ML scores
        assert isinstance(result, list), "Should return list even when ML fails"
        assert len(result) > 0, "Should return at least some results"

        # ML scores should be neutral (0.5) when ML fails
        for rec in result:
            assert rec['ml_score'] == 0.5, "Should use neutral ML score when ML fails"
