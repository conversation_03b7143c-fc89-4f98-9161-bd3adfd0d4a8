"""
MCDA NumPy Return Type Tests
Tests to ensure MCDA functions return numpy arrays
"""

import sys
from pathlib import Path
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.mcda import score_vectorized


def test_score_vectorized_returns_numpy():
    """Test that score_vectorized returns numpy array"""
    scores = score_vectorized({}, [])
    assert isinstance(scores, np.ndarray)
    assert scores.size == 0


def test_score_vectorized_returns_numpy_with_data():
    """Test that score_vectorized returns numpy array with data"""
    user_answers = {"location_preference": "pp"}
    programmes = [{"major_id": "test", "city": "ភ្នំពេញ"}]
    
    scores = score_vectorized(user_answers, programmes)
    assert isinstance(scores, np.ndarray)
    assert scores.size == 1
    assert scores.dtype == float
