"""
Cold-Start Tests
Tests for handling missing/partial data gracefully
"""

import pytest
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.mcda import score
from src.core.ml_reranker import <PERSON><PERSON><PERSON><PERSON>
from src.core.feature_schema import extract_features


def test_mcda_score_missing_fields():
    """Test that MCDA scoring handles missing mandatory fields gracefully"""
    # Programme with missing critical fields
    incomplete_programme = {
        "major_name_kh": "កម្មវិធីសាកល្បង",
        # Missing: tuition_fees_usd, employment_rate, city, field_tag, etc.
    }
    
    user_answers = {
        "location_preference": "pp",
        "budget_range": "mid",
        "field_of_interest": "stem"
    }
    
    # Should not crash and return a float
    result = score(user_answers, incomplete_programme)
    
    assert isinstance(result, float), "MCDA score should return float even with missing fields"
    assert 0.0 <= result <= 1.0, "MCDA score should be between 0 and 1"


def test_mcda_score_empty_programme():
    """Test MCDA scoring with completely empty programme"""
    empty_programme = {}
    
    user_answers = {
        "location_preference": "pp",
        "budget_range": "mid",
        "field_of_interest": "stem"
    }
    
    # Should not crash
    result = score(user_answers, empty_programme)
    
    assert isinstance(result, float), "MCDA score should handle empty programme"
    assert 0.0 <= result <= 1.0, "Score should be valid range"


def test_mcda_score_missing_user_answers():
    """Test MCDA scoring with missing user answers"""
    programme = {
        "city": "ភ្នំពេញ",
        "tuition_fees_usd": "$1500",
        "field_tag": "STEM"
    }
    
    # Empty user answers
    empty_answers = {}
    
    # Should not crash
    result = score(empty_answers, programme)
    
    assert isinstance(result, float), "MCDA score should handle missing user answers"
    assert 0.0 <= result <= 1.0, "Score should be valid range"


def test_feature_extraction_missing_fields():
    """Test that feature extraction handles missing fields gracefully"""
    # Programme with missing critical fields
    incomplete_programme = {
        "major_name_kh": "កម្មវិធីសាកល្បង",
        # Missing: mcda_score, tuition_fees_usd, employment_rate, etc.
    }
    
    user_answers = {
        "location_preference": "pp",
        "field_of_interest": "stem"
    }
    
    # Should not crash and return correct number of features
    features = extract_features(user_answers, incomplete_programme)
    
    from src.core.feature_schema import FEATURES
    assert len(features) == len(FEATURES), f"Should return {len(FEATURES)} features"
    assert all(isinstance(f, (int, float)) for f in features), "All features should be numeric"
    assert all(not (f != f) for f in features), "No features should be NaN"  # NaN check


def test_feature_extraction_null_values():
    """Test feature extraction with null/None values"""
    programme_with_nulls = {
        "mcda_score": None,
        "tuition_fees_usd": None,
        "employment_rate": None,
        "field_tag": None,
        "has_scholarship": None,
        "language_of_instruction": None
    }
    
    user_answers = {"location_preference": "pp"}
    
    # Should handle nulls gracefully
    features = extract_features(user_answers, programme_with_nulls)
    
    from src.core.feature_schema import FEATURES
    assert len(features) == len(FEATURES), "Should return correct number of features"
    assert all(isinstance(f, (int, float)) for f in features), "All features should be numeric"


def test_ml_reranker_missing_fields():
    """Test that ML reranker handles missing fields gracefully"""
    # Skip if no trained model available
    model_path = "models/rf_ranker_v3.joblib"
    if not Path(model_path).exists():
        model_path = "models/rf_ranker.joblib"
    
    if not Path(model_path).exists():
        pytest.skip("No trained model available")
    
    reranker = MLReranker(model_path)
    
    # Programmes with missing fields
    incomplete_programmes = [
        {
            "major_id": "test_001",
            "major_name_kh": "កម្មវិធី១",
            # Missing most fields
        },
        {
            "major_id": "test_002", 
            "major_name_kh": "កម្មវិធី២",
            "tuition_fees_usd": None,
            "employment_rate": "",
            "field_tag": None
        }
    ]
    
    user_answers = {
        "location_preference": "pp",
        "field_of_interest": "stem"
    }
    
    # Should not crash and return results
    try:
        ranked_programmes = reranker.rank(user_answers, incomplete_programmes, top_k=2)
        
        assert len(ranked_programmes) <= 2, "Should return requested number of programmes"
        assert all('ml_score' in prog for prog in ranked_programmes), "All programmes should have ML scores"
        assert all(isinstance(prog['ml_score'], (int, float)) for prog in ranked_programmes), "ML scores should be numeric"
        
    except Exception as e:
        # If ML ranking fails, should fallback gracefully
        assert "fallback" in str(e).lower() or "error" in str(e).lower(), f"Should handle errors gracefully: {e}"


def test_cold_start_with_new_programme():
    """Test complete cold-start scenario with brand new programme"""
    # Simulate a completely new programme with minimal data
    new_programme = {
        "major_id": "new_prog_001",
        "major_name_kh": "កម្មវិធីថ្មី",
        "university_name_kh": "សាកលវិទ្យាល័យថ្មី"
        # Missing all other fields
    }
    
    user_answers = {
        "location_preference": "pp",
        "budget_range": "mid",
        "field_of_interest": "stem"
    }
    
    # Test MCDA scoring
    mcda_result = score(user_answers, new_programme)
    assert isinstance(mcda_result, float), "MCDA should handle new programme"
    assert 0.0 <= mcda_result <= 1.0, "MCDA score should be valid"
    
    # Test feature extraction
    features = extract_features(user_answers, new_programme)
    from src.core.feature_schema import FEATURES
    assert len(features) == len(FEATURES), "Should extract all features"
    assert all(isinstance(f, (int, float)) for f in features), "Features should be numeric"


def test_weights_loading_failure():
    """Test MCDA behavior when weights file cannot be loaded"""
    # This test verifies the fallback behavior in load_weights()
    from src.core.mcda import load_weights
    
    # Should not crash even if weights file is missing/corrupt
    try:
        weights = load_weights()
        assert isinstance(weights, dict), "Should return dict even on failure"
        assert 'question_weights' in weights, "Should have question_weights section"
    except Exception as e:
        pytest.fail(f"load_weights should not crash: {e}")


def test_feature_schema_medians():
    """Test that feature schema has proper median values"""
    from src.core.feature_schema import DATASET_MEDIANS
    
    assert isinstance(DATASET_MEDIANS, dict), "DATASET_MEDIANS should be a dict"
    assert 'employment_rate' in DATASET_MEDIANS, "Should have employment_rate median"
    assert 'tuition_usd' in DATASET_MEDIANS, "Should have tuition_usd median"
    assert 'mcda_score' in DATASET_MEDIANS, "Should have mcda_score median"
    
    # Check reasonable values
    assert 0.0 <= DATASET_MEDIANS['employment_rate'] <= 1.0, "Employment rate should be 0-1"
    assert DATASET_MEDIANS['tuition_usd'] > 0, "Tuition should be positive"
    assert 0.0 <= DATASET_MEDIANS['mcda_score'] <= 1.0, "MCDA score should be 0-1"
