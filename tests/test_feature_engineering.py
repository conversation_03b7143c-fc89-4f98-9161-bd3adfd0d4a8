"""
Feature Engineering Tests
Tests for feature engineering functionality
"""

import pytest
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.feature_engineering import add_derived_features, categorize_field, calculate_tuition_bracket


class TestFeatureEngineering:
    """Test cases for feature engineering module"""
    
    def test_add_derived_features_basic(self):
        """Test basic feature engineering functionality"""
        programs = [
            {
                'major_name_en': 'Computer Science',
                'faculty': {'name_en': 'Faculty of Engineering'},
                'tuition_fees_usd': '1000',
                'scholarship_availability': True
            }
        ]
        
        result = add_derived_features(programs)
        
        assert len(result) == 1
        assert 'field_tag' in result[0]
        assert 'tuition_bracket' in result[0]
        assert 'has_scholarship' in result[0]
        assert result[0]['field_tag'] == 'STEM'
        assert result[0]['tuition_bracket'] == 'Medium'
        assert result[0]['has_scholarship'] == True
    
    def test_add_derived_features_invalid_tuition(self):
        """Test feature engineering with invalid tuition values"""
        programs = [
            {
                'major_name_en': 'Business Administration',
                'tuition_fees_usd': 'invalid_amount',  # Invalid value
                'scholarship_availability': False
            },
            {
                'major_name_en': 'Medicine',
                'tuition_fees_usd': None,  # None value
                'scholarship_availability': True
            }
        ]
        
        result = add_derived_features(programs)
        
        assert len(result) == 2
        # Both should handle invalid tuition gracefully
        assert result[0]['tuition_bracket'] == 'Unknown'  # Invalid string
        assert result[1]['tuition_bracket'] == 'Unknown'  # None value
    
    def test_categorize_field_stem(self):
        """Test STEM field categorization"""
        assert categorize_field('Computer Science') == 'STEM'
        assert categorize_field('Engineering') == 'STEM'
        assert categorize_field('Information Technology') == 'STEM'
        assert categorize_field('Data Science') == 'STEM'
    
    def test_categorize_field_business(self):
        """Test Business field categorization"""
        assert categorize_field('Business Administration') == 'Business'
        assert categorize_field('Finance') == 'Business'
        assert categorize_field('Marketing') == 'Business'
        assert categorize_field('Economics') == 'Business'
    
    def test_categorize_field_health(self):
        """Test Health field categorization"""
        assert categorize_field('Medicine') == 'Health'
        assert categorize_field('Nursing') == 'Health'
        assert categorize_field('Public Health') == 'Health'
    
    def test_categorize_field_education(self):
        """Test Education field categorization"""
        assert categorize_field('Education') == 'Education'
        assert categorize_field('Teaching') == 'Education'
    
    def test_categorize_field_social_sciences(self):
        """Test Social Sciences field categorization"""
        assert categorize_field('Psychology') == 'Social Sciences'
        assert categorize_field('International Relations') == 'Social Sciences'
        assert categorize_field('Law') == 'Social Sciences'
    
    def test_categorize_field_arts(self):
        """Test Arts & Humanities field categorization"""
        assert categorize_field('Art') == 'Arts & Humanities'
        assert categorize_field('Music') == 'Arts & Humanities'
        assert categorize_field('Philosophy') == 'Arts & Humanities'
    
    def test_categorize_field_other(self):
        """Test Other field categorization"""
        assert categorize_field('Unknown Field') == 'Other'
        assert categorize_field('') == 'Other'
        assert categorize_field(None) == 'Other'
    
    def test_categorize_field_with_faculty(self):
        """Test field categorization with faculty context"""
        assert categorize_field('General Studies', 'Faculty of Engineering') == 'STEM'
        assert categorize_field('General Studies', 'Faculty of Business') == 'Business'
    
    def test_calculate_tuition_bracket(self):
        """Test tuition bracket calculation"""
        assert calculate_tuition_bracket(0) == 'Unknown'
        assert calculate_tuition_bracket(-100) == 'Unknown'
        assert calculate_tuition_bracket(300) == 'Low'
        assert calculate_tuition_bracket(1000) == 'Medium'
        assert calculate_tuition_bracket(2000) == 'High'


if __name__ == '__main__':
    pytest.main([__file__])
