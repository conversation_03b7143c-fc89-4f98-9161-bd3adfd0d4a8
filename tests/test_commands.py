"""
Command Tests
Tests for new user commands functionality
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.bot.commands import (filter_language_command, campus_map_command, 
                             study_plan_command, mental_health_command, 
                             settings_command)


class MockUpdate:
    """Mock Telegram Update object"""
    def __init__(self, message_text: str):
        self.message = Mock()
        self.message.text = message_text
        self.message.reply_text = AsyncMock()


class MockContext:
    """Mock Telegram Context object"""
    def __init__(self, user_data: dict = None):
        self.user_data = user_data or {}


def test_filter_language_invalid_profile():
    """Test filter-language command with no user profile"""
    
    async def run_test():
        update = MockUpdate("/filter-language kh")
        context = MockContext()  # No user_data['answers']
        
        await filter_language_command(update, context)
        
        # Check that error message was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args[0][0]
        assert "run /start" in call_args.lower() or "/start" in call_args
    
    asyncio.run(run_test())


def test_filter_language_invalid_args():
    """Test filter-language command with invalid arguments"""
    
    async def run_test():
        update = MockUpdate("/filter-language")  # No language specified
        context = MockContext({'answers': {'location_preference': 'pp'}})
        
        await filter_language_command(update, context)
        
        # Check that error message was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args[0][0]
        assert "សូមបញ្ជាក់ភាសា" in call_args
    
    asyncio.run(run_test())


def test_campus_map_missing_args():
    """Test campus-map command with missing program ID"""
    
    async def run_test():
        update = MockUpdate("/campus-map")  # No program ID
        context = MockContext()
        
        await campus_map_command(update, context)
        
        # Check that error message was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args[0][0]
        assert "program_id" in call_args or "លេខកម្មវិធី" in call_args
    
    asyncio.run(run_test())


def test_study_plan_table():
    """Test study-plan command generates markdown table"""
    
    async def run_test():
        # Use a real program ID from loaded data
        from src.core.data_loader import load_raw
        programs = load_raw()
        
        if not programs:
            pytest.skip("No program data available")
        
        # Get first program ID
        program_id = programs[0].get('major_id', 'test_id')
        
        update = MockUpdate(f"/study-plan {program_id}")
        context = MockContext()
        
        await study_plan_command(update, context)
        
        # Check that response was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args[0][0]
        
        # Check for markdown table structure and Khmer headers
        assert "|" in call_args  # Markdown table
        assert "ឆ្នាំ" in call_args  # Khmer header for "Year"
        assert "ក្រេឌីត" in call_args  # Khmer header for "Credits"
    
    asyncio.run(run_test())


def test_study_plan_invalid_id():
    """Test study-plan command with invalid program ID"""
    
    async def run_test():
        update = MockUpdate("/study-plan invalid_id_12345")
        context = MockContext()
        
        await study_plan_command(update, context)
        
        # Check that error message was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args[0][0]
        assert "រកមិនឃើញ" in call_args  # "Not found" in Khmer
    
    asyncio.run(run_test())


def test_mental_health_static_content():
    """Test mental-health command returns static content"""
    
    async def run_test():
        update = MockUpdate("/mental-health")
        context = MockContext()
        
        await mental_health_command(update, context)
        
        # Check that response was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args[0][0]
        
        # Check for expected content
        assert "TPO" in call_args
        assert "📞" in call_args  # Phone emoji
        assert "សុខភាពផ្លូវចិត្ត" in call_args  # Mental health in Khmer
        assert "023" in call_args  # Phone number format
    
    asyncio.run(run_test())


def test_settings_keyboard():
    """Test settings command returns inline keyboard"""
    
    async def run_test():
        update = MockUpdate("/settings")
        context = MockContext()
        
        await settings_command(update, context)
        
        # Check that response was sent with reply_markup
        update.message.reply_text.assert_called_once()
        call_kwargs = update.message.reply_text.call_args[1]
        
        # Check that reply_markup was provided (inline keyboard)
        assert 'reply_markup' in call_kwargs
        assert call_kwargs['reply_markup'] is not None
        
        # Check message content
        call_args = update.message.reply_text.call_args[0][0]
        assert "ការកំណត់" in call_args  # "Settings" in Khmer
    
    asyncio.run(run_test())


def test_campus_map_placeholder_api():
    """Test campus-map uses placeholder API key"""
    
    async def run_test():
        # Use a real program ID
        from src.core.data_loader import load_raw
        programs = load_raw()
        
        if not programs:
            pytest.skip("No program data available")
        
        program_id = programs[0].get('major_id', 'test_id')
        
        update = MockUpdate(f"/campus-map {program_id}")
        context = MockContext()
        
        await campus_map_command(update, context)
        
        # Check that response contains placeholder API key
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args[0][0]
        
        assert "YOUR_API_KEY" in call_args
        assert "maps.googleapis.com" in call_args
        assert "staticmap" in call_args
    
    asyncio.run(run_test())


def test_filter_language_valid_request():
    """Test filter-language with valid user profile and language"""
    
    async def run_test():
        # Mock user answers
        user_answers = {
            'location_preference': 'pp',
            'budget_range': 'mid',
            'field_of_interest': 'stem'
        }
        
        update = MockUpdate("/filter-language kh")
        context = MockContext({'answers': user_answers})
        
        await filter_language_command(update, context)
        
        # Should not raise error and should send some response
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args[0][0]
        
        # Should either show results or "no programs found" message
        assert len(call_args) > 0
    
    asyncio.run(run_test())
