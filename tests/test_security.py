"""
Security Tests
Tests for input validation, sanitization, and security measures
"""

import pytest
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.bot.validation import (
    sanitize_text, 
    validate_program_id, 
    validate_language_code,
    sanitize_command_args,
    validate_user_input
)


def test_sanitize_text_basic():
    """Test basic text sanitization"""
    # Test markdown special characters
    result = sanitize_text('*_hello_*')
    assert result == '\\*\\_hello\\_\\*', "Should escape markdown special characters"
    
    # Test square brackets
    result = sanitize_text('[link](url)')
    assert result == '\\[link\\]\\(url\\)', "Should escape square brackets and parentheses"
    
    # Test backticks and tildes
    result = sanitize_text('`code` ~strikethrough~')
    assert result == '\\`code\\` \\~strikethrough\\~', "Should escape backticks and tildes"


def test_sanitize_text_edge_cases():
    """Test edge cases for text sanitization"""
    # Empty string
    assert sanitize_text('') == '', "Empty string should remain empty"
    
    # None input
    assert sanitize_text(None) == 'None', "None should be converted to string"
    
    # Number input
    assert sanitize_text(123) == '123', "Numbers should be converted to string"
    
    # String with no special characters
    assert sanitize_text('hello world') == 'hello world', "Normal text should be unchanged"


def test_validate_program_id_valid():
    """Test valid program IDs"""
    # Valid alphanumeric
    assert validate_program_id('program123'), "Alphanumeric should be valid"
    
    # Valid with underscores and hyphens
    assert validate_program_id('program_123-abc'), "Underscores and hyphens should be valid"
    
    # Minimum length (3 characters)
    assert validate_program_id('abc'), "3 characters should be valid"
    
    # Maximum length (60 characters)
    long_id = 'a' * 60
    assert validate_program_id(long_id), "60 characters should be valid"


def test_validate_program_id_invalid():
    """Test invalid program IDs that should be rejected"""
    # Path traversal attempts
    assert not validate_program_id('../../etc/passwd'), "Path traversal should be rejected"
    assert not validate_program_id('../config'), "Relative paths should be rejected"
    assert not validate_program_id('/etc/passwd'), "Absolute paths should be rejected"
    
    # Special characters
    assert not validate_program_id('program;rm -rf'), "Semicolons should be rejected"
    assert not validate_program_id('program|cat'), "Pipes should be rejected"
    assert not validate_program_id('program&echo'), "Ampersands should be rejected"
    assert not validate_program_id('program$(whoami)'), "Command substitution should be rejected"
    
    # Too short
    assert not validate_program_id('ab'), "2 characters should be rejected"
    
    # Too long
    long_id = 'a' * 61
    assert not validate_program_id(long_id), "61 characters should be rejected"
    
    # Empty string
    assert not validate_program_id(''), "Empty string should be rejected"
    
    # Non-string input
    assert not validate_program_id(None), "None should be rejected"
    assert not validate_program_id(123), "Numbers should be rejected"


def test_validate_language_code():
    """Test language code validation"""
    # Valid codes
    assert validate_language_code('kh'), "Khmer should be valid"
    assert validate_language_code('en'), "English should be valid"
    assert validate_language_code('both'), "Both should be valid"
    
    # Case insensitive
    assert validate_language_code('KH'), "Uppercase should be valid"
    assert validate_language_code('En'), "Mixed case should be valid"
    
    # Invalid codes
    assert not validate_language_code('fr'), "French should be invalid"
    assert not validate_language_code('invalid'), "Random string should be invalid"
    assert not validate_language_code(''), "Empty string should be invalid"
    assert not validate_language_code(None), "None should be invalid"


def test_sanitize_command_args():
    """Test command argument sanitization"""
    # Normal command
    result = sanitize_command_args('/filter-language kh')
    assert result == ['/filter-language', 'kh'], "Normal command should be parsed correctly"
    
    # Command with extra spaces
    result = sanitize_command_args('  /command   arg1   arg2  ')
    assert result == ['/command', 'arg1', 'arg2'], "Extra spaces should be handled"
    
    # Command with control characters
    result = sanitize_command_args('/command\x00arg\x01')
    assert result == ['/command', 'arg'], "Control characters should be removed"
    
    # Too many arguments (should be limited)
    many_args = '/cmd ' + ' '.join([f'arg{i}' for i in range(20)])
    result = sanitize_command_args(many_args)
    assert len(result) <= 10, "Should limit to maximum 10 arguments"
    
    # Non-string input
    result = sanitize_command_args(None)
    assert result == [], "None should return empty list"


def test_validate_user_input():
    """Test general user input validation"""
    # Valid input
    result = validate_user_input('Hello world')
    assert result == 'Hello world', "Normal text should be valid"
    
    # Input with newlines and tabs (should be preserved)
    result = validate_user_input('Line 1\nLine 2\tTabbed')
    assert result == 'Line 1\nLine 2\tTabbed', "Newlines and tabs should be preserved"
    
    # Input with control characters (should be removed)
    result = validate_user_input('Hello\x00\x01world')
    assert result == 'Helloworld', "Control characters should be removed"
    
    # Too long input
    long_text = 'a' * 1001
    result = validate_user_input(long_text)
    assert result is None, "Too long input should be rejected"
    
    # Custom max length
    result = validate_user_input('hello', max_length=3)
    assert result is None, "Input exceeding custom max length should be rejected"
    
    # Empty input
    result = validate_user_input('')
    assert result is None, "Empty input should be rejected"
    
    # Whitespace only
    result = validate_user_input('   ')
    assert result is None, "Whitespace-only input should be rejected"
    
    # Non-string input
    result = validate_user_input(None)
    assert result is None, "None should be rejected"


def test_injection_attempts():
    """Test various injection attack attempts"""
    # SQL injection patterns
    sql_injections = [
        "'; DROP TABLE users; --",
        "1' OR '1'='1",
        "admin'--",
        "' UNION SELECT * FROM passwords --"
    ]
    
    for injection in sql_injections:
        assert not validate_program_id(injection), f"SQL injection should be rejected: {injection}"
    
    # Command injection patterns
    cmd_injections = [
        "; rm -rf /",
        "| cat /etc/passwd",
        "&& whoami",
        "`id`",
        "$(ls -la)"
    ]
    
    for injection in cmd_injections:
        assert not validate_program_id(injection), f"Command injection should be rejected: {injection}"
    
    # XSS patterns (should be sanitized)
    xss_patterns = [
        "<script>alert('xss')</script>",
        "javascript:alert('xss')",
        "<img src=x onerror=alert('xss')>"
    ]
    
    for xss in xss_patterns:
        sanitized = sanitize_text(xss)
        assert '<' not in sanitized or '\\<' in sanitized, f"XSS should be escaped: {xss}"


def test_security_edge_cases():
    """Test security edge cases and boundary conditions"""
    # Unicode characters
    unicode_text = "Hello 🌍 世界"
    result = sanitize_text(unicode_text)
    assert len(result) > 0, "Unicode should be handled gracefully"
    
    # Very long strings
    very_long = 'a' * 10000
    result = validate_user_input(very_long, max_length=5000)
    assert result is None, "Very long strings should be rejected"
    
    # Binary data
    binary_data = b'\x00\x01\x02\x03'
    result = validate_program_id(binary_data.decode('latin1', errors='ignore'))
    assert not result, "Binary data should be rejected"
    
    # Null bytes
    null_string = "hello\x00world"
    result = validate_user_input(null_string)
    assert '\x00' not in result, "Null bytes should be removed"


def test_markdown_injection():
    """Test protection against markdown injection attacks"""
    # Markdown link injection
    markdown_link = "[Click here](javascript:alert('xss'))"
    sanitized = sanitize_text(markdown_link)
    assert '\\[' in sanitized and '\\]' in sanitized, "Markdown links should be escaped"
    
    # Markdown image injection
    markdown_img = "![alt](http://evil.com/steal.jpg)"
    sanitized = sanitize_text(markdown_img)
    assert '\\!' in sanitized and '\\[' in sanitized, "Markdown images should be escaped"
    
    # Bold/italic injection
    bold_italic = "**bold** *italic* __underline__"
    sanitized = sanitize_text(bold_italic)
    assert '\\*' in sanitized and '\\_' in sanitized, "Bold/italic markers should be escaped"
