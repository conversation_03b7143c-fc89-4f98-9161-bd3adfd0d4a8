"""
Metrics Coverage Tests
Tests to improve coverage for metrics.py
"""

import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
import json
import tempfile

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.infra.metrics import (
    log_event, track_latency, count_calls,
    format_prometheus_metrics, main
)


def test_log_event_basic():
    """Test log_event basic functionality"""
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
        with patch("src.infra.metrics.METRICS_FILE", Path(f.name)):
            log_event("test_event", {"value": 1.0, "tags": {"env": "test"}})

            # Read back the metric
            f.seek(0)
            line = f.readline().strip()
            metric = json.loads(line)

            assert metric["event"] == "test_event"
            assert metric["data"]["value"] == 1.0
            assert metric["data"]["tags"]["env"] == "test"


def test_track_latency_sync():
    """Test track_latency decorator with sync function"""
    @track_latency("test_timer")
    def test_func():
        return "result"

    with patch("src.infra.metrics.log_event") as mock_log:
        result = test_func()
        assert result == "result"
        mock_log.assert_called_once()


def test_count_calls_decorator():
    """Test count_calls decorator"""
    @count_calls("test_counter")
    def test_func():
        return "result"
    
    with patch("src.infra.metrics.log_event") as mock_log:
        result = test_func()
        assert result == "result"
        mock_log.assert_called_once()


def test_format_prometheus_metrics_empty():
    """Test format_prometheus_metrics with no metrics"""
    with patch("src.infra.metrics.read_metrics", return_value=[]):
        output = format_prometheus_metrics()
        assert output == ""


def test_main_flask_available():
    """Test main function when Flask is available"""
    mock_flask = MagicMock()
    mock_response = MagicMock()
    
    with patch("src.infra.metrics._lazy_import_flask", return_value=(mock_flask, mock_response)):
        with patch("sys.exit") as mock_exit:
            # Should not exit when Flask is available
            try:
                main()
            except:
                pass  # Expected since we're mocking Flask
            
            mock_exit.assert_not_called()
