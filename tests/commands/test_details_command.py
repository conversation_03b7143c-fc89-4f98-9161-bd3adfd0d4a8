"""
Details Command Tests
Tests for /details_<program_id> command using new vectorized scoring
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.bot.commands import details_command


@pytest.fixture
def mock_update():
    """Mock Telegram Update object"""
    update = MagicMock()
    update.message.text = "/details_cs-001"
    update.message.reply_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Mock Telegram Context object"""
    context = MagicMock()
    context.user_data = {
        "last_recs": {
            "cs-001": {
                "major_id": "cs-001",
                "major_name_kh": "វិទ្យាសាស្ត្រកុំព្យូទ័រ",
                "major_name_en": "Computer Science",
                "university_name_kh": "សាកលវិទ្យាល័យភូមិន្ទ",
                "city": "ភ្នំពេញ",
                "tuition_fees_usd": 400,
                "mcda_score": 0.85,
                "ml_score": 0.72,
                "hybrid_score": 0.81,
                "mcda_reason": "ស្ថិតនៅភ្នំពេញតាមការចង់បាន, ជំនាញវិទ្យាសាស្ត្រនិងបច្ចេកវិទ្យាតាមចំណាប់អារម្មណ៍"
            }
        },
        "last_answers": {
            "location_preference": "pp",
            "budget_range": "low",
            "field_of_interest": "stem"
        }
    }
    return context


@pytest.fixture
def sample_programme():
    """Sample programme data"""
    return {
        "major_id": "cs-001",
        "major_name_kh": "វិទ្យាសាស្ត្រកុំព្យូទ័រ",
        "major_name_en": "Computer Science",
        "university_name_kh": "សាកលវិទ្យាល័យភូមិន្ទ",
        "city": "ភ្នំពេញ",
        "tuition_fees_usd": 400,
        "employment_rate": 90,
        "scholarship_availability": True,
        "is_phnom_penh": True,
        "is_stem": True,
        "is_low_cost": True
    }


class TestDetailsCommand:
    """Test details command functionality"""

    @pytest.mark.asyncio
    async def test_details_command_from_cache(self, mock_update, mock_context):
        """Test details command when program is in cache"""
        await details_command(mock_update, mock_context)
        
        # Should reply with formatted details
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args[0][0]
        
        # Check that response contains expected information
        assert "វិទ្យាសាស្ត្រកុំព្យូទ័រ" in call_args
        assert "សាកលវិទ្យាល័យភូមិន្ទ" in call_args
        assert "ភ្នំពេញ" in call_args
        assert "400 USD" in call_args
        assert "MCDA 0.85" in call_args
        assert "ML 0.72" in call_args

    @pytest.mark.asyncio
    async def test_details_command_with_lookup(self, mock_update, mock_context, sample_programme):
        """Test details command when program needs to be looked up"""
        # Remove from cache
        mock_context.user_data["last_recs"] = {}
        
        with patch('src.bot.utils.get_program_from_cache_or_lookup') as mock_lookup, \
             patch('src.bot.utils.ensure_program_scores') as mock_ensure_scores, \
             patch('src.bot.utils.format_program_details') as mock_format:
            
            # Mock the lookup and scoring
            mock_lookup.return_value = sample_programme
            scored_programme = sample_programme.copy()
            scored_programme.update({
                "mcda_score": 0.85,
                "ml_score": 0.72,
                "hybrid_score": 0.81,
                "mcda_reason": "ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ"
            })
            mock_ensure_scores.return_value = scored_programme
            mock_format.return_value = "Formatted details text"
            
            await details_command(mock_update, mock_context)
            
            # Verify the flow
            mock_lookup.assert_called_once_with("cs-001", mock_context)
            mock_ensure_scores.assert_called_once_with(sample_programme, mock_context.user_data["last_answers"])
            mock_format.assert_called_once_with(scored_programme)
            mock_update.message.reply_text.assert_called_once_with("Formatted details text")

    @pytest.mark.asyncio
    async def test_details_command_invalid_format(self, mock_context):
        """Test details command with invalid format"""
        update = MagicMock()
        update.message.text = "/invalid_command"
        update.message.reply_text = AsyncMock()
        
        await details_command(update, mock_context)
        
        update.message.reply_text.assert_called_once_with("❌ Invalid details command format")

    @pytest.mark.asyncio
    async def test_details_command_invalid_program_id(self, mock_context):
        """Test details command with invalid program ID"""
        update = MagicMock()
        update.message.text = "/details_invalid@id"
        update.message.reply_text = AsyncMock()
        
        await details_command(update, mock_context)
        
        update.message.reply_text.assert_called_once_with("❌ Invalid program ID")

    @pytest.mark.asyncio
    async def test_details_command_program_not_found(self, mock_update, mock_context):
        """Test details command when program is not found"""
        # Remove from cache
        mock_context.user_data["last_recs"] = {}
        
        with patch('src.bot.utils.get_program_from_cache_or_lookup') as mock_lookup:
            mock_lookup.return_value = None
            
            await details_command(mock_update, mock_context)
            
            mock_update.message.reply_text.assert_called_once_with("❌ Program not found: cs-001")

    @pytest.mark.asyncio
    async def test_details_command_exception_handling(self, mock_update, mock_context):
        """Test details command exception handling"""
        with patch('src.bot.utils.get_program_from_cache_or_lookup') as mock_lookup:
            mock_lookup.side_effect = Exception("Database error")
            
            await details_command(mock_update, mock_context)
            
            # Should handle exception gracefully
            mock_update.message.reply_text.assert_called_once()
            call_args = mock_update.message.reply_text.call_args[0][0]
            assert "❌ Error retrieving details" in call_args
            assert "Database error" in call_args


class TestDetailsCommandIntegration:
    """Integration tests for details command"""

    @pytest.mark.asyncio
    async def test_details_command_end_to_end(self, mock_update, mock_context, sample_programme):
        """Test details command end-to-end with real utilities"""
        # Remove from cache to force lookup
        mock_context.user_data["last_recs"] = {}
        
        with patch('src.core.data_loader.load_raw') as mock_load_raw, \
             patch('src.core.feature_engineering.add_derived_features') as mock_add_features, \
             patch('src.core.hybrid_recommender.get_recommendations') as mock_get_recs:
            
            # Mock data loading
            mock_load_raw.return_value = [sample_programme]
            mock_add_features.return_value = [sample_programme]
            
            # Mock scoring
            scored_programme = sample_programme.copy()
            scored_programme.update({
                "mcda_score": 0.85,
                "ml_score": 0.72,
                "hybrid_score": 0.81,
                "mcda_reason": "ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ"
            })
            mock_get_recs.return_value = [scored_programme]
            
            await details_command(mock_update, mock_context)
            
            # Should successfully return formatted details
            mock_update.message.reply_text.assert_called_once()
            call_args = mock_update.message.reply_text.call_args[0][0]
            
            # Check that response contains expected information
            assert "វិទ្យាសាស្ត្រកុំព្យូទ័រ" in call_args
            assert "សាកលវិទ្យាល័យភូមិន្ទ" in call_args

    @pytest.mark.asyncio
    async def test_details_command_with_missing_scores(self, mock_update, mock_context, sample_programme):
        """Test details command when program lacks scores"""
        # Put program in cache without scores
        mock_context.user_data["last_recs"]["cs-001"] = sample_programme
        
        with patch('src.core.hybrid_recommender.get_recommendations') as mock_get_recs:
            # Mock scoring
            scored_programme = sample_programme.copy()
            scored_programme.update({
                "mcda_score": 0.85,
                "ml_score": 0.72,
                "hybrid_score": 0.81,
                "mcda_reason": "ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ"
            })
            mock_get_recs.return_value = [scored_programme]
            
            await details_command(mock_update, mock_context)
            
            # Should call scoring system to get missing scores
            mock_get_recs.assert_called_once()
            mock_update.message.reply_text.assert_called_once()

    @pytest.mark.asyncio
    async def test_details_command_different_program_ids(self, mock_context):
        """Test details command with different program ID formats"""
        test_cases = [
            "/details_cs-001",
            "/details_bus_mgmt_001", 
            "/details_eng123",
            "/details_art-fine-001"
        ]
        
        for command_text in test_cases:
            update = MagicMock()
            update.message.text = command_text
            update.message.reply_text = AsyncMock()
            
            with patch('src.bot.utils.get_program_from_cache_or_lookup') as mock_lookup:
                mock_lookup.return_value = None  # Program not found
                
                await details_command(update, mock_context)
                
                # Should extract program ID correctly and attempt lookup
                expected_id = command_text[9:]  # Remove "/details_"
                mock_lookup.assert_called_once_with(expected_id, mock_context)
