"""
Internationalization (i18n) Tests
Tests for bilingual support and translation functionality
"""

import pytest
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.bot.i18n import t, get_lang, set_lang, clear_cache, get_supported_languages, is_supported


def test_translation_keys_exist():
    """Test that key translation strings exist in both languages"""
    # Test English translations
    assert t("start_greeting", "en").startswith("Hello"), "English greeting should start with 'Hello'"
    assert "Welcome" in t("start_greeting", "en"), "English greeting should contain 'Welcome'"
    
    # Test Khmer translations
    assert t("start_greeting", "kh").startswith("សួស្តី"), "Khmer greeting should start with 'សួស្តី'"
    assert "ស្វាគមន៍" in t("start_greeting", "kh"), "Khmer greeting should contain 'ស្វាគមន៍'"


def test_help_translations():
    """Test help command translations"""
    # English help
    en_help = t("help_title", "en")
    assert "Help" in en_help, "English help should contain 'Help'"
    
    # Khmer help
    kh_help = t("help_title", "kh")
    assert "ជំនួយ" in kh_help, "Khmer help should contain 'ជំនួយ'"


def test_assessment_translations():
    """Test assessment-related translations"""
    # Assessment title
    assert "Assessment" in t("assessment_title", "en"), "English assessment title should contain 'Assessment'"
    assert "ការវាយតម្លៃ" in t("assessment_title", "kh"), "Khmer assessment title should contain 'ការវាយតម្លៃ'"
    
    # Assessment complete
    assert "Complete" in t("assessment_complete", "en"), "English completion should contain 'Complete'"
    assert "បានបញ្ចប់" in t("assessment_complete", "kh"), "Khmer completion should contain 'បានបញ្ចប់'"


def test_language_preference_functions():
    """Test language preference get/set functions"""
    # Test with empty chat data
    chat_data = {}
    
    # Should default to Khmer
    assert get_lang(chat_data) == "kh", "Should default to Khmer"
    
    # Test setting English
    result_lang = set_lang(chat_data, "en")
    assert result_lang == "en", "Should return English"
    assert get_lang(chat_data) == "en", "Should store English preference"
    
    # Test setting back to Khmer
    result_lang = set_lang(chat_data, "kh")
    assert result_lang == "kh", "Should return Khmer"
    assert get_lang(chat_data) == "kh", "Should store Khmer preference"
    
    # Test invalid language code
    result_lang = set_lang(chat_data, "fr")  # French not supported
    assert result_lang == "kh", "Should fallback to Khmer for unsupported language"


def test_language_toggle_persistence():
    """Test that language toggle persists across commands"""
    chat_data = {}
    
    # Start with default (Khmer)
    assert get_lang(chat_data) == "kh"
    
    # Switch to English
    set_lang(chat_data, "en")
    
    # Verify persistence across multiple calls
    assert get_lang(chat_data) == "en"
    assert get_lang(chat_data) == "en"
    assert get_lang(chat_data) == "en"
    
    # Switch back to Khmer
    set_lang(chat_data, "kh")
    assert get_lang(chat_data) == "kh"


def test_fallback_behavior():
    """Test fallback behavior for missing keys"""
    # Test with non-existent key
    result = t("non_existent_key", "en")
    assert result == "non_existent_key", "Should return key itself as fallback"
    
    result = t("non_existent_key", "kh")
    assert result == "non_existent_key", "Should return key itself as fallback"
    
    # Test with invalid language
    result = t("start_greeting", "invalid_lang")
    assert result == t("start_greeting", "kh"), "Should fallback to Khmer for invalid language"


def test_supported_languages():
    """Test supported language functions"""
    supported = get_supported_languages()
    assert "kh" in supported, "Khmer should be supported"
    assert "en" in supported, "English should be supported"
    assert len(supported) == 2, "Should support exactly 2 languages"
    
    # Test is_supported function
    assert is_supported("kh"), "Khmer should be supported"
    assert is_supported("en"), "English should be supported"
    assert not is_supported("fr"), "French should not be supported"
    assert not is_supported("invalid"), "Invalid codes should not be supported"


def test_cache_functionality():
    """Test translation cache functionality"""
    # Clear cache first
    clear_cache()
    
    # Load translations
    result1 = t("start_greeting", "en")
    result2 = t("start_greeting", "en")
    
    # Should return same result (from cache)
    assert result1 == result2, "Cached results should be identical"
    
    # Clear cache and test again
    clear_cache()
    result3 = t("start_greeting", "en")
    assert result1 == result3, "Results should be consistent after cache clear"


def test_question_option_translations():
    """Test that question options are properly translated"""
    # Test location options
    assert t("option_pp", "en") == "Phnom Penh", "English Phnom Penh translation"
    assert t("option_pp", "kh") == "ភ្នំពេញ", "Khmer Phnom Penh translation"
    
    # Test budget options
    assert "$500" in t("option_mid", "en"), "English mid budget should contain $500"
    assert "$500" in t("option_mid", "kh"), "Khmer mid budget should contain $500"
    
    # Test field options
    assert "Science" in t("option_stem", "en"), "English STEM should contain 'Science'"
    assert "វិទ្យាសាស្ត្រ" in t("option_stem", "kh"), "Khmer STEM should contain 'វិទ្យាសាស្ត្រ'"


def test_settings_translations():
    """Test settings-related translations"""
    # Settings title
    assert "Settings" in t("settings_title", "en"), "English settings title"
    assert "ការកំណត់" in t("settings_title", "kh"), "Khmer settings title"
    
    # Language labels
    assert "Khmer" in t("language_khmer", "en"), "English Khmer label"
    assert "ខ្មែរ" in t("language_khmer", "kh"), "Khmer Khmer label"
    
    assert "English" in t("language_english", "en"), "English English label"
    assert "English" in t("language_english", "kh"), "Khmer English label"


def test_error_message_translations():
    """Test error message translations"""
    # Error messages should exist in both languages
    assert "Error" in t("error_recommendation", "en"), "English error message"
    assert "បញ្ហា" in t("error_recommendation", "kh"), "Khmer error message"
    
    # Cancel message
    assert "cancelled" in t("cancel_message", "en").lower(), "English cancel message"
    assert "បោះបង់" in t("cancel_message", "kh"), "Khmer cancel message"


def test_progress_text_formatting():
    """Test that progress text supports formatting"""
    # Test progress text with placeholders
    progress_en = t("assessment_progress", "en")
    progress_kh = t("assessment_progress", "kh")
    
    # Should contain format placeholders
    assert "{current}" in progress_en, "English progress should have {current} placeholder"
    assert "{total}" in progress_en, "English progress should have {total} placeholder"
    assert "{current}" in progress_kh, "Khmer progress should have {current} placeholder"
    assert "{total}" in progress_kh, "Khmer progress should have {total} placeholder"


def test_recommendation_translations():
    """Test recommendation-related translations"""
    # Recommendation title
    assert "Recommendations" in t("recommendations_title", "en"), "English recommendations title"
    assert "អនុសាសន៍" in t("recommendations_title", "kh"), "Khmer recommendations title"
    
    # Details link
    details_en = t("recommendation_card_details", "en")
    details_kh = t("recommendation_card_details", "kh")
    
    assert "Details" in details_en, "English details should contain 'Details'"
    assert "ព័ត៌មានលម្អិត" in details_kh, "Khmer details should contain 'ព័ត៌មានលម្អិត'"


def test_command_translations():
    """Test command-related translations"""
    # Help commands
    help_commands_en = t("help_commands", "en")
    help_commands_kh = t("help_commands", "kh")
    
    assert "/start" in help_commands_en, "English help should list /start command"
    assert "/help" in help_commands_en, "English help should list /help command"
    assert "/start" in help_commands_kh, "Khmer help should list /start command"
    assert "/help" in help_commands_kh, "Khmer help should list /help command"


def test_multiple_user_contexts():
    """Test that different user contexts maintain separate language preferences"""
    user1_data = {}
    user2_data = {}
    
    # Set different languages for different users
    set_lang(user1_data, "en")
    set_lang(user2_data, "kh")
    
    # Verify they maintain separate preferences
    assert get_lang(user1_data) == "en", "User 1 should have English"
    assert get_lang(user2_data) == "kh", "User 2 should have Khmer"
    
    # Change one user's preference
    set_lang(user1_data, "kh")
    
    # Verify other user is unaffected
    assert get_lang(user1_data) == "kh", "User 1 should now have Khmer"
    assert get_lang(user2_data) == "kh", "User 2 should still have Khmer"


def test_translation_completeness():
    """Test that key translations exist in both languages"""
    key_translations = [
        "start_greeting", "start_description", "start_button",
        "assessment_title", "assessment_complete", "recommendations_title",
        "help_title", "help_commands", "settings_title",
        "language_khmer", "language_english", "language_changed"
    ]
    
    for key in key_translations:
        en_result = t(key, "en")
        kh_result = t(key, "kh")
        
        # Should not return the key itself (indicating missing translation)
        assert en_result != key, f"English translation missing for key: {key}"
        assert kh_result != key, f"Khmer translation missing for key: {key}"
        
        # Should not be empty
        assert len(en_result.strip()) > 0, f"English translation empty for key: {key}"
        assert len(kh_result.strip()) > 0, f"Khmer translation empty for key: {key}"


def test_unsupported_language_fallback():
    """Test that unsupported languages fall back to default"""
    from src.bot.i18n import get_translation_bundle, lazy_load_bundle, t

    # Test get_translation_bundle with unsupported language
    bundle = get_translation_bundle('fr')  # French not supported
    default_bundle = get_translation_bundle('kh')  # Default language
    assert bundle == default_bundle, "Unsupported language should fallback to default"

    # Test lazy_load_bundle with unsupported language
    lazy_bundle = lazy_load_bundle('es')  # Spanish not supported
    assert lazy_bundle == default_bundle, "Lazy load should fallback to default"

    # Test t() function with unsupported language
    result = t('welcome', 'de')  # German not supported
    expected = t('welcome', 'kh')  # Default language
    assert result == expected, "Translation should fallback to default language"


def test_translation_file_not_found():
    """Test behavior when translation files are missing"""
    from src.bot.i18n import get_translation_bundle
    from unittest.mock import patch

    # Clear cache first to ensure fresh load
    get_translation_bundle.cache_clear()

    # Mock both resource loading and file loading to fail
    with patch('importlib.resources.read_text', side_effect=FileNotFoundError):
        with patch('builtins.open', side_effect=FileNotFoundError):
            bundle = get_translation_bundle('unsupported_lang')  # Use unsupported language
            assert bundle == {}, "Should return empty dict when files not found"


def test_translation_json_decode_error():
    """Test behavior when translation files have invalid JSON"""
    from src.bot.i18n import get_translation_bundle
    from unittest.mock import patch, mock_open
    import json

    # Clear cache first to ensure fresh load
    get_translation_bundle.cache_clear()

    # Mock resource loading to fail, file loading to return invalid JSON
    with patch('importlib.resources.read_text', side_effect=FileNotFoundError):
        with patch('builtins.open', mock_open(read_data='invalid json')):
            bundle = get_translation_bundle('unsupported_lang')  # Use unsupported language
            assert bundle == {}, "Should return empty dict when JSON is invalid"
