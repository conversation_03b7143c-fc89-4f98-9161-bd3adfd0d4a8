"""
Tests for the /details command functionality
"""

import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from telegram import Update, Message, User, Chat
from telegram.ext import ContextTypes

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.bot.commands import details_command, DETAILS_PLAIN_TEMPLATE
from src.bot.validation import validate_program_id


@pytest.fixture
def mock_update():
    """Create a mock Telegram update object"""
    update = Mock(spec=Update)
    update.message = Mock(spec=Message)
    update.message.text = "/details_accounting"
    update.message.reply_text = AsyncMock()
    update.message.chat = Mock(spec=Chat)
    update.message.chat.id = 12345
    update.message.from_user = Mock(spec=User)
    update.message.from_user.id = 67890
    return update


@pytest.fixture
def mock_context():
    """Create a mock bot context"""
    context = Mock(spec=ContextTypes.DEFAULT_TYPE)
    context.user_data = {
        "last_recs": {
            "accounting": {
                "major_id": "accounting",
                "major_name_kh": "បរិញ្ញាបត្រគណនេយ្យ",
                "university_name_kh": "សាកលវិទ្យាល័យបញ្ញាជាតិ",
                "city": "ភ្នំពេញ",
                "tuition_fees_usd": "750",
                "mcda_score": 0.85,
                "ml_score": 0.78,
                "mcda_reason": "ផ្តល់អនុសាសន៍ដោយសារតម្លៃសិក្សាសមរម្យ"
            }
        },
        "last_answers": {
            "location_preference": "pp",
            "budget_range": "medium",
            "field_of_interest": "business"
        }
    }
    return context


def test_details_command_with_cached_program(mock_update, mock_context):
    """Test /details command with program in cache (from last recommendations)"""

    async def run_test():
        # Test with valid program ID that exists in cache
        await details_command(mock_update, mock_context)

        # Verify reply was sent
        mock_update.message.reply_text.assert_called_once()

        # Get the reply text
        call_args = mock_update.message.reply_text.call_args[0]
        reply_text = call_args[0]

        # Verify the details contain expected information
        assert "បរិញ្ញាបត្រគណនេយ្យ" in reply_text  # Program name in Khmer
        assert "សាកលវិទ្យាល័យបញ្ញាជាតិ" in reply_text  # University name
        assert "750 USD" in reply_text  # Tuition fee
        assert "0.85" in reply_text  # MCDA score
        assert "0.78" in reply_text  # ML score
        assert "ផ្តល់អនុសាសន៍ដោយសារតម្លៃសិក្សាសមរម្យ" in reply_text  # Reason

    asyncio.run(run_test())


def test_details_command_with_fallback_lookup(mock_update, mock_context):
    """Test /details command with program not in cache (requires data lookup)"""

    async def run_test():
        # Clear the cache to force fallback lookup
        mock_context.user_data["last_recs"] = {}

        # Mock the data loading functions
        mock_program = {
            "major_id": "accounting",
            "major_name_kh": "បរិញ្ញាបត្រគណនេយ្យ",
            "university_name_kh": "សាកលវិទ្យាល័យបញ្ញាជាតិ",
            "city": "ភ្នំពេញ",
            "tuition_fees_usd": "750"
        }

        with patch('src.core.data_loader.load_raw') as mock_load_raw, \
             patch('src.core.feature_engineering.add_derived_features') as mock_add_features, \
             patch('src.core.hybrid_recommender.get_recommendations') as mock_get_recs:

            # Setup mocks
            mock_load_raw.return_value = [mock_program]
            mock_add_features.return_value = [mock_program]

            # Mock the hybrid recommender to return scored program
            scored_program = mock_program.copy()
            scored_program.update({
                "mcda_score": 0.75,
                "ml_score": 0.68,
                "hybrid_score": 0.72,
                "mcda_reason": "ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ"
            })
            mock_get_recs.return_value = [scored_program]

            # Test the command
            await details_command(mock_update, mock_context)

            # Verify data loading was called
            mock_load_raw.assert_called_once()
            assert mock_add_features.call_count >= 1
            # Verify hybrid recommender was called for scoring
            mock_get_recs.assert_called_once()

            # Verify reply was sent
            mock_update.message.reply_text.assert_called_once()

            # Get the reply text
            call_args = mock_update.message.reply_text.call_args[0]
            reply_text = call_args[0]

            # Verify the details contain expected information
            assert "បរិញ្ញាបត្រគណនេយ្យ" in reply_text
            assert "750 USD" in reply_text
            assert "0.75" in reply_text  # MCDA score from mock

    asyncio.run(run_test())


def test_details_command_invalid_format(mock_update, mock_context):
    """Test /details command with invalid command format"""

    async def run_test():
        # Test with invalid command format
        mock_update.message.text = "/details"  # Missing program ID

        await details_command(mock_update, mock_context)

        # Verify error message was sent
        mock_update.message.reply_text.assert_called_once_with("❌ Invalid details command format")

    asyncio.run(run_test())


def test_details_command_invalid_program_id(mock_update, mock_context):
    """Test /details command with invalid program ID"""

    async def run_test():
        # Test with invalid program ID
        mock_update.message.text = "/details_invalid-id!"

        await details_command(mock_update, mock_context)

        # Verify error message was sent
        mock_update.message.reply_text.assert_called_once_with("❌ Invalid program ID")

    asyncio.run(run_test())


def test_details_command_program_not_found(mock_update, mock_context):
    """Test /details command with program not found"""

    async def run_test():
        # Clear cache and mock empty data
        mock_context.user_data["last_recs"] = {}
        mock_update.message.text = "/details_nonexistent"

        with patch('src.core.data_loader.load_raw') as mock_load_raw, \
             patch('src.core.feature_engineering.add_derived_features') as mock_add_features:

            # Setup mocks to return empty data
            mock_load_raw.return_value = []
            mock_add_features.return_value = []

            await details_command(mock_update, mock_context)

            # Verify error message was sent
            mock_update.message.reply_text.assert_called_once_with("❌ Program not found: nonexistent")

    asyncio.run(run_test())


def test_details_template_formatting():
    """Test that the details template formats correctly with sample data"""
    
    sample_program = {
        "major_name_kh": "បរិញ្ញាបត្រគណនេយ្យ",
        "university_name_kh": "សាកលវិទ្យាល័យបញ្ញាជាតិ",
        "city": "ភ្នំពេញ",
        "tuition_fees_usd": "750",
        "mcda_score": 0.85,
        "ml_score": 0.78,
        "mcda_reason": "ផ្តល់អនុសាសន៍ដោយសារតម្លៃសិក្សាសមរម្យ"
    }
    
    # Test template formatting
    result = DETAILS_PLAIN_TEMPLATE.format(**sample_program)
    
    # Verify all expected elements are present
    assert "បរិញ្ញាបត្រគណនេយ្យ" in result
    assert "សាកលវិទ្យាល័យបញ្ញាជាតិ" in result
    assert "ភ្នំពេញ" in result
    assert "750 USD" in result
    assert "0.85" in result
    assert "0.78" in result
    assert "ផ្តល់អនុសាសន៍ដោយសារតម្លៃសិក្សាសមរម្យ" in result


def test_program_id_validation():
    """Test program ID validation function"""
    
    # Valid IDs
    valid_ids = ["accounting", "computer-science", "business_admin", "eng123", "a" * 60]
    for valid_id in valid_ids:
        assert validate_program_id(valid_id), f"Should accept valid ID: {valid_id}"
    
    # Invalid IDs
    invalid_ids = [
        "",  # Empty
        "ab",  # Too short
        "a" * 61,  # Too long
        "invalid!",  # Special characters
        "invalid@id",  # Special characters
        "../../etc/passwd",  # Path traversal
        "id with spaces",  # Spaces
        123,  # Not a string
        None  # None
    ]
    for invalid_id in invalid_ids:
        assert not validate_program_id(invalid_id), f"Should reject invalid ID: {invalid_id}"
