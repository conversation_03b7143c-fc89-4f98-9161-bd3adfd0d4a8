"""
MCDA Regression Tests
Tests to prevent regressions in MCDA scoring
"""

import sys
from pathlib import Path
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parents[2]))

from src.core.mcda import score_vectorized


def test_score_budget_accepts_strings():
    """Test that budget scoring accepts string inputs"""
    # Test through the main scoring function since _score_budget is internal
    user_answers = {"budget_range": "mid", "location_preference": "pp"}
    programmes = [{
        "major_id": "test",
        "cost_level": 1,
        "city": "ភ្នំពេញ",
        "is_phnom_penh": True,
        "is_low_cost": False,
        "is_medium_cost": True,
        "is_high_cost": False
    }]

    # Should not crash and return valid score
    scores = score_vectorized(user_answers, programmes)
    assert isinstance(scores, np.ndarray)
    assert len(scores) == 1
    assert isinstance(scores[0], (int, float))
    assert scores[0] >= 0.0


def test_score_vectorized_string_coercion():
    """Test that score_vectorized handles string inputs properly"""
    user_answers = {
        "budget_range": "low",  # string
        "field_weight": "2",    # string number
        "location_preference": "pp"
    }
    
    programmes = [
        {
            "major_id": "test1",
            "city": "ភ្នំពេញ",
            "cost_level": 0,
            "field_tag": "stem",
            "is_phnom_penh": True,
            "is_stem": True,
            "is_low_cost": True
        }
    ]
    
    # Should not crash and return valid scores
    scores = score_vectorized(user_answers, programmes)
    assert isinstance(scores, np.ndarray)
    assert len(scores) == 1
    assert isinstance(scores[0], (int, float))
    assert 0.0 <= scores[0] <= 1.0
