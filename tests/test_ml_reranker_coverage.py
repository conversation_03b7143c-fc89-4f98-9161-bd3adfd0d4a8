"""
ML Reranker Coverage Tests
Tests to improve coverage for ml_reranker.py
"""

import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.ml_reranker import M<PERSON>eranker


def test_ml_reranker_model_not_found():
    """Test MLReranker when model file doesn't exist"""
    with patch("pathlib.Path.exists", return_value=False):
        reranker = <PERSON>LReranker("nonexistent.joblib")
        # Should handle missing model gracefully
        assert reranker.model is None
        assert reranker.scaler is None


def test_ml_reranker_extract_features_edge_cases():
    """Test feature extraction with edge cases"""
    from src.core.feature_schema import extract_features

    # Test with minimal programme data
    user_answers = {}
    programme = {"major_id": "test"}

    features = extract_features(user_answers, programme)
    assert isinstance(features, list)
    assert len(features) > 0


def test_ml_reranker_rank_no_model():
    """Test rank when no model is loaded"""
    reranker = MLReranker("fake.joblib")
    reranker.model = None
    reranker.scaler = None
    
    programmes = [{"major_id": "test", "mcda_score": 0.8}]
    result = reranker.rank({}, programmes)
    
    # Should return programmes with neutral ML scores
    assert len(result) == 1
    assert result[0]["ml_score"] == 0.5


def test_ml_reranker_rank_empty_programmes():
    """Test rank with empty programmes list"""
    reranker = MLReranker("fake.joblib")
    reranker.model = MagicMock()
    reranker.scaler = MagicMock()
    
    result = reranker.rank({}, [])
    assert result == []
