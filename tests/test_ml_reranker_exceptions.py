"""
ML Reranker Exception Tests
Tests for exception handling in ML reranker
"""

import numpy as np
from unittest.mock import MagicMock, patch
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.ml_reranker import <PERSON><PERSON><PERSON><PERSON>


def _dummy_reranker():
    r = <PERSON>LReranker("fake.joblib")
    r.model = MagicMock()
    r.scaler = MagicMock()
    return r


def test_rank_scaler_failure():
    r = _dummy_reranker()
    r.scaler.transform.side_effect = RuntimeError
    out = r.rank({}, [{"major_id": "x", "mcda_score": .8}])
    assert out[0]["ml_score"] == 0.5


def test_rank_predict_failure():
    r = _dummy_reranker()
    r.scaler.transform.return_value = np.zeros((1, 3))
    r.model.predict.side_effect = ValueError
    out = r.rank({}, [{"major_id": "x", "mcda_score": .8}])
    assert out[0]["ml_score"] == 0.5
