"""
MCDA Edge Input Tests
Tests for edge cases in MCDA scoring
"""

import sys
from pathlib import Path
import numpy as np

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.mcda import score_vectorized, score


def test_vectorized_none_or_empty():
    result1 = score_vectorized({}, None)
    result2 = score_vectorized({}, [])
    assert isinstance(result1, np.ndarray) and len(result1) == 0
    assert isinstance(result2, np.ndarray) and len(result2) == 0


def test_score_none_inputs():
    assert score(None, None) == 0.0
