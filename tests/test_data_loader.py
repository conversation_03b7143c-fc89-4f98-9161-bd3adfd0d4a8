"""
Data Loader Tests
Unit tests for data loading functionality
"""

import pytest
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.data_loader import load_raw, validate_program_data


class TestDataLoader:
    """Test cases for data loader module"""

    def test_load_raw_returns_list(self):
        """
        Test that load_raw() returns a list
        """
        # Test with fixture data
        fixture_dir = str(Path(__file__).parent / "fixtures")
        result = load_raw(data_dir=fixture_dir)
        assert isinstance(result, list)
        assert len(result) > 0  # Should have programs from fixtures

    def test_load_raw_program_count(self):
        """
        Test that load_raw() returns expected number of programs from fixtures
        """
        fixture_dir = str(Path(__file__).parent / "fixtures")
        result = load_raw(data_dir=fixture_dir)
        # Should have 5 programs from our 3 fixture files (2+2+1)
        assert len(result) == 5
    
    def test_validate_program_data_valid(self):
        """
        Test program validation with valid data
        """
        valid_program = {
            'university_id': 'test_uni',
            'university_name_kh': 'សាកលវិទ្យាល័យសាកល្បង',
            'university_name_en': 'Test University',
            'major_id': 'test_major',
            'major_name_kh': 'ជំនាញសាកល្បង',
            'major_name_en': 'Test Major',
            'city': 'ភ្នំពេញ'
        }
        assert validate_program_data(valid_program) == True
    
    def test_validate_program_data_invalid(self):
        """
        Test program validation with invalid data
        """
        invalid_program = {
            'university_id': 'test_uni',
            # Missing required fields
        }
        assert validate_program_data(invalid_program) == False

    def test_validate_program_data_missing_ids(self):
        """
        Test program validation when both university_id and major_id are missing
        """
        program_missing_ids = {
            'university_name_kh': 'សាកលវិទ្យាល័យសាកល្បង',
            'university_name_en': 'Test University',
            'major_name_kh': 'ជំនាញសាកល្បង',
            'major_name_en': 'Test Major',
            'city': 'ភ្នំពេញ'
            # Missing both university_id and major_id
        }
        assert validate_program_data(program_missing_ids) == False

    def test_load_raw_with_corrupted_json(self):
        """
        Test load_raw handles corrupted JSON files gracefully
        """
        from unittest.mock import patch, mock_open
        import json

        # Mock glob to return a fake file path
        fake_files = [Path("fake_file.json")]

        # Mock file content with invalid JSON
        mock_file_content = '{"invalid": json content}'

        with patch('pathlib.Path.glob', return_value=fake_files):
            with patch('builtins.open', mock_open(read_data=mock_file_content)):
                with patch('json.load', side_effect=json.JSONDecodeError("Invalid JSON", "", 0)):
                    result = load_raw()
                    # Should return empty list when JSON is corrupted
                    assert isinstance(result, list)

    def test_load_raw_with_file_read_error(self):
        """
        Test load_raw handles file read errors gracefully
        """
        from unittest.mock import patch

        # Mock glob to return a fake file path
        fake_files = [Path("fake_file.json")]

        with patch('pathlib.Path.glob', return_value=fake_files):
            with patch('builtins.open', side_effect=IOError("File read error")):
                result = load_raw()
                # Should return empty list when file cannot be read
                assert isinstance(result, list)


if __name__ == '__main__':
    pytest.main([__file__])
