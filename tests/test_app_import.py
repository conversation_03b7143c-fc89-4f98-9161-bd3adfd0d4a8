"""
App Import Tests
Tests that the bot app can be imported without hanging
"""

import os
import importlib


def test_app_import():
    """Test that src.bot.app can be imported with dry token"""
    # Set dry token to prevent network calls
    os.environ['BOT_TOKEN'] = 'dry'
    
    # Import the module
    mod = importlib.import_module("src.bot.app")
    
    # Verify main function exists
    assert hasattr(mod, "main")
    
    # Verify main function is callable
    assert callable(mod.main)


def test_app_main_dry_run():
    """Test that main() works in dry-run mode"""
    # Set dry token
    os.environ['BOT_TOKEN'] = 'dry'
    
    # Import and call main
    from src.bot.app import main
    
    # Should not raise any exceptions
    main(dry_run=True)
