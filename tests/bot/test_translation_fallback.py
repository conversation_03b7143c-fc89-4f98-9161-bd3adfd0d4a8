"""
Tests for i18n and translation fallback system
"""

import pytest
from src.bot.ui import tr


class TestTranslationFallback:
    """Test translation fallback system"""
    
    def test_tr_fallback_to_english(self):
        """Test fallback to English when key not found in target language"""
        # This should fallback to English if key doesn't exist in Khmer
        result = tr("nonexistent_key", "kh")
        assert result == "nonexistent_key"  # Should return key itself as final fallback
    
    def test_tr_existing_key_khmer(self):
        """Test translation of existing key in Khmer"""
        result = tr("more", "kh")
        assert result == "ព័ត៌មានបន្ថែម"
    
    def test_tr_existing_key_english(self):
        """Test translation of existing key in English"""
        result = tr("more", "en")
        assert result == "More"
    
    def test_tr_fallback_chain(self):
        """Test complete fallback chain: target lang -> English -> key itself"""
        # Test with completely nonexistent key
        result = tr("completely_nonexistent_key_12345", "kh")
        assert result == "completely_nonexistent_key_12345"
    
    def test_tr_invalid_language(self):
        """Test with invalid language code"""
        result = tr("more", "invalid_lang")
        # Should fallback gracefully
        assert isinstance(result, str)
        assert len(result) > 0


class TestNewTranslationKeys:
    """Test that all new translation keys are available"""
    
    def test_enhanced_navigation_keys_khmer(self):
        """Test enhanced navigation keys in Khmer"""
        keys_to_test = [
            ("more", "ព័ត៌មានបន្ថែម"),
            ("compare", "ប្រៀបធៀប"),
            ("filters", "តម្រង"),
            ("prev", "មុន"),
            ("next", "បន្ទាប់"),
            ("save", "រក្សាទុក"),
            ("remove", "លុបចេញ")
        ]
        
        for key, expected in keys_to_test:
            result = tr(key, "kh")
            assert result == expected, f"Key '{key}' should translate to '{expected}' but got '{result}'"
    
    def test_enhanced_navigation_keys_english(self):
        """Test enhanced navigation keys in English"""
        keys_to_test = [
            ("more", "More"),
            ("compare", "Compare"),
            ("filters", "Filters"),
            ("prev", "Prev"),
            ("next", "Next"),
            ("save", "Save"),
            ("remove", "Remove")
        ]
        
        for key, expected in keys_to_test:
            result = tr(key, "en")
            assert result == expected, f"Key '{key}' should translate to '{expected}' but got '{result}'"
    
    def test_comparison_keys(self):
        """Test comparison-related translation keys"""
        comparison_keys = ["comparison", "metric", "score"]
        
        for key in comparison_keys:
            kh_result = tr(key, "kh")
            en_result = tr(key, "en")
            
            assert isinstance(kh_result, str)
            assert isinstance(en_result, str)
            assert len(kh_result) > 0
            assert len(en_result) > 0
    
    def test_filter_keys(self):
        """Test filter-related translation keys"""
        filter_keys = ["select_filter", "budget_filter", "field_filter", "city_filter"]
        
        for key in filter_keys:
            kh_result = tr(key, "kh")
            en_result = tr(key, "en")
            
            assert isinstance(kh_result, str)
            assert isinstance(en_result, str)
            assert len(kh_result) > 0
            assert len(en_result) > 0
    
    def test_feedback_keys(self):
        """Test user feedback translation keys"""
        feedback_keys = ["saved", "already_saved", "not_found", "unknown_action", "please_restart"]
        
        for key in feedback_keys:
            kh_result = tr(key, "kh")
            en_result = tr(key, "en")
            
            assert isinstance(kh_result, str)
            assert isinstance(en_result, str)
            assert len(kh_result) > 0
            assert len(en_result) > 0


class TestTranslationConsistency:
    """Test translation consistency and completeness"""
    
    def test_all_keys_have_both_languages(self):
        """Test that all keys exist in both Khmer and English"""
        # Get all new keys from the UI module
        from src.bot.ui import add_missing_translation_keys
        required_keys = add_missing_translation_keys()
        
        for key in required_keys:
            kh_result = tr(key, "kh")
            en_result = tr(key, "en")
            
            # Both should return actual translations, not the key itself
            assert kh_result != key or en_result != key, f"Key '{key}' missing in both languages"
    
    def test_no_empty_translations(self):
        """Test that no translations are empty strings"""
        test_keys = ["more", "compare", "filters", "save", "remove", "back"]
        
        for key in test_keys:
            kh_result = tr(key, "kh")
            en_result = tr(key, "en")
            
            assert kh_result.strip() != "", f"Khmer translation for '{key}' is empty"
            assert en_result.strip() != "", f"English translation for '{key}' is empty"
