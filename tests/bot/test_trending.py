"""
Tests for trending majors functionality
Tests carousel structure, callback compliance, and empty data handling
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, mock_open
from telegram import Update, CallbackQuery, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from src.bot.handlers import trending_callback
from src.bot.ui import load_trending_majors, create_trending_carousel


@pytest.fixture
def mock_update():
    """Create mock update with callback query"""
    update = Mock(spec=Update)
    update.callback_query = Mock(spec=CallbackQuery)
    update.callback_query.answer = AsyncMock()
    update.callback_query.edit_message_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Create mock context with user data"""
    context = Mock(spec=ContextTypes.DEFAULT_TYPE)
    user_data = {
        'language': 'en',
        'shortlist': [],
        'last_recs': {}
    }
    context.user_data = user_data
    return context


@pytest.fixture
def sample_trending_data():
    """Sample trending majors data"""
    return [
        {
            "major_id": "computer-science",
            "name_en": "Computer Science",
            "name_kh": "វិទ្យាសាស្ត្រកុំព្យូទ័រ"
        },
        {
            "major_id": "business-management",
            "name_en": "Business Management", 
            "name_kh": "គ្រប់គ្រងអាជីវកម្ម"
        },
        {
            "major_id": "civil-engineering",
            "name_en": "Civil Engineering",
            "name_kh": "វិស្វកម្មស៊ីវិល"
        }
    ]


@pytest.fixture
def sample_programmes():
    """Sample programme data for testing"""
    return [
        {
            'major_id': 'computer-science',
            'major_name_en': 'Computer Science',
            'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
            'university_name_en': 'Royal University of Phnom Penh',
            'degree_type': 'Bachelor'
        },
        {
            'major_id': 'business-management',
            'major_name_en': 'Business Management',
            'major_name_kh': 'គ្រប់គ្រងអាជីវកម្ម',
            'university_name_en': 'University of Commerce',
            'degree_type': 'Bachelor'
        }
    ]


class TestTrendingDataLoading:
    """Test trending majors data loading functionality"""
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('pathlib.Path.exists')
    def test_load_trending_majors_success(self, mock_exists, mock_file, sample_trending_data):
        """Test successful loading of trending majors data"""
        import json
        
        mock_exists.return_value = True
        mock_file.return_value.read.return_value = json.dumps(sample_trending_data)
        
        result = load_trending_majors()
        
        assert len(result) == 3
        assert result[0]['major_id'] == 'computer-science'
        assert result[1]['major_id'] == 'business-management'
        assert result[2]['major_id'] == 'civil-engineering'
    
    @patch('pathlib.Path.exists')
    def test_load_trending_majors_file_not_found(self, mock_exists):
        """Test graceful handling when trending.json doesn't exist"""
        mock_exists.return_value = False
        
        result = load_trending_majors()
        
        assert result == []
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('pathlib.Path.exists')
    def test_load_trending_majors_invalid_json(self, mock_exists, mock_file):
        """Test graceful handling of invalid JSON"""
        mock_exists.return_value = True
        mock_file.return_value.read.return_value = "invalid json content"
        
        result = load_trending_majors()
        
        assert result == []
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('pathlib.Path.exists')
    def test_load_trending_majors_limit_to_10(self, mock_exists, mock_file):
        """Test that trending majors are limited to 10 items maximum"""
        # Create 15 trending majors
        large_trending_data = []
        for i in range(15):
            large_trending_data.append({
                "major_id": f"major-{i}",
                "name_en": f"Major {i}",
                "name_kh": f"ជំនាញ {i}"
            })
        
        import json
        mock_exists.return_value = True
        mock_file.return_value.read.return_value = json.dumps(large_trending_data)
        
        result = load_trending_majors()
        
        # Should be limited to 10
        assert len(result) == 10
        assert result[0]['major_id'] == 'major-0'
        assert result[9]['major_id'] == 'major-9'


class TestTrendingCarousel:
    """Test trending carousel generation"""
    
    @patch('src.bot.ui.load_trending_majors')
    def test_create_trending_carousel_english(self, mock_load_trending, sample_trending_data):
        """Test trending carousel creation in English"""
        mock_load_trending.return_value = sample_trending_data
        
        result = create_trending_carousel('en')
        
        assert len(result) == 2  # 3 items = 2 rows (2 items per row)
        
        # First row should have 2 buttons
        assert len(result[0]) == 2
        assert "Computer" in result[0][0].text  # Partial match for truncated text
        assert result[0][0].callback_data == "TREND_computer-science"
        assert "Business" in result[0][1].text  # Partial match for truncated text
        assert result[0][1].callback_data == "TREND_business-management"

        # Second row should have 1 button
        assert len(result[1]) == 1
        assert "Civil" in result[1][0].text  # Partial match for truncated text
        assert result[1][0].callback_data == "TREND_civil-engineering"
    
    @patch('src.bot.ui.load_trending_majors')
    def test_create_trending_carousel_khmer(self, mock_load_trending, sample_trending_data):
        """Test trending carousel creation in Khmer"""
        mock_load_trending.return_value = sample_trending_data
        
        result = create_trending_carousel('kh')
        
        assert len(result) == 2
        
        # Check Khmer names are used (may be truncated)
        assert "វិទ្យាសាស្ត្រ" in result[0][0].text  # Partial match for truncated text
        assert "គ្រប់គ្រង" in result[0][1].text
        assert "វិស្វកម្ម" in result[1][0].text
    
    @patch('src.bot.ui.load_trending_majors')
    def test_create_trending_carousel_empty_data(self, mock_load_trending):
        """Test trending carousel with empty data"""
        mock_load_trending.return_value = []
        
        result = create_trending_carousel('en')
        
        assert result == []
    
    @patch('src.bot.ui.load_trending_majors')
    def test_create_trending_carousel_long_names(self, mock_load_trending):
        """Test trending carousel with long major names"""
        long_name_data = [
            {
                "major_id": "very-long-major-name",
                "name_en": "Very Long Major Name That Exceeds Normal Length",
                "name_kh": "ជំនាញដែលមានឈ្មោះវែងណាស់"
            }
        ]
        mock_load_trending.return_value = long_name_data
        
        result = create_trending_carousel('en')
        
        assert len(result) == 1
        # Long names should be truncated
        button_text = result[0][0].text
        assert len(button_text) <= 20  # Should be truncated with "..."
        assert "..." in button_text


class TestCallbackDataCompliance:
    """Test callback data compliance with Telegram limits"""
    
    def test_callback_data_length_compliance(self):
        """Test that all TREND_* callback data stays under 64 bytes"""
        # Test with various major_id lengths
        major_ids = [
            "cs",
            "computer-science",
            "business-management-bachelor-degree",
            "very-long-major-name-that-might-exceed-limits"
        ]
        
        for major_id in major_ids:
            callback_data = f"TREND_{major_id}"
            byte_length = len(callback_data.encode('utf-8'))
            
            if byte_length <= 64:
                # Should be accepted
                assert True
            else:
                # Should be filtered out by the carousel creation
                # This tests that the UI properly handles long callback data
                assert byte_length > 64  # Confirm it would exceed limit
    
    @patch('src.bot.ui.load_trending_majors')
    def test_carousel_filters_long_callback_data(self, mock_load_trending):
        """Test that carousel filters out majors with callback data > 64 bytes"""
        # Create data with one major that would exceed callback limit
        test_data = [
            {
                "major_id": "short",
                "name_en": "Short Name",
                "name_kh": "ឈ្មោះខ្លី"
            },
            {
                "major_id": "very-long-major-name-that-would-definitely-exceed-the-64-byte-limit-for-telegram-callback-data",
                "name_en": "Very Long Name",
                "name_kh": "ឈ្មោះវែង"
            }
        ]
        mock_load_trending.return_value = test_data
        
        result = create_trending_carousel('en')
        
        # Should only include the short one
        assert len(result) == 1
        assert result[0][0].callback_data == "TREND_short"
    
    def test_trend_callback_pattern_uniqueness(self):
        """Test that TREND_ patterns don't conflict with other patterns"""
        # TREND_ should not conflict with other callback patterns
        other_patterns = ["SEC_", "DET_", "QS_", "FB_", "SAVE_", "REMOVE_"]
        
        trend_pattern = "TREND_"
        
        for pattern in other_patterns:
            assert not trend_pattern.startswith(pattern)
            assert not pattern.startswith(trend_pattern)


class TestTrendingCallback:
    """Test trending callback handler"""
    
    @pytest.mark.asyncio
    @patch('src.core.data_loader.load_raw')
    async def test_trending_callback_success(self, mock_load_raw, mock_update, mock_context, sample_programmes):
        """Test successful trending callback handling"""
        mock_update.callback_query.data = "TREND_computer-science"
        mock_load_raw.return_value = sample_programmes
        
        await trending_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Should show programme details
        call_args = mock_update.callback_query.edit_message_text.call_args
        assert 'text' in call_args.kwargs
        assert 'reply_markup' in call_args.kwargs
        assert 'parse_mode' in call_args.kwargs
    
    @pytest.mark.asyncio
    @patch('src.core.data_loader.load_raw')
    async def test_trending_callback_nonexistent_major(self, mock_load_raw, mock_update, mock_context, sample_programmes):
        """Test trending callback with nonexistent major"""
        mock_update.callback_query.data = "TREND_nonexistent-major"
        mock_load_raw.return_value = sample_programmes
        
        await trending_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Should show error message
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.args[0] if call_args.args else call_args.kwargs.get('text', '')
        assert "❌" in message_text
    
    @pytest.mark.asyncio
    @patch('src.core.data_loader.load_raw')
    async def test_trending_callback_data_loading_error(self, mock_load_raw, mock_update, mock_context):
        """Test trending callback when data loading fails"""
        mock_update.callback_query.data = "TREND_computer-science"
        mock_load_raw.side_effect = Exception("Data loading error")
        
        await trending_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Should show error message
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.args[0] if call_args.args else call_args.kwargs.get('text', '')
        assert "❌" in message_text
    
    @pytest.mark.asyncio
    async def test_trending_callback_invalid_pattern(self, mock_update, mock_context):
        """Test trending callback with invalid callback data pattern"""
        mock_update.callback_query.data = "INVALID_PATTERN"
        
        await trending_callback(mock_update, mock_context)
        
        # Should answer callback query but not edit message
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_not_called()


class TestTrendingIntegration:
    """Test trending integration with home screen"""
    
    @patch('src.bot.ui.create_trending_carousel')
    def test_home_screen_trending_integration(self, mock_create_carousel):
        """Test that home screen integrates trending carousel correctly"""
        from src.bot.ui import create_home_screen
        
        # Mock trending carousel
        mock_carousel = [
            [
                InlineKeyboardButton("🔥 Computer Science", callback_data="TREND_computer-science"),
                InlineKeyboardButton("🔥 Business", callback_data="TREND_business")
            ]
        ]
        mock_create_carousel.return_value = mock_carousel
        
        message_text, reply_markup = create_home_screen('en')
        
        # Should include trending section in message
        assert "Trending" in message_text or "🔥" in message_text
        
        # Should include trending buttons in keyboard
        keyboard_rows = reply_markup.inline_keyboard
        
        # Find trending row
        trending_found = False
        for row in keyboard_rows:
            for button in row:
                if button.callback_data and button.callback_data.startswith("TREND_"):
                    trending_found = True
                    break
        
        assert trending_found, "Trending buttons not found in home screen keyboard"
    
    @patch('src.bot.ui.create_trending_carousel')
    def test_home_screen_no_trending_fallback(self, mock_create_carousel):
        """Test home screen when no trending data is available"""
        from src.bot.ui import create_home_screen
        
        # Mock empty trending carousel
        mock_create_carousel.return_value = []
        
        message_text, reply_markup = create_home_screen('en')
        
        # Should not include trending section when empty
        keyboard_rows = reply_markup.inline_keyboard
        
        # Should not have trending buttons
        for row in keyboard_rows:
            for button in row:
                assert not (button.callback_data and button.callback_data.startswith("TREND_"))
    
    def test_trending_carousel_button_structure(self):
        """Test that trending carousel buttons have correct structure"""
        sample_data = [
            {"major_id": "cs", "name_en": "CS", "name_kh": "CS"},
            {"major_id": "biz", "name_en": "Business", "name_kh": "អាជីវកម្ម"}
        ]
        
        with patch('src.bot.ui.load_trending_majors', return_value=sample_data):
            result = create_trending_carousel('en')
        
        assert len(result) == 1  # 2 items = 1 row
        assert len(result[0]) == 2  # 2 buttons in row
        
        # Check button structure
        for button in result[0]:
            assert isinstance(button, InlineKeyboardButton)
            assert button.text.startswith("🔥")
            assert button.callback_data.startswith("TREND_")
            assert len(button.callback_data.encode('utf-8')) <= 64
