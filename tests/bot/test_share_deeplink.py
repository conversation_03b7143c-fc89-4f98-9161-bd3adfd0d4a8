"""
Tests for share deep-link functionality
Tests /start major_{id} parsing and security validation
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from telegram import Update, Message
from telegram.ext import ContextTypes

from src.bot.handlers import share_start_handler


@pytest.fixture
def mock_update():
    """Create mock update with message"""
    update = Mock(spec=Update)
    update.message = Mock(spec=Message)
    update.message.reply_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Create mock context with user data"""
    context = Mock(spec=ContextTypes.DEFAULT_TYPE)
    user_data = {
        'language': 'en',
        'shortlist': [],
        'last_recs': {}
    }
    context.user_data = user_data
    return context


@pytest.fixture
def sample_programmes():
    """Sample programme data for testing"""
    return [
        {
            'major_id': 'computer-science',
            'major_name_en': 'Computer Science',
            'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
            'university_name_en': 'Royal University of Phnom Penh',
            'university_name_kh': 'សាកលវិទ្យាល័យភូមិន្ទ',
            'degree_type': 'Bachelor',
            'field_tag': 'STEM'
        },
        {
            'major_id': 'business-management',
            'major_name_en': 'Business Management',
            'major_name_kh': 'គ្រប់គ្រងអាជីវកម្ម',
            'university_name_en': 'University of Commerce',
            'university_name_kh': 'សាកលវិទ្យាល័យពាណិជ្ជសាស្ត្រ',
            'degree_type': 'Bachelor',
            'field_tag': 'Business'
        }
    ]


class TestShareDeepLinkParsing:
    """Test deep-link parsing functionality"""
    
    @pytest.mark.asyncio
    @patch('src.core.data_loader.load_raw')
    async def test_valid_major_id_parsing(self, mock_load_raw, mock_update, mock_context, sample_programmes):
        """Test parsing valid major_id from /start major_{id}"""
        mock_update.message.text = "/start major_computer-science"
        mock_load_raw.return_value = sample_programmes
        
        await share_start_handler(mock_update, mock_context)
        
        # Should call reply_text with programme details
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        
        # Should have text, reply_markup, and parse_mode
        assert 'text' in call_args.kwargs
        assert 'reply_markup' in call_args.kwargs
        assert 'parse_mode' in call_args.kwargs
        
        # Text should contain programme information
        message_text = call_args.kwargs['text']
        assert "Computer Science" in message_text or "វិទ្យាសាស្ត្រកុំព្យូទ័រ" in message_text
    
    @pytest.mark.asyncio
    async def test_invalid_parameter_handling(self, mock_update, mock_context):
        """Test handling of invalid start parameters"""
        # Test cases for invalid parameters
        invalid_cases = [
            "/start major_",  # Empty major_id
            "/start major_invalid<script>",  # Script injection attempt
            "/start major_../../../etc/passwd",  # Path traversal attempt
            "/start major_DROP TABLE users;",  # SQL injection attempt
            "/start major_with spaces",  # Spaces not allowed
            "/start major_with@symbols",  # Invalid symbols
        ]
        
        for invalid_text in invalid_cases:
            mock_update.message.text = invalid_text
            mock_update.message.reply_text.reset_mock()
            
            await share_start_handler(mock_update, mock_context)
            
            if invalid_text.endswith("major_"):
                # Empty major_id should be handled gracefully
                continue
            else:
                # Invalid characters should trigger error message
                mock_update.message.reply_text.assert_called_once()
                call_args = mock_update.message.reply_text.call_args
                message_text = call_args.args[0] if call_args.args else call_args.kwargs.get('text', '')
                assert "❌" in message_text
    
    @pytest.mark.asyncio
    @patch('src.core.data_loader.load_raw')
    async def test_nonexistent_major_id(self, mock_load_raw, mock_update, mock_context, sample_programmes):
        """Test handling of nonexistent major_id"""
        mock_update.message.text = "/start major_nonexistent-major"
        mock_load_raw.return_value = sample_programmes
        
        await share_start_handler(mock_update, mock_context)
        
        # Should show error message
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        message_text = call_args.args[0] if call_args.args else call_args.kwargs.get('text', '')
        assert "❌" in message_text
        assert "not found" in message_text.lower() or "រកមិនឃើញ" in message_text


class TestSecurityValidation:
    """Test security validation for deep-link parameters"""
    
    def test_major_id_regex_validation(self):
        """Test that major_id regex validation works correctly"""
        import re
        
        # Valid major_ids
        valid_ids = [
            "computer-science",
            "business_management",
            "civil-engineering",
            "math123",
            "CS_2024",
            "program-name-with-dashes"
        ]
        
        # Invalid major_ids (should be rejected)
        invalid_ids = [
            "major with spaces",
            "major@symbol",
            "major<script>",
            "major/path",
            "major\\path",
            "major;command",
            "major|pipe",
            "major&command",
            "major$(command)",
            "../../../etc/passwd"
        ]
        
        # Test regex pattern used in share_start_handler
        pattern = r'^[a-zA-Z0-9_-]+$'
        
        for valid_id in valid_ids:
            assert re.match(pattern, valid_id), f"Valid ID rejected: {valid_id}"
        
        for invalid_id in invalid_ids:
            assert not re.match(pattern, invalid_id), f"Invalid ID accepted: {invalid_id}"
    
    @pytest.mark.asyncio
    async def test_injection_attack_prevention(self, mock_update, mock_context):
        """Test prevention of various injection attacks"""
        injection_attempts = [
            "/start major_'; DROP TABLE programmes; --",
            "/start major_<script>alert('xss')</script>",
            "/start major_$(rm -rf /)",
            "/start major_`whoami`",
            "/start major_../../../etc/passwd",
            "/start major_file:///etc/passwd",
            "/start major_javascript:alert(1)"
        ]
        
        for injection in injection_attempts:
            mock_update.message.text = injection
            mock_update.message.reply_text.reset_mock()
            
            await share_start_handler(mock_update, mock_context)
            
            # Should either show error or do nothing (not crash)
            # If reply_text was called, it should be an error message
            if mock_update.message.reply_text.called:
                call_args = mock_update.message.reply_text.call_args
                message_text = call_args.args[0] if call_args.args else call_args.kwargs.get('text', '')
                assert "❌" in message_text, f"Injection attempt not properly handled: {injection}"
    
    def test_major_id_length_limits(self):
        """Test that major_id length is reasonable"""
        import re
        
        pattern = r'^[a-zA-Z0-9_-]+$'
        
        # Test reasonable length limits
        max_reasonable_length = 50
        
        # Very long but valid characters
        long_id = "a" * 100
        assert re.match(pattern, long_id), "Long valid ID should match pattern"
        
        # But we should consider length limits in practice
        reasonable_id = "computer-science-bachelor-degree-2024"
        assert len(reasonable_id) < max_reasonable_length
        assert re.match(pattern, reasonable_id)


class TestDeepLinkIntegration:
    """Test deep-link integration with UI components"""
    
    @pytest.mark.asyncio
    @patch('src.core.data_loader.load_raw')
    @patch('src.bot.ui.render_section')
    async def test_render_section_integration(self, mock_render_section, mock_load_raw, 
                                            mock_update, mock_context, sample_programmes):
        """Test integration with render_section function"""
        mock_update.message.text = "/start major_computer-science"
        mock_load_raw.return_value = sample_programmes
        
        # Mock render_section return value
        mock_render_section.return_value = (
            "Test programme details",
            Mock()  # Mock InlineKeyboardMarkup
        )
        
        await share_start_handler(mock_update, mock_context)
        
        # Should call render_section with correct parameters
        mock_render_section.assert_called_once()
        call_args = mock_render_section.call_args
        
        # First arg should be the programme
        programme = call_args[0][0]
        assert programme['major_id'] == 'computer-science'
        
        # Second arg should be section index (0 for overview)
        section_index = call_args[0][1]
        assert section_index == 0
        
        # Third arg should be language
        language = call_args[0][2]
        assert language in ['en', 'kh']
    
    @pytest.mark.asyncio
    @patch('src.core.data_loader.load_raw')
    async def test_error_handling_integration(self, mock_load_raw, mock_update, mock_context):
        """Test error handling when data loading fails"""
        mock_update.message.text = "/start major_computer-science"
        mock_load_raw.side_effect = Exception("Data loading error")
        
        await share_start_handler(mock_update, mock_context)
        
        # Should show error message
        mock_update.message.reply_text.assert_called_once()
        call_args = mock_update.message.reply_text.call_args
        message_text = call_args.args[0] if call_args.args else call_args.kwargs.get('text', '')
        assert "❌" in message_text
    
    @pytest.mark.asyncio
    async def test_non_start_message_handling(self, mock_update, mock_context):
        """Test that non-start messages are ignored"""
        # Test various non-start messages
        non_start_messages = [
            "Hello",
            "/help",
            "/quiz",
            "major_computer-science",  # Missing /start
            "/start",  # Just /start without parameters
            "/start hello"  # /start with non-major parameter
        ]
        
        for message_text in non_start_messages:
            mock_update.message.text = message_text
            mock_update.message.reply_text.reset_mock()
            
            await share_start_handler(mock_update, mock_context)
            
            # Should not call reply_text for non-matching messages
            mock_update.message.reply_text.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_missing_message_handling(self, mock_context):
        """Test handling when update has no message"""
        update = Mock(spec=Update)
        update.message = None
        
        # Should not crash
        await share_start_handler(update, mock_context)
        
        # No assertions needed - just shouldn't crash


class TestShareURLGeneration:
    """Test share URL generation in UI components"""
    
    def test_share_url_format(self):
        """Test that share URLs are generated in correct format"""
        bot_username = "teslajds1bot"
        major_id = "computer-science"
        
        expected_url = f"https://t.me/{bot_username}?start=major_{major_id}"
        
        # This would be tested in the UI component that generates share buttons
        # The format should match what share_start_handler expects
        assert expected_url == f"https://t.me/{bot_username}?start=major_{major_id}"
    
    def test_share_url_major_id_encoding(self):
        """Test that major_ids in share URLs are properly encoded"""
        # Test various major_id formats
        major_ids = [
            "computer-science",
            "business_management", 
            "civil-engineering",
            "math123"
        ]
        
        bot_username = "teslajds1bot"
        
        for major_id in major_ids:
            share_url = f"https://t.me/{bot_username}?start=major_{major_id}"
            
            # Extract major_id from URL
            if "?start=major_" in share_url:
                extracted_id = share_url.split("?start=major_")[1]
                assert extracted_id == major_id
    
    def test_callback_data_vs_share_url_consistency(self):
        """Test consistency between callback data and share URL formats"""
        major_id = "computer-science"
        
        # Callback data format (used in buttons)
        callback_data = f"DET_{major_id}"
        
        # Share URL format
        share_url_param = f"major_{major_id}"
        
        # Both should reference the same major_id
        callback_major_id = callback_data[4:]  # Remove "DET_"
        share_major_id = share_url_param[6:]   # Remove "major_"
        
        assert callback_major_id == share_major_id == major_id
