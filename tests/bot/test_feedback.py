"""
Tests for feedback functionality (FB_UP/FB_DOWN patterns)
Tests metric logging and user feedback flow
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from telegram import Update, CallbackQuery
from telegram.ext import ContextTypes

from src.bot.handlers import feedback_callback


@pytest.fixture
def mock_update():
    """Create mock update with callback query"""
    update = Mock(spec=Update)
    update.callback_query = Mock(spec=CallbackQuery)
    update.callback_query.answer = AsyncMock()
    update.callback_query.edit_message_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Create mock context with user data"""
    context = Mock(spec=ContextTypes.DEFAULT_TYPE)
    user_data = {
        'language': 'en',
        'shortlist': [],
        'last_recs': {}
    }
    context.user_data = user_data
    return context


class TestFeedbackCallback:
    """Test feedback callback functionality"""
    
    @pytest.mark.asyncio
    async def test_fb_up_positive_feedback(self, mock_update, mock_context):
        """Test FB_UP positive feedback handling"""
        mock_update.callback_query.data = "FB_UP"
        
        with patch('src.bot.handlers.logger') as mock_logger:
            await feedback_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Check that positive feedback was logged
        mock_logger.info.assert_called_once()
        log_call = mock_logger.info.call_args
        log_message = log_call[0][0]
        assert "positive" in log_message.lower()
        assert "feedback" in log_message.lower()
        
        # Check response message
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "👍" in message_text
        assert "Thanks" in message_text or "អរគុណ" in message_text
        
        # Should remove reply markup (no more buttons)
        assert call_args.kwargs['reply_markup'] is None
    
    @pytest.mark.asyncio
    async def test_fb_down_negative_feedback(self, mock_update, mock_context):
        """Test FB_DOWN negative feedback with follow-up prompt"""
        mock_update.callback_query.data = "FB_DOWN"
        
        with patch('src.bot.handlers.logger') as mock_logger:
            await feedback_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Check that negative feedback was logged
        mock_logger.info.assert_called_once()
        log_call = mock_logger.info.call_args
        log_message = log_call[0][0]
        assert "negative" in log_message.lower()
        assert "feedback" in log_message.lower()
        
        # Check response message includes follow-up question
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "👎" in message_text
        assert "Thanks" in message_text or "អរគុណ" in message_text
        assert "Tell us what was wrong" in message_text or "សូមប្រាប់យើងពីបញ្ហា" in message_text
        
        # Should remove reply markup
        assert call_args.kwargs['reply_markup'] is None
    
    @pytest.mark.asyncio
    async def test_invalid_feedback_pattern(self, mock_update, mock_context):
        """Test handling of invalid FB_ patterns"""
        mock_update.callback_query.data = "FB_INVALID"
        
        await feedback_callback(mock_update, mock_context)
        
        # Should answer callback query but not edit message
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_feedback_logging_error_handling(self, mock_update, mock_context):
        """Test feedback handling when logging fails"""
        mock_update.callback_query.data = "FB_UP"
        
        with patch('src.bot.handlers.logger') as mock_logger:
            mock_logger.info.side_effect = Exception("Logging error")
            mock_logger.error = Mock()  # Mock error logging
            
            await feedback_callback(mock_update, mock_context)
        
        # Should still respond to user even if logging fails
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Should log the error
        mock_logger.error.assert_called_once()


class TestFeedbackLanguageSupport:
    """Test feedback functionality in different languages"""
    
    @pytest.mark.asyncio
    async def test_feedback_english_responses(self, mock_update, mock_context):
        """Test feedback responses in English"""
        mock_context.user_data['language'] = 'en'
        mock_context.user_data['lang'] = 'en'
        
        # Test positive feedback
        mock_update.callback_query.data = "FB_UP"
        await feedback_callback(mock_update, mock_context)
        
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "Thanks for the feedback!" in message_text
        
        # Reset mock
        mock_update.callback_query.edit_message_text.reset_mock()
        
        # Test negative feedback
        mock_update.callback_query.data = "FB_DOWN"
        await feedback_callback(mock_update, mock_context)
        
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "Thanks for the feedback!" in message_text
        assert "Tell us what was wrong?" in message_text
    
    @pytest.mark.asyncio
    async def test_feedback_khmer_responses(self, mock_update, mock_context):
        """Test feedback responses in Khmer"""
        mock_context.user_data['language'] = 'kh'
        mock_context.user_data['lang'] = 'kh'
        
        # Test positive feedback
        mock_update.callback_query.data = "FB_UP"
        await feedback_callback(mock_update, mock_context)
        
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "អរគុណសម្រាប់មតិរបស់អ្នក!" in message_text
        
        # Reset mock
        mock_update.callback_query.edit_message_text.reset_mock()
        
        # Test negative feedback
        mock_update.callback_query.data = "FB_DOWN"
        await feedback_callback(mock_update, mock_context)
        
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "អរគុណសម្រាប់មតិរបស់អ្នក!" in message_text
        assert "សូមប្រាប់យើងពីបញ្ហា?" in message_text


class TestFeedbackMetricsIntegration:
    """Test feedback integration with metrics system"""
    
    @pytest.mark.asyncio
    async def test_metrics_logging_format(self, mock_update, mock_context):
        """Test that feedback is logged in correct format for metrics"""
        mock_update.callback_query.data = "FB_UP"
        
        with patch('src.bot.handlers.logger') as mock_logger:
            await feedback_callback(mock_update, mock_context)
        
        # Check log format
        mock_logger.info.assert_called_once()
        log_call = mock_logger.info.call_args
        log_message = log_call[0][0]
        
        # Should include feedback type and context
        assert "User feedback:" in log_message
        assert "positive" in log_message
        assert "recommendations" in log_message
    
    @pytest.mark.asyncio
    async def test_feedback_event_structure(self, mock_update, mock_context):
        """Test that feedback events have proper structure for analytics"""
        test_cases = [
            ("FB_UP", "positive"),
            ("FB_DOWN", "negative")
        ]
        
        for callback_data, expected_type in test_cases:
            mock_update.callback_query.data = callback_data
            
            with patch('src.bot.handlers.logger') as mock_logger:
                await feedback_callback(mock_update, mock_context)
            
            # Verify log contains expected feedback type
            log_call = mock_logger.info.call_args
            log_message = log_call[0][0]
            assert expected_type in log_message
            
            # Reset for next iteration
            mock_logger.reset_mock()
    
    def test_feedback_callback_data_compliance(self):
        """Test that feedback callback data meets Telegram requirements"""
        feedback_patterns = ["FB_UP", "FB_DOWN"]
        
        for pattern in feedback_patterns:
            # Should be under 64 bytes
            assert len(pattern.encode('utf-8')) <= 64
            
            # Should follow FB_ prefix pattern
            assert pattern.startswith("FB_")
            
            # Should be unique
            assert pattern in ["FB_UP", "FB_DOWN"]
    
    @pytest.mark.asyncio
    async def test_future_metrics_integration_placeholder(self, mock_update, mock_context):
        """Test placeholder for future metrics system integration"""
        mock_update.callback_query.data = "FB_UP"
        
        # This test verifies that the current implementation has a placeholder
        # for future metrics system integration
        await feedback_callback(mock_update, mock_context)
        
        # The current implementation logs to logger
        # Future implementation should integrate with actual metrics system
        # This test ensures the structure is ready for that integration
        assert True  # Placeholder test


class TestFeedbackUIIntegration:
    """Test feedback integration with UI components"""
    
    def test_feedback_buttons_in_render_section(self):
        """Test that feedback buttons are included in programme detail views"""
        from src.bot.ui import render_section
        
        # Mock programme data
        programme = {
            'major_id': 'computer-science',
            'major_name_en': 'Computer Science',
            'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
            'university_name_en': 'Test University',
            'degree_type': 'Bachelor'
        }
        
        message_text, reply_markup = render_section(programme, 0, 'en')
        
        # Should include feedback buttons
        keyboard_rows = reply_markup.inline_keyboard
        
        # Find feedback buttons
        feedback_buttons_found = False
        for row in keyboard_rows:
            for button in row:
                if button.callback_data in ["FB_UP", "FB_DOWN"]:
                    feedback_buttons_found = True
                    break
        
        assert feedback_buttons_found, "Feedback buttons not found in programme detail view"
    
    def test_feedback_button_text_localization(self):
        """Test that feedback buttons show correct text in both languages"""
        from src.bot.ui import render_section
        
        programme = {
            'major_id': 'test',
            'major_name_en': 'Test',
            'major_name_kh': 'តេស្ត',
            'university_name_en': 'Test University'
        }
        
        # Test English
        message_text, reply_markup = render_section(programme, 0, 'en')
        keyboard_rows = reply_markup.inline_keyboard
        
        # Find feedback buttons and check text
        for row in keyboard_rows:
            for button in row:
                if button.callback_data == "FB_UP":
                    assert "👍" in button.text
                    assert "Yes" in button.text
                elif button.callback_data == "FB_DOWN":
                    assert "👎" in button.text
                    assert "No" in button.text
        
        # Test Khmer
        message_text, reply_markup = render_section(programme, 0, 'kh')
        keyboard_rows = reply_markup.inline_keyboard
        
        # Find feedback buttons and check Khmer text
        for row in keyboard_rows:
            for button in row:
                if button.callback_data == "FB_UP":
                    assert "👍" in button.text
                    assert "ចាស" in button.text
                elif button.callback_data == "FB_DOWN":
                    assert "👎" in button.text
                    assert "ទេ" in button.text
    
    def test_feedback_button_placement(self):
        """Test that feedback buttons are placed in correct row"""
        from src.bot.ui import render_section
        
        programme = {
            'major_id': 'test',
            'major_name_en': 'Test',
            'university_name_en': 'Test University'
        }
        
        message_text, reply_markup = render_section(programme, 0, 'en')
        keyboard_rows = reply_markup.inline_keyboard
        
        # Feedback buttons should be in secondary row (with share button)
        feedback_row_found = False
        for row in keyboard_rows:
            fb_up_found = any(btn.callback_data == "FB_UP" for btn in row)
            fb_down_found = any(btn.callback_data == "FB_DOWN" for btn in row)
            share_found = any(btn.url and "start=major_" in btn.url for btn in row)
            
            if fb_up_found and fb_down_found and share_found:
                feedback_row_found = True
                # Should have exactly 3 buttons: share, like, dislike
                assert len(row) == 3
                break
        
        assert feedback_row_found, "Feedback buttons not found in correct row with share button"


class TestFeedbackErrorHandling:
    """Test error handling in feedback functionality"""
    
    @pytest.mark.asyncio
    async def test_callback_query_answer_failure(self, mock_update, mock_context):
        """Test handling when callback query answer fails"""
        mock_update.callback_query.data = "FB_UP"
        mock_update.callback_query.answer.side_effect = Exception("Answer failed")

        # Should not crash (decorator handles gracefully)
        result = await feedback_callback(mock_update, mock_context)

        # Should return None due to error handling
        assert result is None

    @pytest.mark.asyncio
    async def test_message_edit_failure(self, mock_update, mock_context):
        """Test handling when message edit fails"""
        mock_update.callback_query.data = "FB_UP"
        mock_update.callback_query.edit_message_text.side_effect = Exception("Edit failed")

        # Should not crash (decorator handles gracefully)
        result = await feedback_callback(mock_update, mock_context)

        # Should return None due to error handling
        assert result is None
    
    @pytest.mark.asyncio
    async def test_missing_user_data(self, mock_update):
        """Test feedback handling with missing user data"""
        context = Mock(spec=ContextTypes.DEFAULT_TYPE)
        context.user_data = {}  # Empty user data
        
        mock_update.callback_query.data = "FB_UP"
        
        # Should not crash
        await feedback_callback(mock_update, context)
        
        # Should still respond
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_corrupted_callback_data(self, mock_update, mock_context):
        """Test handling of corrupted callback data"""
        corrupted_patterns = [
            "FB_",  # Incomplete
            "FB_INVALID",  # Invalid suffix
            "FB_UP_EXTRA",  # Extra data
            "",  # Empty
            None  # None value
        ]
        
        for pattern in corrupted_patterns:
            mock_update.callback_query.data = pattern
            mock_update.callback_query.answer.reset_mock()
            mock_update.callback_query.edit_message_text.reset_mock()
            
            # Should not crash
            await feedback_callback(mock_update, mock_context)
            
            # Should answer callback query
            mock_update.callback_query.answer.assert_called_once()
