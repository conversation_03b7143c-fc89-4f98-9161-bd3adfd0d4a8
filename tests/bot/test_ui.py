"""
Tests for UI module
"""

import pytest
from src.bot.ui import create_recommendations_view, create_detail_view, get_programme_by_id


@pytest.fixture
def sample_recommendations():
    """Sample recommendations for testing"""
    return [
        {
            'major_id': 'cs-001',
            'major_name_kh': 'វិទ្យាសាស្ត្រកុំព្យូទ័រ',
            'major_name_en': 'Computer Science',
            'university_name_kh': 'សាកលវិទ្យាល័យភូមិន្ទ',
            'university_name_en': 'Bhumindra University',
            'city': 'ភ្នំពេញ',
            'tuition_fees_usd': '400',
            'employment_rate': '90',
            'hybrid_score': 0.85,
            'mcda_score': 0.80,
            'mcda_reason': 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ',
            'contact_url': 'https://example.com/contact',
            'map_url': 'https://maps.google.com/example'
        },
        {
            'major_id': 'eng-001',
            'major_name_kh': 'វិស្វកម្មស៊ីវិល',
            'major_name_en': 'Civil Engineering',
            'university_name_kh': 'វិទ្យាស្ថានបច្ចេកវិទ្យា',
            'university_name_en': 'Institute of Technology',
            'city': 'ភ្នំពេញ',
            'tuition_fees_usd': '600',
            'employment_rate': '85',
            'hybrid_score': 0.75,
            'mcda_score': 0.70,
            'mcda_reason': 'ការវាយតម្លៃតាមលក្ខណៈវិនិច្ឆ័យ'
        }
    ]


class TestRecommendationsView:
    """Test recommendations view creation"""
    
    def test_create_recommendations_view_khmer(self, sample_recommendations):
        """Test creating recommendations view in Khmer"""
        message_text, keyboard = create_recommendations_view(sample_recommendations, 'kh')
        
        # Check message text contains recommendations
        assert 'អនុសាសន៍កម្មវិធីសិក្សាសម្រាប់អ្នក' in message_text
        assert 'វិទ្យាសាស្ត្រកុំព្យូទ័រ' in message_text
        assert 'វិស្វកម្មស៊ីវិល' in message_text
        assert 'ភ្នំពេញ' in message_text
        
        # Check keyboard has correct structure
        assert keyboard.inline_keyboard is not None
        assert len(keyboard.inline_keyboard) >= 3  # Recommendation rows + controls row

        # Check enhanced action buttons for each recommendation
        first_row = keyboard.inline_keyboard[0]
        assert len(first_row) == 2  # More and Compare buttons
        assert first_row[0].callback_data == "DET_cs-001"
        assert first_row[1].callback_data == "CMP_cs-001"

        second_row = keyboard.inline_keyboard[1]
        assert len(second_row) == 2  # More and Compare buttons
        assert second_row[0].callback_data == "DET_eng-001"
        assert second_row[1].callback_data == "CMP_eng-001"

        # Check controls row (last row)
        last_row = keyboard.inline_keyboard[-1]
        assert len(last_row) == 3  # Filters, Refresh, Restart
        assert any(button.callback_data == "FILTERS" for button in last_row)
        assert any(button.callback_data == "REFRESH" for button in last_row)
        assert any(button.callback_data == "RESTART" for button in last_row)
    
    def test_create_recommendations_view_english(self, sample_recommendations):
        """Test creating recommendations view in English"""
        message_text, keyboard = create_recommendations_view(sample_recommendations, 'en')
        
        # Check message text uses English names
        assert 'Computer Science' in message_text
        assert 'Civil Engineering' in message_text
        assert 'Bhumindra University' in message_text
        assert 'Institute of Technology' in message_text
    
    def test_create_recommendations_view_empty(self):
        """Test creating recommendations view with empty list"""
        message_text, keyboard = create_recommendations_view([], 'kh')
        
        assert 'គ្មានអនុសាសន៍' in message_text
        assert len(keyboard.inline_keyboard) == 0


class TestDetailView:
    """Test detail view creation"""
    
    def test_create_detail_view_khmer(self, sample_recommendations):
        """Test creating detail view in Khmer"""
        programme = sample_recommendations[0]
        message_text, keyboard = create_detail_view(programme, 'kh')
        
        # Check message text contains programme details
        assert 'វិទ្យាសាស្ត្រកុំព្យូទ័រ' in message_text
        assert 'សាកលវិទ្យាល័យភូមិន្ទ' in message_text
        assert '400 USD' in message_text
        assert '90%' in message_text
        
        # Check keyboard structure
        assert len(keyboard.inline_keyboard) >= 2
        
        # Check contact and map buttons (first row)
        first_row = keyboard.inline_keyboard[0]
        contact_button = next((btn for btn in first_row if btn.url == 'https://example.com/contact'), None)
        map_button = next((btn for btn in first_row if btn.url == 'https://maps.google.com/example'), None)
        assert contact_button is not None
        assert map_button is not None
        
        # Check navigation buttons (last row)
        last_row = keyboard.inline_keyboard[-1]
        assert any(button.callback_data == "BACK" for button in last_row)
        assert any(button.callback_data == "REFRESH" for button in last_row)
    
    def test_create_detail_view_english(self, sample_recommendations):
        """Test creating detail view in English"""
        programme = sample_recommendations[0]
        message_text, keyboard = create_detail_view(programme, 'en')
        
        # Check message text uses English names
        assert 'Computer Science' in message_text
        assert 'Bhumindra University' in message_text
    
    def test_create_detail_view_no_urls(self, sample_recommendations):
        """Test creating detail view without contact/map URLs"""
        programme = sample_recommendations[1]  # This one has no URLs
        message_text, keyboard = create_detail_view(programme, 'kh')

        # Should still work but without contact/map buttons
        assert 'វិស្វកម្មស៊ីវិល' in message_text

        # Should have action buttons and navigation buttons (no contact/map row)
        assert len(keyboard.inline_keyboard) == 2

        # Check action buttons row
        action_row = keyboard.inline_keyboard[0]
        assert any(button.callback_data == "SAVE_eng-001" for button in action_row)
        assert any(button.callback_data == "REMOVE_eng-001" for button in action_row)

        # Check navigation row
        nav_row = keyboard.inline_keyboard[1]
        assert any(button.callback_data == "BACK" for button in nav_row)
        assert any(button.callback_data == "REFRESH" for button in nav_row)


class TestUtilityFunctions:
    """Test utility functions"""
    
    def test_get_programme_by_id_found(self, sample_recommendations):
        """Test getting programme by ID when it exists"""
        programme = get_programme_by_id('cs-001', sample_recommendations)
        
        assert programme['major_id'] == 'cs-001'
        assert programme['major_name_en'] == 'Computer Science'
    
    def test_get_programme_by_id_not_found(self, sample_recommendations):
        """Test getting programme by ID when it doesn't exist"""
        programme = get_programme_by_id('nonexistent', sample_recommendations)
        
        assert programme == {}
    
    def test_get_programme_by_id_empty_list(self):
        """Test getting programme by ID from empty list"""
        programme = get_programme_by_id('cs-001', [])
        
        assert programme == {}
