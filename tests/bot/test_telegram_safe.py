"""
Tests for Telegram-Safe Operations Module
Comprehensive testing of error handling and safe wrappers for Telegram bot operations
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from telegram.error import BadRequest, TimedOut, RetryAfter, NetworkError
from telegram import InlineKeyboardMarkup, InlineKeyboardButton

from src.bot.telegram_safe import (
    safe_answer_callback,
    safe_edit_message, 
    log_telegram_errors,
    offload_heavy_task,
    validate_callback_pattern,
    CALLBACK_PATTERNS,
    IGNORABLE_ERRORS
)


class TestSafeAnswerCallback:
    """Test safe_answer_callback function"""
    
    @pytest.mark.asyncio
    async def test_successful_answer(self):
        """Test successful callback answer"""
        mock_query = AsyncMock()
        
        result = await safe_answer_callback(mock_query, "Success", show_alert=True)
        
        assert result is True
        mock_query.answer.assert_called_once_with(
            text="Success", 
            show_alert=True, 
            cache_time=1
        )
    
    @pytest.mark.asyncio
    async def test_ignorable_error_handling(self):
        """Test handling of ignorable Telegram errors"""
        mock_query = AsyncMock()
        mock_query.answer.side_effect = BadRequest("Query is too old")
        
        result = await safe_answer_callback(mock_query)
        
        assert result is False
        mock_query.answer.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_serious_badrequest_handling(self):
        """Test handling of serious BadRequest errors"""
        mock_query = AsyncMock()
        mock_query.answer.side_effect = BadRequest("Forbidden: bot was blocked by the user")
        
        result = await safe_answer_callback(mock_query)
        
        assert result is False
        mock_query.answer.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_timeout_error_handling(self):
        """Test handling of timeout errors"""
        mock_query = AsyncMock()
        mock_query.answer.side_effect = TimedOut("Request timed out")
        
        result = await safe_answer_callback(mock_query)
        
        assert result is False
        mock_query.answer.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_unexpected_error_handling(self):
        """Test handling of unexpected errors"""
        mock_query = AsyncMock()
        mock_query.answer.side_effect = ValueError("Unexpected error")
        
        result = await safe_answer_callback(mock_query)
        
        assert result is False
        mock_query.answer.assert_called_once()


class TestSafeEditMessage:
    """Test safe_edit_message function"""
    
    @pytest.mark.asyncio
    async def test_successful_edit(self):
        """Test successful message edit"""
        mock_query = AsyncMock()
        mock_query.message.text = "Old text"
        mock_query.message.reply_markup = None
        
        keyboard = InlineKeyboardMarkup([[InlineKeyboardButton("Test", callback_data="test")]])
        result = await safe_edit_message(mock_query, "New text", keyboard, "Markdown")
        
        assert result is True
        mock_query.edit_message_text.assert_called_once_with(
            text="New text",
            reply_markup=keyboard,
            parse_mode="Markdown"
        )
    
    @pytest.mark.asyncio
    async def test_skip_unchanged_content(self):
        """Test skipping edit when content is unchanged"""
        mock_query = AsyncMock()
        mock_query.message.text = "Same text"
        mock_query.message.reply_markup = None
        
        result = await safe_edit_message(mock_query, "Same text", None)
        
        assert result is True
        mock_query.edit_message_text.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_message_not_modified_error(self):
        """Test handling of 'message is not modified' error"""
        mock_query = AsyncMock()
        mock_query.message.text = "Old text"
        mock_query.message.reply_markup = None
        mock_query.edit_message_text.side_effect = BadRequest("Message is not modified")
        
        result = await safe_edit_message(mock_query, "New text")
        
        assert result is False
        mock_query.edit_message_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_serious_edit_error(self):
        """Test handling of serious edit errors"""
        mock_query = AsyncMock()
        mock_query.message.text = "Old text"
        mock_query.message.reply_markup = None
        mock_query.edit_message_text.side_effect = BadRequest("Message to edit not found")
        
        result = await safe_edit_message(mock_query, "New text")
        
        assert result is False
        mock_query.edit_message_text.assert_called_once()


class TestLogTelegramErrors:
    """Test log_telegram_errors decorator"""
    
    @pytest.mark.asyncio
    async def test_successful_function_execution(self):
        """Test decorator with successful function execution"""
        import logging
        logger = logging.getLogger("test")
        
        @log_telegram_errors(logger)
        async def test_func(update, context):
            return "success"
        
        mock_update = MagicMock()
        mock_context = MagicMock()
        
        result = await test_func(mock_update, mock_context)
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_ignorable_error_handling(self):
        """Test decorator handling of ignorable errors"""
        import logging
        logger = logging.getLogger("test")
        
        @log_telegram_errors(logger)
        async def test_func(update, context):
            raise BadRequest("Query is too old")
        
        mock_update = MagicMock()
        mock_context = MagicMock()
        
        result = await test_func(mock_update, mock_context)
        assert result is None
    
    @pytest.mark.asyncio
    async def test_serious_error_with_notification(self):
        """Test decorator handling of serious errors with user notification"""
        import logging
        logger = logging.getLogger("test")
        
        @log_telegram_errors(logger)
        async def test_func(update, context):
            raise BadRequest("Forbidden: bot was blocked")
        
        mock_update = MagicMock()
        mock_update.callback_query = AsyncMock()
        mock_context = MagicMock()
        
        with patch('src.bot.telegram_safe.safe_answer_callback') as mock_safe_answer:
            result = await test_func(mock_update, mock_context)
            
        assert result is None
        mock_safe_answer.assert_called_once()


class TestOffloadHeavyTask:
    """Test offload_heavy_task function"""
    
    @pytest.mark.asyncio
    async def test_successful_task_creation(self):
        """Test successful background task creation"""
        async def dummy_coro():
            await asyncio.sleep(0.01)
            return "completed"
        
        with patch('asyncio.create_task') as mock_create_task:
            offload_heavy_task(dummy_coro())
            mock_create_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_task_creation_error(self):
        """Test handling of task creation errors"""
        async def dummy_coro():
            return "test"
        
        with patch('asyncio.create_task', side_effect=RuntimeError("Task creation failed")):
            # Should not raise exception
            offload_heavy_task(dummy_coro())


class TestCallbackPatternValidation:
    """Test callback pattern validation"""
    
    @pytest.mark.parametrize("callback_data,expected", [
        ("answer_location_preference_pp", True),
        ("answer_budget_range_low", True),
        ("FILTER_city", True),
        ("FILTERS_HOME", True),
        ("BACK", True),
        ("HOME", True),
        ("RESTART", True),
        ("DET_123", True),
        ("SEC_456_2", True),
        ("QS_SURPRISE", True),
        ("QS_PP", True),
        ("TREND_789", True),
        ("FB_UP", True),
        ("FB_DOWN", True),
        ("MORE_123", True),
        ("SAVE_456", True),
        ("REMOVE_789", True),
        ("CMP_123", True),
        ("lang_kh", True),
        ("lang_en", True),
        ("WIZARD_START", True),
        ("invalid_pattern", False),
        ("UNKNOWN_CALLBACK", False),
        ("", False)
    ])
    def test_callback_pattern_validation(self, callback_data, expected):
        """Test validation of various callback patterns"""
        result = validate_callback_pattern(callback_data)
        assert result == expected
    
    def test_all_patterns_compile(self):
        """Test that all regex patterns compile successfully"""
        import re
        
        for pattern_name, pattern in CALLBACK_PATTERNS.items():
            try:
                re.compile(pattern)
            except re.error as e:
                pytest.fail(f"Pattern '{pattern_name}' failed to compile: {e}")


class TestIgnorableErrors:
    """Test ignorable error patterns"""
    
    def test_ignorable_error_coverage(self):
        """Test that all common ignorable errors are covered"""
        test_errors = [
            "message is not modified",
            "query is too old",
            "response timeout expired",
            "message can't be edited",
            "message to edit not found",
            "bad request: message is not modified",
            "bad request: query is too old"
        ]
        
        for error_msg in test_errors:
            assert any(ignorable in error_msg.lower() for ignorable in IGNORABLE_ERRORS), \
                f"Error '{error_msg}' not covered by IGNORABLE_ERRORS"


@pytest.mark.asyncio
async def test_integration_safe_operations():
    """Integration test for safe operations working together"""
    mock_query = AsyncMock()
    mock_query.message.text = "Old text"
    mock_query.message.reply_markup = None
    
    # Test successful workflow
    answer_result = await safe_answer_callback(mock_query, "Processing...")
    edit_result = await safe_edit_message(mock_query, "New text")
    
    assert answer_result is True
    assert edit_result is True
    
    mock_query.answer.assert_called_once()
    mock_query.edit_message_text.assert_called_once()
