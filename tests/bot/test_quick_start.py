"""
Tests for quick start functionality (QS_* patterns)
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from telegram import Update, CallbackQuery, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from src.bot.handlers import quick_start_callback


@pytest.fixture
def mock_update():
    """Create mock update with callback query"""
    update = Mock(spec=Update)
    update.callback_query = Mock(spec=CallbackQuery)
    update.callback_query.answer = AsyncMock()
    update.callback_query.edit_message_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Create mock context with user data"""
    context = Mock(spec=ContextTypes.DEFAULT_TYPE)
    user_data = {
        'language': 'en',
        'shortlist': [],
        'last_recs': {}
    }
    context.user_data = user_data
    return context


class TestQuickStartCallbacks:
    """Test quick start callback functionality"""
    
    @pytest.mark.asyncio
    @patch('src.core.hybrid_recommender.get_recommendations')
    async def test_qs_surprise_success(self, mock_get_recs, mock_update, mock_context):
        """Test QS_SURPRISE generates random recommendations"""
        mock_update.callback_query.data = "QS_SURPRISE"
        
        # Mock recommendations
        mock_recommendations = [
            {
                'major_id': 'computer-science',
                'major_name_en': 'Computer Science',
                'hybrid_score': 0.85
            }
        ]
        mock_get_recs.return_value = mock_recommendations
        
        await quick_start_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Check that recommendations were generated with random answers
        mock_get_recs.assert_called_once()
        call_args = mock_get_recs.call_args
        user_answers = call_args[0][0]
        
        # Verify random answers were generated
        expected_keys = ['location_preference', 'budget_range', 'field_of_interest', 'career_goal', 'scholarship_need']
        for key in expected_keys:
            assert key in user_answers
        
        # Check that surprise banner was added
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "🎲" in message_text
        assert "Random Recommendations" in message_text or "ការណែនាំចៃដន្យ" in message_text
    
    @pytest.mark.asyncio
    @patch('src.core.hybrid_recommender.get_recommendations')
    async def test_qs_pp_phnom_penh_focus(self, mock_get_recs, mock_update, mock_context):
        """Test QS_PP focuses on Phnom Penh programs"""
        mock_update.callback_query.data = "QS_PP"
        
        # Mock recommendations
        mock_recommendations = [
            {
                'major_id': 'computer-science',
                'major_name_en': 'Computer Science',
                'hybrid_score': 0.85
            }
        ]
        mock_get_recs.return_value = mock_recommendations
        
        await quick_start_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Check that recommendations were generated with PP focus
        mock_get_recs.assert_called_once()
        call_args = mock_get_recs.call_args
        user_answers = call_args[0][0]
        
        # Verify Phnom Penh focus
        assert user_answers['location_preference'] == 'pp'
        assert user_answers['field_of_interest'] == 'stem'
        assert user_answers['career_goal'] == 'tech'
        
        # Check that PP banner was added
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "🏛️" in message_text
    
    @pytest.mark.asyncio
    @patch('src.core.hybrid_recommender.get_recommendations')
    async def test_qs_bud_low_budget_focus(self, mock_get_recs, mock_update, mock_context):
        """Test QS_BUD_LOW focuses on low-budget programs"""
        mock_update.callback_query.data = "QS_BUD_LOW"
        
        # Mock recommendations
        mock_recommendations = [
            {
                'major_id': 'mathematics',
                'major_name_en': 'Mathematics',
                'hybrid_score': 0.78
            }
        ]
        mock_get_recs.return_value = mock_recommendations
        
        await quick_start_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Check that recommendations were generated with budget focus
        mock_get_recs.assert_called_once()
        call_args = mock_get_recs.call_args
        user_answers = call_args[0][0]
        
        # Verify low budget focus
        assert user_answers['budget_range'] == 'low'
        assert user_answers['scholarship_need'] == 'yes'
        assert user_answers['location_preference'] == 'any'
        
        # Check that budget banner was added
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "💰" in message_text
    
    @pytest.mark.asyncio
    @patch('src.core.hybrid_recommender.get_recommendations')
    async def test_quick_start_error_handling(self, mock_get_recs, mock_update, mock_context):
        """Test quick start error handling"""
        mock_update.callback_query.data = "QS_SURPRISE"
        mock_get_recs.side_effect = Exception("Test error")
        
        await quick_start_callback(mock_update, mock_context)
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Check that error message was shown
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert "❌" in message_text
        assert "Error" in message_text or "កំហុស" in message_text


class TestQuickStartDataValidation:
    """Test quick start data validation and caching"""
    
    @pytest.mark.asyncio
    @patch('src.core.hybrid_recommender.get_recommendations')
    @patch('src.bot.handlers.cache_recommendations')
    @patch('src.bot.handlers.cache_user_answers')
    async def test_caching_functionality(self, mock_cache_answers, mock_cache_recs, 
                                       mock_get_recs, mock_update, mock_context):
        """Test that quick start properly caches results"""
        mock_update.callback_query.data = "QS_SURPRISE"
        
        mock_recommendations = [{'major_id': 'test', 'hybrid_score': 0.5}]
        mock_get_recs.return_value = mock_recommendations
        
        await quick_start_callback(mock_update, mock_context)
        
        # Verify caching was called
        mock_cache_recs.assert_called_once_with(mock_recommendations, mock_context)
        mock_cache_answers.assert_called_once()
        
        # Check that user answers were cached
        cached_answers = mock_cache_answers.call_args[0][0]
        assert isinstance(cached_answers, dict)
        assert 'location_preference' in cached_answers
    
    def test_preset_answer_patterns(self):
        """Test that preset answer patterns are correct"""
        # Test QS_PP pattern
        pp_answers = {
            'location_preference': 'pp',
            'budget_range': 'mid',
            'field_of_interest': 'stem',
            'career_goal': 'tech',
            'scholarship_need': 'no'
        }
        
        # Verify all required keys are present
        required_keys = ['location_preference', 'budget_range', 'field_of_interest', 'career_goal', 'scholarship_need']
        for key in required_keys:
            assert key in pp_answers
        
        # Test QS_BUD_LOW pattern
        budget_answers = {
            'location_preference': 'any',
            'budget_range': 'low',
            'field_of_interest': 'any',
            'career_goal': 'any',
            'scholarship_need': 'yes'
        }
        
        for key in required_keys:
            assert key in budget_answers
        
        # Verify budget focus
        assert budget_answers['budget_range'] == 'low'
        assert budget_answers['scholarship_need'] == 'yes'


class TestCallbackDataCompliance:
    """Test callback data compliance with Telegram limits"""
    
    def test_callback_data_length(self):
        """Test that all QS_* callback data stays under 64 bytes"""
        callback_patterns = [
            "QS_SURPRISE",
            "QS_PP", 
            "QS_BUD_LOW"
        ]
        
        for pattern in callback_patterns:
            assert len(pattern.encode('utf-8')) <= 64, f"Callback data too long: {pattern}"
    
    def test_callback_pattern_uniqueness(self):
        """Test that callback patterns are unique and don't conflict"""
        patterns = ["QS_SURPRISE", "QS_PP", "QS_BUD_LOW"]
        
        # Check uniqueness
        assert len(patterns) == len(set(patterns))
        
        # Check they all start with QS_
        for pattern in patterns:
            assert pattern.startswith("QS_")
    
    @pytest.mark.asyncio
    async def test_unknown_qs_pattern_handling(self, mock_update, mock_context):
        """Test handling of unknown QS_ patterns"""
        mock_update.callback_query.data = "QS_UNKNOWN"
        
        # Should not crash, but also should not generate recommendations
        await quick_start_callback(mock_update, mock_context)
        
        # Should still answer the callback query
        mock_update.callback_query.answer.assert_called_once()


class TestPerformanceRequirements:
    """Test performance requirements for quick start"""
    
    @pytest.mark.asyncio
    @patch('src.core.hybrid_recommender.get_recommendations')
    async def test_response_time_requirement(self, mock_get_recs, mock_update, mock_context):
        """Test that quick start responds within time requirements"""
        import time
        
        mock_update.callback_query.data = "QS_SURPRISE"
        mock_get_recs.return_value = [{'major_id': 'test', 'hybrid_score': 0.5}]
        
        start_time = time.time()
        await quick_start_callback(mock_update, mock_context)
        end_time = time.time()
        
        # Should complete within 2 seconds (generous for testing)
        assert (end_time - start_time) < 2.0
        
        # Should have called the UI update
        mock_update.callback_query.edit_message_text.assert_called_once()
    
    def test_preset_answers_efficiency(self):
        """Test that preset answers are efficiently structured"""
        # All preset patterns should have exactly 5 keys for consistency
        required_keys = {'location_preference', 'budget_range', 'field_of_interest', 'career_goal', 'scholarship_need'}
        
        # Test each pattern would have the right structure
        patterns = ['QS_SURPRISE', 'QS_PP', 'QS_BUD_LOW']
        
        for pattern in patterns:
            # Each pattern should map to a complete set of answers
            # (This is tested implicitly in the callback tests above)
            assert len(required_keys) == 5  # Verify our test assumption
