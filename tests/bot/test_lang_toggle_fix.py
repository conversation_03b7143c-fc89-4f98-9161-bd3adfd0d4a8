"""
Tests for language toggle fix (CRITICAL BUG FIX)
Tests that language toggle updates both 'language' and 'lang' keys for system compatibility
"""
import pytest
from unittest.mock import Mock, AsyncMock
from telegram import Update, CallbackQuery, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from src.bot.handlers import language_toggle_callback, get_lang


@pytest.fixture
def mock_update():
    """Create mock update with callback query"""
    update = Mock(spec=Update)
    update.callback_query = Mock(spec=CallbackQuery)
    update.callback_query.answer = AsyncMock()
    update.callback_query.edit_message_text = AsyncMock()
    return update


@pytest.fixture
def mock_context():
    """Create mock context with user data"""
    context = Mock(spec=ContextTypes.DEFAULT_TYPE)
    user_data = {
        'language': 'en',
        'shortlist': [],
        'last_recs': {}
    }
    context.user_data = user_data
    return context


class TestLanguageToggleFix:
    """Test the critical language toggle bug fix"""
    
    @pytest.mark.asyncio
    async def test_english_to_khmer_toggle(self, mock_update, mock_context):
        """Test language toggle from English to Khmer updates both keys"""
        # Start with English
        mock_context.user_data['language'] = 'en'
        mock_context.user_data['lang'] = 'en'
        
        await language_toggle_callback(mock_update, mock_context)
        
        # CRITICAL FIX: Both keys should be updated to Khmer
        assert mock_context.user_data['language'] == 'kh'
        assert mock_context.user_data['lang'] == 'kh'
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Check that home screen was shown in new language
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert isinstance(call_args.kwargs['reply_markup'], InlineKeyboardMarkup)
    
    @pytest.mark.asyncio
    async def test_khmer_to_english_toggle(self, mock_update, mock_context):
        """Test language toggle from Khmer to English updates both keys"""
        # Start with Khmer
        mock_context.user_data['language'] = 'kh'
        mock_context.user_data['lang'] = 'kh'
        
        await language_toggle_callback(mock_update, mock_context)
        
        # CRITICAL FIX: Both keys should be updated to English
        assert mock_context.user_data['language'] == 'en'
        assert mock_context.user_data['lang'] == 'en'
        
        mock_update.callback_query.answer.assert_called_once()
        mock_update.callback_query.edit_message_text.assert_called_once()
        
        # Check that home screen was shown in new language
        call_args = mock_update.callback_query.edit_message_text.call_args
        message_text = call_args.kwargs['text']
        assert isinstance(call_args.kwargs['reply_markup'], InlineKeyboardMarkup)
    
    @pytest.mark.asyncio
    async def test_toggle_persistence_across_screens(self, mock_update, mock_context):
        """Test that language toggle persists across different screens"""
        # Start with English
        mock_context.user_data['language'] = 'en'
        mock_context.user_data['lang'] = 'en'
        
        # Toggle to Khmer
        await language_toggle_callback(mock_update, mock_context)
        
        # Verify both keys are updated
        assert mock_context.user_data['language'] == 'kh'
        assert mock_context.user_data['lang'] == 'kh'
        
        # Simulate navigation to another screen
        # The get_lang function should now return 'kh' consistently
        current_lang = get_lang(mock_context.user_data)
        assert current_lang == 'kh'
        
        # Toggle back to English
        await language_toggle_callback(mock_update, mock_context)
        
        # Verify both keys are updated again
        assert mock_context.user_data['language'] == 'en'
        assert mock_context.user_data['lang'] == 'en'
        
        # get_lang should now return 'en'
        current_lang = get_lang(mock_context.user_data)
        assert current_lang == 'en'


class TestGetLangFunction:
    """Test the enhanced get_lang function compatibility"""
    
    def test_get_lang_with_language_key(self):
        """Test get_lang works with 'language' key (used by handlers)"""
        user_data = {'language': 'en'}
        result = get_lang(user_data)
        assert result == 'en'
        
        user_data = {'language': 'kh'}
        result = get_lang(user_data)
        assert result == 'kh'
    
    def test_get_lang_with_lang_key(self):
        """Test get_lang works with 'lang' key (used by i18n system)"""
        user_data = {'lang': 'en'}
        result = get_lang(user_data)
        assert result == 'en'
        
        user_data = {'lang': 'kh'}
        result = get_lang(user_data)
        assert result == 'kh'
    
    def test_get_lang_with_both_keys(self):
        """Test get_lang prioritizes 'language' key when both exist"""
        user_data = {'language': 'en', 'lang': 'kh'}
        result = get_lang(user_data)
        assert result == 'en'  # Should prioritize 'language' key
    
    def test_get_lang_fallback_to_khmer(self):
        """Test get_lang falls back to Khmer when no valid keys exist"""
        user_data = {}
        result = get_lang(user_data)
        assert result == 'kh'
        
        user_data = {'language': 'invalid'}
        result = get_lang(user_data)
        assert result == 'kh'
        
        user_data = {'lang': 'invalid'}
        result = get_lang(user_data)
        assert result == 'kh'
    
    def test_get_lang_invalid_values(self):
        """Test get_lang handles invalid language values gracefully"""
        invalid_values = ['fr', 'es', 'zh', '', None, 123, []]
        
        for invalid_value in invalid_values:
            user_data = {'language': invalid_value}
            result = get_lang(user_data)
            assert result == 'kh', f"Failed for invalid value: {invalid_value}"


class TestLanguageSystemCompatibility:
    """Test compatibility between different language systems"""
    
    def test_handler_language_storage(self):
        """Test that handlers store language correctly"""
        user_data = {}
        
        # Simulate handler storing language
        user_data['language'] = 'en'
        
        # get_lang should work
        assert get_lang(user_data) == 'en'
    
    def test_i18n_system_compatibility(self):
        """Test compatibility with i18n system expectations"""
        user_data = {}
        
        # Simulate i18n system storing language
        user_data['lang'] = 'kh'
        
        # get_lang should work
        assert get_lang(user_data) == 'kh'
    
    def test_cross_system_updates(self):
        """Test that updates work across both systems"""
        user_data = {'language': 'en'}
        
        # Simulate language toggle updating both keys
        current_lang = get_lang(user_data)
        new_lang = 'kh' if current_lang == 'en' else 'en'
        
        # Update both keys (as fixed language_toggle_callback does)
        user_data['language'] = new_lang
        user_data['lang'] = new_lang
        
        # Both systems should see the update
        assert get_lang(user_data) == new_lang
        assert user_data['language'] == new_lang
        assert user_data['lang'] == new_lang


class TestLanguageToggleErrorHandling:
    """Test error handling in language toggle"""
    
    @pytest.mark.asyncio
    async def test_missing_callback_query(self, mock_context):
        """Test handling when callback query is missing"""
        update = Mock(spec=Update)
        update.callback_query = None
        
        # Should not crash
        try:
            await language_toggle_callback(update, mock_context)
        except AttributeError:
            # Expected - callback query is None
            pass
    
    @pytest.mark.asyncio
    async def test_corrupted_user_data(self, mock_update):
        """Test handling when user_data is corrupted"""
        context = Mock(spec=ContextTypes.DEFAULT_TYPE)
        context.user_data = None
        
        # Should not crash, but may not work correctly
        # The get_lang function should handle this gracefully
        try:
            await language_toggle_callback(mock_update, context)
        except (AttributeError, TypeError):
            # Expected when user_data is None
            pass
    
    def test_get_lang_with_none_user_data(self):
        """Test get_lang handles None user_data gracefully"""
        try:
            result = get_lang(None)
            # Should fall back to default
            assert result == 'kh'
        except (AttributeError, TypeError):
            # Also acceptable - calling code should handle this
            pass


class TestLanguageToggleIntegration:
    """Test language toggle integration with UI components"""
    
    @pytest.mark.asyncio
    async def test_home_screen_language_update(self, mock_update, mock_context):
        """Test that home screen updates correctly after language toggle"""
        # Start with English
        mock_context.user_data['language'] = 'en'
        mock_context.user_data['lang'] = 'en'
        
        await language_toggle_callback(mock_update, mock_context)
        
        # Verify the edit_message_text was called with proper parameters
        mock_update.callback_query.edit_message_text.assert_called_once()
        call_args = mock_update.callback_query.edit_message_text.call_args
        
        # Should have text, reply_markup, and parse_mode
        assert 'text' in call_args.kwargs
        assert 'reply_markup' in call_args.kwargs
        assert 'parse_mode' in call_args.kwargs
        
        # Reply markup should be InlineKeyboardMarkup
        assert isinstance(call_args.kwargs['reply_markup'], InlineKeyboardMarkup)
    
    def test_language_consistency_check(self):
        """Test that language values are consistent across the system"""
        valid_languages = ['en', 'kh']
        
        # Test that get_lang only returns valid languages
        for lang in valid_languages:
            user_data = {'language': lang}
            result = get_lang(user_data)
            assert result in valid_languages
        
        # Test default fallback
        user_data = {}
        result = get_lang(user_data)
        assert result in valid_languages
