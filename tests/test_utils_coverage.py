"""
Utils Coverage Tests
Tests to improve coverage for utils.py
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parents[1]))

from src.core.utils import to_int, safe_float


def test_to_int_with_int():
    """Test to_int with integer input"""
    assert to_int(5) == 5
    assert to_int(0) == 0
    assert to_int(-1) == -1


def test_to_int_with_string_numbers():
    """Test to_int with string numbers"""
    assert to_int("5") == 5
    assert to_int("0") == 0
    assert to_int("-1") == -1


def test_to_int_with_budget_strings():
    """Test to_int with budget mapping strings"""
    assert to_int("low") == 0
    assert to_int("LOW") == 0
    assert to_int("mid") == 1
    assert to_int("MID") == 1
    assert to_int("high") == 2
    assert to_int("HIGH") == 2


def test_to_int_with_invalid_string():
    """Test to_int with invalid string"""
    assert to_int("invalid") == 0
    assert to_int("abc") == 0
    assert to_int("") == 0


def test_to_int_with_other_types():
    """Test to_int with other types"""
    assert to_int(None) == 0
    assert to_int([]) == 0
    assert to_int({}) == 0
    assert to_int(3.14) == 0


def test_to_int_with_custom_default():
    """Test to_int with custom default"""
    assert to_int("invalid", default=99) == 99
    assert to_int(None, default=-1) == -1


def test_safe_float_with_numbers():
    """Test safe_float with numeric inputs"""
    assert safe_float(5) == 5.0
    assert safe_float(3.14) == 3.14
    assert safe_float(0) == 0.0


def test_safe_float_with_string_numbers():
    """Test safe_float with string numbers"""
    assert safe_float("5") == 5.0
    assert safe_float("3.14") == 3.14
    assert safe_float("0") == 0.0


def test_safe_float_with_invalid_string():
    """Test safe_float with invalid string"""
    assert safe_float("invalid") == 0.0
    assert safe_float("abc") == 0.0
    assert safe_float("") == 0.0


def test_safe_float_with_other_types():
    """Test safe_float with other types"""
    assert safe_float(None) == 0.0
    assert safe_float([]) == 0.0
    assert safe_float({}) == 0.0


def test_safe_float_with_custom_default():
    """Test safe_float with custom default"""
    assert safe_float("invalid", default=99.9) == 99.9
    assert safe_float(None, default=-1.5) == -1.5
