#!/usr/bin/env python3
"""
EduGuideBot Production Deployment Script
Comprehensive deployment with real Telegram API testing
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path
from telegram import Bot, Update
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import bot modules
from src.bot.handlers import *
from src.bot.commands import start_command, help_command, settings_command
from src.bot.telegram_safe import validate_callback_patterns_on_startup
from src.core.data_loader import load_raw

# Import callback router
try:
    from src.bot.handlers import callback_router
except ImportError:
    # If callback_router doesn't exist, create a simple one
    async def callback_router(update, context):
        """Simple callback router for production"""
        query = update.callback_query
        callback_data = query.data

        # Route to appropriate handler based on callback data
        if callback_data == "START_QUIZ":
            await start_quiz_callback(update, context)
        elif callback_data == "HOME":
            await home_screen_callback(update, context)
        elif callback_data.startswith("QS_"):
            await quick_start_callback(update, context)
        elif callback_data.startswith("DET_"):
            await detail_callback(update, context)
        elif callback_data.startswith("CMP_"):
            await compare_callback(update, context)
        elif callback_data.startswith("SAVE_"):
            await save_callback(update, context)
        elif callback_data.startswith("REMOVE_"):
            await remove_callback(update, context)
        elif callback_data.startswith("answer_"):
            await handle_question_answer(update, context)
        elif callback_data == "BACK":
            await back_callback(update, context)
        elif callback_data == "REFRESH":
            await refresh_callback(update, context)
        elif callback_data == "RESTART":
            await restart_callback(update, context)
        elif callback_data.startswith("FB_"):
            await feedback_callback(update, context)
        elif callback_data == "BROWSE_MAJORS":
            await browse_majors_callback(update, context)
        elif callback_data == "SHORTLIST_VIEW":
            await shortlist_callback(update, context)
        elif callback_data == "HELP_INFO":
            await help_callback(update, context)
        elif callback_data == "LANG_TOGGLE":
            await language_toggle_callback(update, context)
        else:
            await unknown_callback(update, context)

# Add missing handlers
async def error_handler(update, context):
    """Handle errors"""
    logger.error(f"Update {update} caused error {context.error}")

async def handle_text_message(update, context):
    """Handle text messages"""
    # For now, just respond with help
    await update.message.reply_text("Please use the buttons to interact with the bot, or type /start to begin.")

# Configure logging for production
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_deployment.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ProductionDeployment:
    """Handles comprehensive production deployment and testing"""
    
    def __init__(self):
        self.bot_token = os.getenv('BOT_TOKEN')
        self.application = None
        self.bot = None
        self.test_results = []
        self.errors = []
        
    async def validate_environment(self):
        """Validate production environment setup"""
        logger.info("🔍 VALIDATING PRODUCTION ENVIRONMENT")
        
        # Check bot token
        if not self.bot_token:
            raise ValueError("BOT_TOKEN not found in environment variables")
        
        logger.info(f"✅ Bot token configured: {self.bot_token[:10]}...")
        
        # Check data files
        try:
            majors_data = load_raw()
            logger.info(f"✅ Data loaded: {len(majors_data)} majors from universities")
        except Exception as e:
            logger.error(f"❌ Data loading failed: {e}")
            raise
        
        # Validate callback patterns
        try:
            validate_callback_patterns_on_startup()
            logger.info("✅ All callback patterns validated")
        except Exception as e:
            logger.error(f"❌ Callback pattern validation failed: {e}")
            raise
        
        logger.info("🎉 Environment validation complete")
        return True

    async def test_telegram_api_connectivity(self):
        """Test basic Telegram API connectivity"""
        logger.info("🔗 TESTING TELEGRAM API CONNECTIVITY")
        
        try:
            # Create bot instance
            self.bot = Bot(token=self.bot_token)
            
            # Test basic API call
            bot_info = await self.bot.get_me()
            logger.info(f"✅ Bot connected: @{bot_info.username} ({bot_info.first_name})")
            
            # Test webhook info
            webhook_info = await self.bot.get_webhook_info()
            logger.info(f"✅ Webhook status: {webhook_info.url or 'Not set (polling mode)'}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Telegram API connectivity failed: {e}")
            self.errors.append(f"API Connectivity: {e}")
            return False

    def setup_handlers(self):
        """Set up all bot handlers for production"""
        logger.info("⚙️ SETTING UP BOT HANDLERS")
        
        # Create application
        self.application = Application.builder().token(self.bot_token).build()
        
        # Add command handlers
        self.application.add_handler(CommandHandler("start", start_command))
        self.application.add_handler(CommandHandler("help", help_command))
        self.application.add_handler(CommandHandler("settings", settings_command))
        
        # Add callback query handler for all interactive buttons
        self.application.add_handler(CallbackQueryHandler(callback_router))
        
        # Add message handler for text messages
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text_message))
        
        # Add error handler
        self.application.add_error_handler(error_handler)
        
        logger.info("✅ All handlers registered successfully")

    async def test_start_command(self):
        """Test the /start command functionality"""
        logger.info("🏠 TESTING /START COMMAND")
        
        try:
            # Create mock update for testing
            from unittest.mock import MagicMock, AsyncMock
            
            mock_update = MagicMock()
            mock_context = MagicMock()
            
            # Mock message
            mock_update.message = AsyncMock()
            mock_update.message.reply_text = AsyncMock()
            mock_update.message.chat_id = 12345
            mock_update.message.from_user.id = 12345
            mock_update.message.from_user.language_code = 'en'
            
            # Mock context
            mock_context.user_data = {}
            mock_context.bot = self.bot
            
            # Test start command
            await start_command(mock_update, mock_context)
            
            # Verify reply_text was called
            mock_update.message.reply_text.assert_called()
            
            logger.info("✅ /start command test passed")
            self.test_results.append("Start Command: PASSED")
            return True
            
        except Exception as e:
            logger.error(f"❌ /start command test failed: {e}")
            self.errors.append(f"Start Command: {e}")
            return False

    async def test_callback_patterns(self):
        """Test all callback patterns with real API"""
        logger.info("🔘 TESTING CALLBACK PATTERNS")
        
        test_patterns = [
            "START_QUIZ", "HOME", "QS_SURPRISE", "QS_PP", 
            "BROWSE_MAJORS", "SHORTLIST_VIEW", "HELP_INFO", "LANG_TOGGLE",
            "DET_test_major", "CMP_test_major", "SAVE_test_major",
            "BACK", "REFRESH", "RESTART", "FB_UP", "FB_DOWN"
        ]
        
        passed_patterns = 0
        
        for pattern in test_patterns:
            try:
                # Test pattern validation
                from src.bot.telegram_safe import validate_callback_pattern
                is_valid = validate_callback_pattern(pattern)
                
                if is_valid:
                    passed_patterns += 1
                    logger.info(f"  ✅ Pattern '{pattern}' validated")
                else:
                    logger.warning(f"  ⚠️ Pattern '{pattern}' not recognized")
                    
            except Exception as e:
                logger.error(f"  ❌ Pattern '{pattern}' failed: {e}")
                self.errors.append(f"Pattern {pattern}: {e}")
        
        success_rate = (passed_patterns / len(test_patterns)) * 100
        logger.info(f"✅ Callback patterns test: {passed_patterns}/{len(test_patterns)} ({success_rate:.1f}%)")
        
        self.test_results.append(f"Callback Patterns: {passed_patterns}/{len(test_patterns)} PASSED")
        return success_rate >= 90

    async def run_production_tests(self):
        """Run comprehensive production tests"""
        logger.info("🧪 RUNNING COMPREHENSIVE PRODUCTION TESTS")
        
        test_functions = [
            ("Environment Validation", self.validate_environment),
            ("Telegram API Connectivity", self.test_telegram_api_connectivity),
            ("Start Command", self.test_start_command),
            ("Callback Patterns", self.test_callback_patterns),
        ]
        
        passed_tests = 0
        total_tests = len(test_functions)
        
        for test_name, test_func in test_functions:
            logger.info(f"\n--- Running {test_name} ---")
            try:
                result = await test_func()
                if result:
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: EXCEPTION - {e}")
                self.errors.append(f"{test_name}: {e}")
        
        success_rate = (passed_tests / total_tests) * 100
        logger.info(f"\n🎯 PRODUCTION TESTS SUMMARY: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        return success_rate >= 90

    async def start_production_bot(self):
        """Start the bot in production mode"""
        logger.info("🚀 STARTING PRODUCTION BOT")
        
        try:
            # Set up handlers
            self.setup_handlers()
            
            # Start polling
            logger.info("🔄 Starting bot polling...")
            await self.application.initialize()
            await self.application.start()
            
            # Start polling
            await self.application.updater.start_polling(
                poll_interval=1.0,
                timeout=10,
                read_timeout=10,
                write_timeout=10,
                connect_timeout=10
            )
            
            logger.info("🎉 BOT IS NOW LIVE AND RUNNING!")
            logger.info("✅ Ready to receive messages and button clicks")
            logger.info("🔗 Users can now interact with the bot on Telegram")
            
            # Keep the bot running
            import signal
            import asyncio

            # Set up signal handlers for graceful shutdown
            stop_signals = (signal.SIGTERM, signal.SIGINT)
            for sig in stop_signals:
                signal.signal(sig, lambda s, f: asyncio.create_task(self.application.stop()))

            # Keep running until interrupted
            try:
                while True:
                    await asyncio.sleep(1)
            except (KeyboardInterrupt, SystemExit):
                logger.info("🛑 Received shutdown signal")
            
        except Exception as e:
            logger.error(f"❌ Failed to start production bot: {e}")
            raise
        finally:
            # Cleanup
            if self.application:
                await self.application.stop()
                await self.application.shutdown()

    def generate_deployment_report(self):
        """Generate comprehensive deployment report"""
        print("\n" + "=" * 80)
        print("📊 PRODUCTION DEPLOYMENT REPORT")
        print("=" * 80)
        
        print(f"🤖 Bot Token: {self.bot_token[:10]}...{self.bot_token[-5:]}")
        print(f"✅ Tests Passed: {len(self.test_results)}")
        print(f"❌ Errors Found: {len(self.errors)}")
        
        if self.test_results:
            print("\n✅ SUCCESSFUL TESTS:")
            for result in self.test_results:
                print(f"   • {result}")
        
        if self.errors:
            print("\n❌ ERRORS ENCOUNTERED:")
            for error in self.errors:
                print(f"   • {error}")
        
        if not self.errors:
            print("\n🎉 DEPLOYMENT SUCCESSFUL!")
            print("✅ EduGuideBot is ready for production use")
            print("✅ All systems operational")
            print("✅ Ready for real user interactions")
        else:
            print("\n⚠️ DEPLOYMENT ISSUES DETECTED")
            print("🔧 Review and fix errors before full deployment")


async def main():
    """Main deployment function"""
    print("🚀 EDUGUIDEBOT PRODUCTION DEPLOYMENT")
    print("=" * 80)
    print("Phase 1: Test Environment Setup")
    print("Phase 2: Real API Testing")
    print("Phase 3: Production Deployment")
    print("=" * 80)
    
    deployment = ProductionDeployment()
    
    try:
        # Run production tests
        tests_passed = await deployment.run_production_tests()
        
        # Generate report
        deployment.generate_deployment_report()
        
        if tests_passed:
            print("\n🎯 PROCEEDING TO LIVE DEPLOYMENT")
            print("Press Ctrl+C to stop the bot")
            
            # Start production bot
            await deployment.start_production_bot()
        else:
            print("\n🛑 DEPLOYMENT HALTED DUE TO TEST FAILURES")
            return 1
            
    except KeyboardInterrupt:
        print("\n🛑 Deployment stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Deployment failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
