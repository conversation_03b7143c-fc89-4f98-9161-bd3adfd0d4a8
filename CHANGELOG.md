# Changelog

All notable changes to EduGuideBot will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2025-01-15

### 🎉 Major Release: Hybrid Scoring System

This release introduces a revolutionary hybrid scoring system that combines Multi-Criteria Decision Analysis (MCDA) with Machine Learning for more accurate and personalized university recommendations.

### ✨ Added

#### Core Features
- **Hybrid Scoring System**: 70% MCDA + 30% ML blend for optimal recommendations
- **Interactive Recommendation Cards**: Inline buttons for detailed explanations and navigation
- **Advanced Filtering System**: User-configurable filters (low-cost only, Phnom Penh only)
- **Enhanced Bilingual Support**: Seamless language switching with improved Khmer localization
- **Vectorized MCDA Scoring**: Performance-optimized scoring engine
- **ML Model Versioning**: Support for multiple ML model versions with automatic fallback

#### User Experience
- **Inline Buttons**: "Back to list" and "Why this recommendation?" buttons on each recommendation card
- **Explanation Engine**: Detailed score breakdowns showing MCDA and ML components
- **Settings Panel**: Enhanced `/settings` command with filter configuration
- **Dynamic Language Switching**: Real-time language changes without restart

#### Technical Infrastructure
- **Comprehensive Test Suite**: 171 tests covering all major functionality
- **CI/CD Pipeline**: GitHub Actions with automated testing, linting, and Docker builds
- **Performance Monitoring**: Enhanced metrics collection and logging
- **Error Handling**: Robust fallback mechanisms and user-friendly error messages

### 🔧 Improved

#### Architecture
- **Modular Design**: Clean separation between bot layer, core logic, and infrastructure
- **Utility Functions**: Shared utilities for code reuse and maintainability
- **Handler Registration**: Idempotent handler registration with duplicate guards
- **Memory Management**: Improved caching and memory leak prevention

#### Data Pipeline
- **Feature Engineering**: Enhanced feature extraction for ML models
- **Data Validation**: Comprehensive data quality checks and auto-patching
- **Cold-Start Handling**: Graceful handling of incomplete user data

#### Performance
- **Vectorized Operations**: NumPy-based vectorized scoring for better performance
- **Caching Strategy**: Intelligent caching of recommendations and user data
- **Async Operations**: Full async/await support for non-blocking operations

### 🐛 Fixed

#### Bot Functionality
- **Command Parsing**: Improved `/details` command with better error handling
- **Conversation Flow**: Fixed state management in conversation handlers
- **Language Persistence**: Proper language preference storage and retrieval

#### Data Handling
- **JSON Parsing**: Robust handling of malformed data files
- **Score Calculation**: Fixed edge cases in MCDA scoring
- **Cache Management**: Resolved cache invalidation issues

### 📊 Performance Metrics

- **Test Coverage**: 171 tests with comprehensive coverage of core functionality
- **ML Model Accuracy**: 99.96% accuracy with RandomForest algorithm
- **Response Time**: Optimized vectorized scoring for faster recommendations
- **Memory Usage**: Improved memory management with proper cleanup

### 🔄 Migration Guide

#### From v1.x to v2.0

1. **Environment Variables**:
   ```bash
   # New optional variable for ML model version
   export ML_VERSION="3"  # Options: 1, 2, 3
   ```

2. **API Changes**:
   - `/details` command now supports inline buttons
   - `/settings` command includes filter configuration
   - Recommendation cards include interactive elements

3. **Data Format**:
   - Recommendations now include `hybrid_score`, `mcda_score`, and `ml_score`
   - Enhanced program data with derived features

4. **Testing**:
   ```bash
   # Install development dependencies
   pip install -r requirements-dev.txt
   
   # Run full test suite
   pytest
   
   # Generate coverage report
   pytest --cov=src --cov-report=html
   ```

### 🚀 Deployment

#### Docker
```bash
# Build and run v2.0
docker build -t eduguidebot:v2.0 .
docker run -e BOT_TOKEN=$BOT_TOKEN eduguidebot:v2.0
```

#### CI/CD
- Automated testing on pull requests
- Docker image builds and pushes to GitHub Container Registry
- Code quality checks (black, flake8)
- Security scanning with Trivy

### 📈 Statistics

- **Lines of Code**: ~1,446 lines in src/
- **Test Files**: 17 test modules
- **Test Cases**: 171 individual tests
- **Universities**: 47 institutions covered
- **Study Programs**: 500+ programs in database
- **Languages**: Full bilingual support (Khmer/English)

### 🙏 Acknowledgments

- Enhanced ML models based on student outcome data
- Improved Khmer language support with cultural context
- Community feedback integration for better user experience

---

## [1.0.0] - 2024-12-01

### Initial Release

#### Features
- Basic MCDA-only recommendation system
- 16-question assessment
- Simple command interface
- Basic bilingual support (Khmer/English)
- 47 universities and 500+ programs database

#### Architecture
- Simple bot layer with basic handlers
- MCDA scoring engine
- JSON-based data storage
- Basic error handling

---

## Legend

- 🎉 Major release
- ✨ New features
- 🔧 Improvements
- 🐛 Bug fixes
- 📊 Performance
- 🔄 Breaking changes
- 🚀 Deployment
- 📈 Statistics
- 🙏 Acknowledgments
